import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  Typography,
  Button,
  CircularProgress,
  Box,
  Autocomplete,
  TextField,
  Grid,
  Checkbox
} from '@mui/material'
import { useState, useMemo, useEffect, useCallback } from 'react'
import {
  useReactTable,
  getCoreRowModel,
  getPaginationRowModel,
  createColumnHelper,
  flexRender
} from '@tanstack/react-table'

import DebouncedInput from '@/components/DebounceInput'
import DateRangePicker from '@/components/DateRangePicker'
import { PurchaseInvoice, PurchaseInvoiceParams, PurchaseInvoiceStatus } from '@/types/purchaseInvoiceTypes'
import { useQuery } from '@tanstack/react-query'
import PurchaseInvoiceQueryMethods, { PURCHASE_INVOICE_LIST_QUERY_KEY } from '@/api/services/purchase-invoice/query'
import { ListResponse } from '@/types/api'
import { defaultListData } from '@/api/queryClient'
import { tableColumns as originalTableColumns } from '@/pages/cash-bank/purchase-invoice/list/config/table'
import CompanyQueryMethods, { VENDOR_LIST_QUERY_KEY } from '@/api/services/company/query'
import { VendorType } from '@/types/companyTypes'
import Table from '@/components/table'
import usePartialState from '@/core/hooks/usePartialState'

import debounce from '@mui/material/utils/debounce'
type AddPurchaseInvoiceDialogProps = {
  open: boolean
  setOpen: (open: boolean) => void
  onSubmit?: (invoiceList?: PurchaseInvoice[]) => void
  selectedInvoices?: PurchaseInvoice[]
}

const columnHelper = createColumnHelper<PurchaseInvoice>()

const AddPurchaseInvoiceDialog = ({
  open,
  setOpen,
  onSubmit,
  selectedInvoices: initialSelectedInvoices
}: AddPurchaseInvoiceDialogProps) => {
  const [rowSelection, setRowSelection] = useState<Record<string, boolean>>({})
  const [selectedInvoices, setSelectedInvoices] = useState<PurchaseInvoice[]>([])

  const [params, setParams] = usePartialState<PurchaseInvoiceParams>({
    limit: 10,
    page: 1,
    search: '',
    startDate: undefined,
    endDate: undefined
  })

  const [vendorSearchQuery, setVendorSearchQuery] = useState('')
  const [selectedVendor, setSelectedVendor] = useState<VendorType | null>(null)

  const { data: vendorListResponse = defaultListData as ListResponse<VendorType>, isFetching: fetchVendorsLoading } =
    useQuery({
      queryKey: [VENDOR_LIST_QUERY_KEY, vendorSearchQuery],
      queryFn: () => CompanyQueryMethods.getVendorList({ search: vendorSearchQuery, limit: 30 }),
      placeholderData: defaultListData as ListResponse<VendorType>
    })
  const vendorList = vendorListResponse.items || []

  const handleVendorInputChange = useCallback(
    debounce((_event: any, newValue: string, reason: string) => {
      if (reason === 'input') {
        setVendorSearchQuery(newValue)
      }
    }, 700),
    []
  )

  const {
    data: purchaseInvoiceListResponse,
    refetch: fetchPurchaseInvoiceList,
    isLoading
  } = useQuery({
    queryKey: [PURCHASE_INVOICE_LIST_QUERY_KEY, params, 'upcoming-payment'],
    queryFn: () =>
      PurchaseInvoiceQueryMethods.getPaginatedPurchaseInvoices({
        ...params,
        status: PurchaseInvoiceStatus.APPROVED
      }),
    placeholderData: defaultListData as ListResponse<PurchaseInvoice>
  })

  const { items: purchaseInvoiceList, totalItems, totalPages } = purchaseInvoiceListResponse

  const tableColumns = useMemo(() => {
    const columns = originalTableColumns({ showDetail: () => {} }).filter(col => col.header !== 'ACTION')

    return [
      columnHelper.display({
        id: 'select',
        header: ({ table }) => <></>,
        cell: ({ row }) => (
          <Checkbox
            checked={row.getIsSelected()}
            disabled={!row.getCanSelect()}
            onChange={row.getToggleSelectedHandler()}
          />
        )
      }),
      ...columns
    ]
  }, [])

  const table = useReactTable({
    data: purchaseInvoiceList,
    columns: tableColumns,
    state: {
      rowSelection,
      pagination: {
        pageIndex: params.page! - 1,
        pageSize: params.limit!
      }
    },
    manualPagination: true,
    rowCount: totalItems,
    pageCount: totalPages,
    onRowSelectionChange: setRowSelection,
    getRowId: row => row.id,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    enableRowSelection: row => {
      if (selectedInvoices.length > 0) {
        return row.original.vendorId === selectedInvoices[0].vendorId
      }
      return true
    }
  })

  useEffect(() => {
    if (initialSelectedInvoices) {
      const initialSelection: Record<string, boolean> = {}
      initialSelectedInvoices.forEach(invoice => {
        initialSelection[invoice.id] = true
      })
      setRowSelection(initialSelection)
      setSelectedInvoices(initialSelectedInvoices)
    }
  }, [initialSelectedInvoices])

  useEffect(() => {
    const selectedData = Object.keys(rowSelection)
      .filter(id => rowSelection[id])
      .map(id => purchaseInvoiceList.find(invoice => invoice.id === id))
      .filter((invoice): invoice is PurchaseInvoice => !!invoice)

    const newSelectedInvoices = [...selectedInvoices]
    purchaseInvoiceList.forEach(invoice => {
      const index = newSelectedInvoices.findIndex(selected => selected.id === invoice.id)
      if (rowSelection[invoice.id]) {
        if (index === -1) {
          newSelectedInvoices.push(invoice)
        }
      } else {
        if (index !== -1) {
          newSelectedInvoices.splice(index, 1)
        }
      }
    })

    setSelectedInvoices(newSelectedInvoices)
  }, [rowSelection, purchaseInvoiceList])

  const handleCancel = () => {
    setOpen(false)
  }

  const handleSubmit = () => {
    onSubmit?.(selectedInvoices)
    setOpen(false)
  }

  return (
    <Dialog open={open} maxWidth='lg' onClose={() => setOpen(false)} fullWidth>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
        Tambah Faktur Pembelian
        <Typography variant='body2' color='textSecondary'>
          Pilih faktur pembelian yang akan dimasukkan ke pembayaran
        </Typography>
      </DialogTitle>
      <DialogContent className='pbs-0 sm:pbe-16 sm:px-12 !py-4'>
        <IconButton onClick={handleCancel} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>

        <Grid container spacing={4} className='mt-4 mb-4'>
          <Grid item xs={12} md={4}>
            <DebouncedInput
              value={params.search}
              onChange={value => setParams('search', value as string)}
              placeholder={`Cari Nomor Faktur...`}
              className='w-full'
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <DateRangePicker
              inputSize='small'
              startDate={params.startDate}
              endDate={params.endDate}
              onChange={(start, end) => {
                setParams('startDate', start)
                setParams('endDate', end)
              }}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <Autocomplete
              fullWidth
              filterOptions={x => x}
              isOptionEqualToValue={(option, value) => option.id === value.id}
              onInputChange={handleVendorInputChange}
              options={vendorList}
              onChange={(_e, newValue: VendorType | null) => {
                setSelectedVendor(newValue)
                setParams('vendorId', newValue?.id)
              }}
              value={selectedVendor}
              noOptionsText='Vendor tidak ditemukan'
              loading={fetchVendorsLoading}
              renderInput={params => (
                <TextField
                  {...params}
                  label='Vendor'
                  placeholder='Cari Vendor'
                  variant='outlined'
                  size='small'
                  InputProps={{
                    ...params.InputProps,
                    endAdornment: (
                      <>
                        {fetchVendorsLoading ? <CircularProgress size={20} /> : null}
                        {params.InputProps.endAdornment}
                      </>
                    )
                  }}
                />
              )}
              getOptionLabel={(option: VendorType) => option?.name || ''}
            />
          </Grid>
        </Grid>

        {isLoading ? (
          <Box display='flex' justifyContent='center' alignItems='center' minHeight='200px'>
            <CircularProgress />
          </Box>
        ) : (
          <div className='mt-4'>
            <Table
              table={table}
              onPageChange={page => setParams('page', page)}
              onRowsPerPageChange={limit => setParams('limit', limit)}
              emptyLabel={
                <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
                  <Typography>Belum ada Faktur Pembelian</Typography>
                </td>
              }
            />
          </div>
        )}
      </DialogContent>
      <DialogActions className='gap-2 sm:gap-6 max-sm:flex-col max-sm:items-center justify-center pbs-0 sm:pbe-10 sm:px-16'>
        <Button variant='outlined' color='secondary' onClick={handleCancel} className='is-full sm:is-auto'>
          BATALKAN
        </Button>
        <Button
          variant='contained'
          onClick={handleSubmit}
          disabled={selectedInvoices.length === 0}
          className='is-full sm:is-auto'
        >
          TAMBAHKAN
        </Button>
      </DialogActions>
    </Dialog>
  )
}

export default AddPurchaseInvoiceDialog
