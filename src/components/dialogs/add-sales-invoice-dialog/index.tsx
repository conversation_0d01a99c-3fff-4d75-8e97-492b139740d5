import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  Typography,
  Table,
  TableHead,
  TableBody,
  TableRow,
  Button,
  CircularProgress,
  Box,
  Checkbox,
  Autocomplete,
  TextField
} from '@mui/material'
import { useEffect, useMemo, useState, useCallback } from 'react'
import debounce from '@mui/material/utils/debounce'

import DebouncedInput from '@/components/DebounceInput'
import DateRangePicker from '@/components/DateRangePicker'
import { SalesInvoice, SalesInvoiceStatuses } from '@/types/salesInvoiceTypes'
import { useQuery } from '@tanstack/react-query'
import SalesInvoiceQueryMethods, { SALES_INVOICE_LIST_QUERY_KEY } from '@/api/services/sales-invoice/query'
import { ListResponse } from '@/types/api'
import { defaultListData } from '@/api/queryClient'
import CompanyQueryMethods, { CUSTOMER_LIST_QUERY_KEY } from '@/api/services/company/query'
import { CustomerType } from '@/types/customerTypes'
import {
  createColumnHelper,
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import { tableColumns } from '@/pages/cash-bank/sales-invoice/list/config/table'
import CustomTable from '@/components/table'
import { isNullOrUndefined } from '@/utils/helper'

const columnHelper = createColumnHelper<SalesInvoice>()

type AddSalesInvoiceDialogProps = {
  open: boolean
  setOpen: (open: boolean) => void
  onSubmit?: (invoiceList?: SalesInvoice[]) => void
  selectedInvoices?: SalesInvoice[]
}

const AddSalesInvoiceDialog = ({
  open,
  setOpen,
  onSubmit,
  selectedInvoices: initialSelectedInvoices
}: AddSalesInvoiceDialogProps) => {
  const [rowSelection, setRowSelection] = useState<Record<string, boolean>>({})
  const [selectedInvoices, setSelectedInvoices] = useState<SalesInvoice[]>([])
  const [searchQuery, setSearchQuery] = useState<string>('')
  const [customerSearchQuery, setCustomerSearchQuery] = useState('')
  const [selectedCustomer, setSelectedCustomer] = useState<CustomerType | null>(null)
  const [dateRange, setDateRange] = useState<string[]>([])
  const [params, setParams] = useState({
    page: 1,
    limit: 10,
    search: '',
    startDate: undefined,
    endDate: undefined,
    customerId: undefined as string | undefined
  })

  const {
    data: customerListResponse = defaultListData as ListResponse<CustomerType>,
    isFetching: fetchCustomerLoading
  } = useQuery({
    queryKey: [CUSTOMER_LIST_QUERY_KEY, customerSearchQuery],
    queryFn: () => {
      return CompanyQueryMethods.getCustomerList({
        ...(customerSearchQuery && { search: customerSearchQuery }),
        limit: 30
      })
    },
    placeholderData: defaultListData as ListResponse<CustomerType>
  })

  const customerList = customerListResponse.items || []

  const handleCustomerInputChange = useCallback(
    debounce((_event: any, newValue: string, reason: string) => {
      if (reason === 'input') {
        setCustomerSearchQuery(newValue)
      }
    }, 700),
    []
  )

  const {
    data: salesInvoiceListResponse,
    refetch: fetchSalesInvoiceList,
    isLoading
  } = useQuery({
    queryKey: [SALES_INVOICE_LIST_QUERY_KEY, params],
    queryFn: () =>
      SalesInvoiceQueryMethods.getSalesInvoiceList({
        ...params,
        status: SalesInvoiceStatuses.APPROVED
      }),
    placeholderData: defaultListData as ListResponse<SalesInvoice>
  })

  const { items: salesInvoiceList, totalItems, totalPages, page, limit } = salesInvoiceListResponse

  const handleCancel = () => {
    setOpen(false)
  }

  const columns = useMemo(() => {
    const columns = tableColumns({ showDetail: () => {} }, undefined).filter(
      col => !['action', 'invoiceStatus'].includes(col.id as string)
    )

    return [
      columnHelper.display({
        id: 'select',
        header: () => <></>,
        cell: ({ row }) => (
          <Checkbox
            checked={row.getIsSelected()}
            disabled={!row.getCanSelect()}
            onChange={row.getToggleSelectedHandler()}
          />
        )
      }),
      ...columns
    ]
  }, [])

  const table = useReactTable({
    data: salesInvoiceList,
    columns,
    state: {
      rowSelection,
      pagination: {
        pageSize: limit,
        pageIndex: page - 1
      }
    },
    initialState: {
      pagination: {
        pageSize: params.limit,
        pageIndex: params.page - 1
      }
    },
    manualPagination: true,
    rowCount: totalItems,
    pageCount: totalPages,
    onRowSelectionChange: setRowSelection,
    getRowId: row => row.id,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getFacetedMinMaxValues: getFacetedMinMaxValues(),
    enableRowSelection: row => {
      if (selectedInvoices.length > 0) {
        return row.original.customerId === selectedInvoices[0].customerId
      }
      return true
    }
  })

  useEffect(() => {
    if (initialSelectedInvoices) {
      const initialSelection: Record<string, boolean> = {}
      initialSelectedInvoices.forEach(invoice => {
        initialSelection[invoice.id] = true
      })
      setRowSelection(initialSelection)
      setSelectedInvoices(initialSelectedInvoices)
    }
  }, [initialSelectedInvoices])

  useEffect(() => {
    const newSelectedInvoices = [...selectedInvoices]
    salesInvoiceList.forEach(invoice => {
      const index = newSelectedInvoices.findIndex(selected => selected.id === invoice.id)
      if (rowSelection[invoice.id]) {
        if (index === -1) {
          newSelectedInvoices.push(invoice)
        }
      } else {
        if (index !== -1) {
          newSelectedInvoices.splice(index, 1)
        }
      }
    })

    setSelectedInvoices(newSelectedInvoices)
  }, [rowSelection, salesInvoiceList])

  useEffect(() => {
    setParams(prev => ({
      ...prev,
      search: searchQuery,
      startDate: dateRange[0],
      endDate: dateRange[1],
      customerId: selectedCustomer?.id
    }))
  }, [searchQuery, dateRange, selectedCustomer])

  return (
    <Dialog open={open} maxWidth='lg' onClose={() => setOpen(false)} fullWidth>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
        Tambah Faktur Penjualan
        <Typography variant='body2' color='textSecondary'>
          Pilih faktur penjualan yang akan dimasukkan ke penerimaan
        </Typography>
      </DialogTitle>
      <DialogContent className='pbs-0 sm:pbe-16 sm:px-12 !py-4'>
        <IconButton onClick={handleCancel} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>

        <div className='grid grid-cols-3 gap-4 mt-4 mb-4'>
          <DebouncedInput
            value={searchQuery}
            onChange={value => setSearchQuery(value as string)}
            placeholder={`Cari Nomor Faktur...`}
            className='w-full'
          />
          <Autocomplete
            filterOptions={x => x}
            isOptionEqualToValue={(option, value) => option.id === value.id}
            onInputChange={handleCustomerInputChange}
            options={customerList}
            freeSolo
            onChange={(_e, newValue: CustomerType | null) => {
              if (newValue) {
                setSelectedCustomer(newValue)
              } else {
                setSelectedCustomer(null)
              }
            }}
            value={selectedCustomer}
            noOptionsText='Customer tidak ditemukan'
            loading={fetchCustomerLoading}
            renderInput={params => (
              <TextField
                {...params}
                label='Customer'
                placeholder='Cari Customer'
                variant='outlined'
                size='small'
                InputProps={{
                  ...params.InputProps,
                  endAdornment: (
                    <>
                      {fetchCustomerLoading ? <CircularProgress size={20} /> : null}
                      {params.InputProps.endAdornment}
                    </>
                  )
                }}
              />
            )}
            getOptionLabel={(option: CustomerType) => option?.name || ''}
            renderOption={(props, option) => {
              const { key, ...optionProps } = props
              return (
                <li key={key} {...optionProps}>
                  <Typography>{option.name}</Typography>
                </li>
              )
            }}
          />
          <div>
            <DateRangePicker
              inputSize='small'
              startDate={dateRange[0]}
              endDate={dateRange[1]}
              onChange={(start, end) => {
                if (start) {
                  setDateRange([start, end])
                } else {
                  setDateRange([])
                }
              }}
            />
          </div>
        </div>

        {isLoading ? (
          <Box display='flex' justifyContent='center' alignItems='center' minHeight='200px'>
            <CircularProgress />
          </Box>
        ) : (
          <div className='mt-4'>
            <CustomTable
              table={table}
              emptyLabel={
                <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
                  <Typography>Belum ada Faktur Penjualan</Typography>
                  <Typography className='text-sm text-gray-400'>
                    Semua Faktur Penjualan yang telah dibuat akan ditampilkan di sini
                  </Typography>
                </td>
              }
              onRowsPerPageChange={pageSize => {
                setParams(prev => ({ ...prev, limit: pageSize, page: 1 }))
              }}
              onPageChange={pageIndex => {
                setParams(prev => ({ ...prev, page: pageIndex }))
              }}
            />
          </div>
        )}
      </DialogContent>
      <DialogActions className='gap-2 sm:gap-6 max-sm:flex-col max-sm:items-center justify-center pbs-0 sm:pbe-10 sm:px-16'>
        <Button variant='outlined' color='secondary' onClick={handleCancel} className='is-full sm:is-auto'>
          BATALKAN
        </Button>
        <Button
          variant='contained'
          onClick={() => {
            if (onSubmit) {
              onSubmit(selectedInvoices)
            }
          }}
          disabled={selectedInvoices.length === 0}
          className='is-full sm:is-auto'
        >
          TAMBAHKAN
        </Button>
      </DialogActions>
    </Dialog>
  )
}

export default AddSalesInvoiceDialog
