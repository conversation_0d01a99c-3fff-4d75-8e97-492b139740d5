import { useState, useMemo, useEffect } from 'react'
import { Card, IconButton, Typography, Button, Box } from '@mui/material'
import DebouncedInput from '@/components/DebounceInput'
import { usePartSwap } from '../../context/PartSwapContext'
import MobileDropDown from '@/components/layout/shared/components/MobileDropDown'
import { tableColumns } from '../config/table'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import Table from '@/components/table'
import FilterGroupDialog, { FilterGroupConfig, FilterValues } from '@/components/layout/shared/filter/FilterGroup'
import { useAuth } from '@/contexts/AuthContext'
import { partSwapStatuses } from '../config/util'

const PartSwapList = () => {
  const { departmentList, ownSiteList, userProfile } = useAuth()
  const {
    isMobile,
    setSelectedPartSwapId,
    partSwapList: { items, totalItems, totalPages },
    partSwapParams,
    setPartSwapParams,
    setPartialPartSwapParams,
    navigate
  } = usePartSwap()

  const { search, page, startDate, endDate, userStatus, siteIds, departmentId, priority } = partSwapParams

  const [searchExtend, setSearchExtend] = useState<boolean>(false)
  const [actionBtn, setBtn] = useState<boolean>(false)
  const [addAnchorEl, setAddAnchorEl] = useState<null | HTMLElement>(null)
  const open = Boolean(addAnchorEl)

  const [filterGroupConfig, setFilterGroupConfig] = useState<FilterGroupConfig>({})

  const actionButton = (
    <></>
    // <Button
    //   color='secondary'
    //   variant='outlined'
    //   startIcon={<i className='ri-upload-2-line' />}
    //   className='is-full sm:is-auto'
    // >
    //   Ekspor
    // </Button>
  )

  const tableOptions = useMemo(
    () => ({
      data: items ?? [],
      columns: tableColumns(
        {
          detail: item => {
            setSelectedPartSwapId(item.id)
            navigate('/part-swap/approval/' + item.id)
          }
        },
        userProfile?.id
      ),
      initialState: {
        pagination: {
          pageSize: 10,
          pageIndex: page - 1
        }
      },
      manualPagination: true,
      rowCount: totalItems,
      pageCount: totalPages,
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [items, totalItems, totalPages, page]
  )

  const table = useReactTable<any>(tableOptions)

  const onFilterChanged = ({ date, status, priority, site, department }: FilterValues) => {
    setPartSwapParams(prev => {
      return {
        ...prev,
        page: 1,
        startDate: date[0],
        endDate: date[1],
        userStatus: status.length > 0 ? status[0] : undefined,
        siteIds: site.length > 0 ? site[0] : undefined,
        departmentId: department.length > 0 ? department[0] : undefined
      }
    })
  }

  useEffect(() => {
    setFilterGroupConfig({
      date: {
        options: [],
        values: [startDate, endDate]
      },
      status: {
        options: partSwapStatuses,
        values: userStatus ? [userStatus] : []
      },
      site: {
        options: ownSiteList?.map(site => {
          return { value: site.id, label: site.name }
        }),
        values: siteIds ? [siteIds] : []
      },
      department: {
        options: departmentList?.map(department => ({ value: department.id, label: department.name })) ?? [],
        values: departmentId ? [departmentId] : []
      }
    })
  }, [partSwapParams])

  return (
    <>
      <Card>
        <div className='flex justify-between gap-4 p-5 flex-row items-start sm:flex-row sm:items-center'>
          {searchExtend ? (
            <div className='flex gap-4 items-center is-full flex-col sm:flex-row'>
              <DebouncedInput
                value={search}
                onChange={value => setPartSwapParams(prev => ({ ...prev, page: 1, search: value as string }))}
                onBlur={() => setSearchExtend(false)}
                placeholder='Cari'
                className='is-full'
              />
            </div>
          ) : !isMobile ? (
            <div className='flex gap-4 items-center is-full sm:is-auto flex-col sm:flex-row'>
              <DebouncedInput
                value={search}
                onChange={value => setPartSwapParams(prev => ({ ...prev, page: 1, search: value as string }))}
                placeholder='Cari'
                className='is-full sm:is-auto'
              />
              <FilterGroupDialog config={filterGroupConfig} onFilterApplied={onFilterChanged} />
            </div>
          ) : (
            <IconButton onClick={() => setSearchExtend(true)} className='bg-[#4C4E640D]/5 rounded-[8px]'>
              <i className='ri-search-line' />
            </IconButton>
          )}
          {!searchExtend && (
            <div className='flex items-center justify-end md:justify-between gap-x-4 max-sm:gap-y-4 is-full flex-row sm:is-auto'>
              {!isMobile ? (
                actionButton
              ) : (
                <IconButton onClick={() => setBtn(true)} className='bg-[#4C4E640D]/5 rounded-[8px]'>
                  <i className='pepicons-pop--dots-y' />
                </IconButton>
              )}
            </div>
          )}
        </div>
        <Table
          table={table}
          emptyLabel={
            <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
              <Typography> Belum ada Data</Typography>
              <Typography className='text-sm text-gray-400'>Semua data FR akan ditampilkan di sini</Typography>
            </td>
          }
          onRowsPerPageChange={pageSize => {
            if (pageSize > totalItems) {
              setPartSwapParams(prev => ({ ...prev, limit: totalItems, page: 1 }))
            } else {
              setPartialPartSwapParams('limit', pageSize)

              const maxPage = Math.ceil(totalItems / pageSize)
              if (page > maxPage) {
                setPartSwapParams(prev => ({ ...prev, page: maxPage }))
              }
            }
          }}
          onPageChange={pageIndex => setPartialPartSwapParams('page', pageIndex)}
        />
      </Card>
      <MobileDropDown className='z-1' open={actionBtn} onClose={() => setBtn(false)} onOpen={() => setBtn(true)}>
        <Typography sx={{ marginTop: 2 }} align='center' variant='h5'>
          Action
        </Typography>
        <Box className='flex gap-2 p-4 pb-2 flex-col'>{actionButton}</Box>
      </MobileDropDown>
    </>
  )
}

export default PartSwapList
