// MUI Imports

import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'
import { usePartSwap } from '../../context/PartSwapContext'
import { mrPriorityOptions } from '@/pages/material-request/create/config/enum'

const AdditionalInfoCard = () => {
  const { partSwapDetail } = usePartSwap()
  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Detil Lainnya</Typography>
        </div>
        <div className='flex flex-col gap-2'>
          <small>Prioritas</small>
          <Typography className='flex gap-2 items-center'>
            <div
              className={`size-2 ${mrPriorityOptions?.find(p => p.value === `${partSwapDetail?.priority ?? 3}`).color}`}
            />
            {`P${partSwapDetail?.priority ?? 3}`}
          </Typography>
        </div>
        <div className='flex flex-col gap-2'>
          <small>Departemen</small>
          <Typography>{partSwapDetail?.department?.name ?? '-'}</Typography>
        </div>
        <div className='flex flex-col gap-2'>
          <small>Lokasi</small>
          <Typography>{partSwapDetail?.site?.name ?? '-'}</Typography>
        </div>
        <div className='flex flex-col gap-2'>
          <small>Catatan</small>
          <Typography>{partSwapDetail?.note ?? '-'}</Typography>
        </div>
      </CardContent>
    </Card>
  )
}

export default AdditionalInfoCard
