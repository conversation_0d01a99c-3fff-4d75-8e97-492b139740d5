import { Document, Page, StyleSheet, Text, View, Font, Image } from '@react-pdf/renderer'
import { Table, TableHeader, TableCell, TableRow } from '@ag-media/react-pdf-table'
import { WoSegmentType, WorkOrderType } from '@/types/woTypes'
import { FrType } from '@/types/frTypes'
import { formatDate } from 'date-fns'
import { toTitleCase } from '@/utils/helper'
import { UnitType } from '@/types/companyTypes'

// Font.register({
//   family: 'Roboto',
//   fonts: [
//     { src: 'https://cdnjs.cloudflare.com/ajax/libs/ink/3.1.10/fonts/Roboto/roboto-regular-webfont.ttf', fontWeight: 'normal' },
//     { src: 'https://cdnjs.cloudflare.com/ajax/libs/ink/3.1.10/fonts/Roboto/roboto-bold-webfont.ttf', fontWeight: 'bold' }
//   ]
// })

const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    backgroundColor: '#FFFFFF',
    padding: 20,
    fontSize: 9
    // fontFamily: 'Roboto'
  },
  border: {
    border: '1px solid #000',
    borderRadius: 4,
    padding: 4
  },
  header: {
    marginBottom: 10,
    borderBottom: 1,
    paddingBottom: 5,
    borderBottomColor: '#dddddd'
  },
  woNumber: {
    fontSize: 14,
    fontWeight: 'bold'
  },
  woDate: {
    fontSize: 8,
    color: '#555555'
  },
  section: {
    marginBottom: 10,
    padding: 10,
    border: 1,
    borderColor: '#000'
  },
  sectionTitle: {
    fontSize: 11,
    fontWeight: 'bold',
    marginBottom: 8,
    borderBottom: 1,
    paddingBottom: 4,
    borderBottomColor: '#000'
  },
  grid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    margin: -5
  },
  gridCol: {
    width: '50%',
    padding: 5
  },
  field: {
    marginBottom: 5
  },
  fieldLabel: {
    fontWeight: 'bold',
    color: '#333333'
  },
  flexRow: {
    display: 'flex',
    flexDirection: 'row'
  },
  flexCol: {
    display: 'flex',
    flexDirection: 'column'
  },
  table: {
    width: '100%',
    marginBottom: 15,
    border: '1px solid #000'
  },
  tableRow: {
    flexDirection: 'row',
    alignItems: 'center',
    borderTop: '1px solid black'
  },
  tableHeader: {
    fontWeight: 'bold',
    fontSize: 8,
    borderTop: '1px solid black'
  },
  tableCell: {
    fontSize: 8,
    padding: 6,
    border: 'none'
  },
  textRight: {
    textAlign: 'right',
    width: '100%'
  },
  textCenter: {
    textAlign: 'center',
    width: '100%'
  },
  tableCol: {
    padding: 4,
    borderRight: '1px solid #eee'
  },
  tableColHeader: {
    padding: 4,
    borderRight: '1px solid #eee'
  },
  textBold: {
    fontFamily: 'Helvetica-Bold',
    fontWeight: 700,
    fontSize: 9
  },
  textAddress: {
    fontSize: 8,
    width: '100%'
  },
  signatureItem: {
    fontSize: 10,
    minHeight: 65,
    maxWidth: 120,
    flexGrow: 1,
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'space-between'
  },
  signatureName: {
    paddingTop: 4,
    fontSize: 9
  }
})

const header = ['No', 'Job Code', 'SMCS', 'Modifier', 'Parts', 'Misc', 'Status']

type WoPdfDocumentProps = {
  woDetail: WorkOrderType
  frData: FrType
  woSegments: WoSegmentType[]
  unit: UnitType
}

const WoPdfDocument = ({ woDetail, woSegments, unit, frData }: WoPdfDocumentProps) => {
  return (
    <Document>
      <Page size='A4' style={styles.page}>
        {/* Header */}
        <View style={[styles.flexRow, { justifyContent: 'space-between', gap: 4, marginBottom: 16 }]}>
          <View style={[styles.border, styles.flexRow, { gap: 8 }]}>
            <Image src='/rpu-logo.png' style={{ width: 32, height: 32 }} />
            <View style={[styles.flexCol]}>
              <Text style={[styles.textBold, { color: '#000' }]}>PT. RIMBA PERKASA UTAMA</Text>
              <View style={{ marginVertical: 2 }} />
              <Text style={styles.textAddress}>Jl. Danau Toba No. 148</Text>
              <Text style={[styles.textAddress, { marginTop: 2 }]}>Samarinda</Text>
            </View>
          </View>
          <Text style={[styles.textBold, { fontSize: 14, color: '#000', paddingHorizontal: 8 }]}>
            Dokumen Work Order
          </Text>
        </View>
        <View style={styles.grid}>
          <View style={styles.gridCol}>
            {/* Reporter */}
            <View style={styles.section}>
              <View style={styles.field}>
                <Text style={styles.fieldLabel}>No Laporan</Text>
                <Text>{frData?.number}</Text>
              </View>
              <View style={styles.field}>
                <Text style={styles.fieldLabel}>Tanggal</Text>
                <Text>{formatDate(frData?.createdAt, 'dd/MM/yyyy HH:mm')}</Text>
              </View>
              <View style={styles.field}>
                <Text style={styles.fieldLabel}>Lokasi Asal Unit</Text>
                <Text>
                  {frData?.site?.name ?? '-'} / {frData?.locationNote ?? '-'} / {`Pit ${frData?.locationPit ?? '-'}`}
                </Text>
              </View>
              <View style={styles.field}>
                <Text style={styles.fieldLabel}>Laporan</Text>
                <Text>{frData?.initialReport}</Text>
              </View>
              <View style={styles.field}>
                <Text style={styles.fieldLabel}>Symptom / Gejala</Text>
                <Text>{frData?.symptom ?? '-'}</Text>
              </View>
              <View style={styles.field}>
                <Text style={styles.fieldLabel}>Penyebab</Text>
                <Text>{frData?.cause ?? '-'}</Text>
              </View>
            </View>
          </View>
          <View style={styles.gridCol}>
            {/* Tujuan */}
            <View style={styles.section}>
              <View style={styles.field}>
                <Text style={styles.fieldLabel}>No Work Order</Text>
                <Text>{woDetail?.number}</Text>
              </View>
              <View style={styles.field}>
                <Text style={styles.fieldLabel}>Tanggal</Text>
                <Text>{formatDate(woDetail?.createdAt, 'dd/MM/yyyy HH:mm')}</Text>
              </View>
              <View style={styles.field}>
                <Text style={styles.fieldLabel}>Diserahkan Kepada</Text>
                <Text>{frData?.destinationSite?.name}</Text>
              </View>
              <View style={styles.field}>
                <Text style={styles.fieldLabel}>Departemen</Text>
                <Text>{frData?.department?.name}</Text>
              </View>
              <View style={styles.field}>
                <Text style={styles.fieldLabel}>Diagnosa</Text>
                <Text>{woDetail?.diagnosis ?? '-'}</Text>
              </View>
            </View>
          </View>
        </View>

        <Text style={[styles.sectionTitle, { borderBottom: 'none' }]}>Segment Pengerjaan</Text>
        {/* Tabel Items */}
        <Table
          tdStyle={{ padding: '10px', textAlign: 'center' }}
          style={styles.table}
          weightings={[0.1, 0.6, 0.6, 0.6, 0.3, 0.3, 0.3]}
        >
          <TableHeader fixed style={styles.tableHeader}>
            {header.map(header => (
              <TableCell key={`$indexHeader}`} style={[styles.tableCell]}>
                <Text
                  style={['Qty', 'Harga', 'Diskon', 'Total'].includes(header) ? styles.textCenter : styles.fieldLabel}
                >
                  {header}
                </Text>
              </TableCell>
            ))}
          </TableHeader>
          {woSegments?.map((segment, index) => (
            <TableRow
              key={index}
              style={[
                styles.tableRow,
                { borderBottom: index === woSegments?.length - 1 ? '1px solid #000' : 'none' },
                { borderTop: index === 0 ? '1px solid #000' : '0.5px solid #000' }
              ]}
            >
              <TableCell style={[styles.tableCell]}>
                <Text>{index + 1}</Text>
              </TableCell>
              <TableCell style={[styles.tableCell]}>
                <Text>{segment?.jobCode?.description ?? '-'}</Text>
              </TableCell>
              <TableCell style={[styles.tableCell]}>
                <Text style={{ textAlign: 'left' }}>{segment?.componentCode?.description ?? '-'}</Text>
              </TableCell>
              <TableCell style={[styles.tableCell]}>
                <Text style={{ textAlign: 'left' }}>{segment?.modifierCode?.description ?? '-'}</Text>
              </TableCell>
              <TableCell style={[styles.tableCell]}>
                <Text>{segment?.componentsCount ?? 0} Part</Text>
              </TableCell>
              <TableCell style={[styles.tableCell]}>
                <Text>{segment?.miscellaneousCount ?? 0} Misc</Text>
              </TableCell>
              <TableCell style={[styles.tableCell]}>
                <Text>{toTitleCase(segment?.status)}</Text>
              </TableCell>
            </TableRow>
          ))}
        </Table>

        <View style={[styles.gridCol, { marginBottom: 10, paddingLeft: 0 }]}>
          <Text style={[styles.fieldLabel]}>Catatan: {woDetail?.note ?? '-'}</Text>
        </View>

        <View style={styles.grid}>
          <View style={styles.gridCol}>
            {/* Detil Unit */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Detil Unit</Text>
              <View style={styles.field}>
                <Text style={styles.fieldLabel}>Kode. Unit</Text>
                <Text>{unit?.number ?? '-'}</Text>
              </View>
              <View style={styles.field}>
                <Text style={styles.fieldLabel}>Kategori Unit</Text>
                <Text>{unit?.category?.name ?? '-'}</Text>
              </View>
              <View style={styles.field}>
                <Text style={styles.fieldLabel}>Type Equipment</Text>
                <Text>{unit?.equipmentType ?? '-'}</Text>
              </View>
            </View>
          </View>
          <View style={styles.gridCol}>
            {/* Data Unit */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Data Unit</Text>
              <Text>KM: {woDetail?.unitKm}</Text>
              <Text>HM: {woDetail?.unitHm}</Text>
            </View>
          </View>
        </View>
        <View style={[styles.grid, { marginTop: 20 }]}>
          <View style={[styles.gridCol, { width: '100%' }]}>
            <View style={[styles.section, { border: 'none' }]}>
              <View style={styles.signatureItem}>
                <Text style={{ flexGrow: 3, marginBottom: 5, fontSize: 9 }}>Dibuat oleh</Text>
                <View style={{ borderBottom: '1px solid #000', width: '60%', marginTop: 5 }} />
                <Text style={styles.signatureName}>{woDetail?.createdByUser?.fullName}</Text>
              </View>
            </View>
          </View>
        </View>
      </Page>
    </Document>
  )
}

export default WoPdfDocument
