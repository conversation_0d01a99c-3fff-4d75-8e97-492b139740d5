import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Grid, Typography } from '@mui/material'
import { pdf } from '@react-pdf/renderer'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { Link, useLocation, useNavigate } from 'react-router-dom'
import CardAnalisis from './component/CardAnalisis'
import CardSegment from './component/CardSegment'
import UnitDetail from './component/UnitDetail'
import DataUnitDetail from './component/DataUnitDetail'
import CreatedByCard from './component/CreatedByCard'
import CardProblem from './component/CardProblem'
import CardSchedule from './component/CardSchedule'
import ActivityLogCard from './component/ActivityLogCard'
import LocationCard from './component/LocationCard'
import { useWo } from '../context/WoContext'
import { WoStatus } from '@/types/woTypes'
import { useEffect, useMemo, useState } from 'react'
import { getStatusConfig } from '../../pre-release/wo-list/config/utils'
import { getStatusConfig as woStatusConfig } from '../created/config/utils'
import { usePreRelease } from '../../pre-release/context/PreReleaseContext'
import { useQuery } from '@tanstack/react-query'
import { PRE_RELEASE_QUERY_LIST_KEY } from '@/api/services/pre-release/service'
import PreReleaseQueryMethods, { PRE_RELEASE_QUERY_KEY } from '@/api/services/pre-release/query'
import { PreReleaseStatusType, PreReleaseType } from '@/types/preReleaseTypes'
import PreReleaseDocCard from './component/PreReleaseDocCard'
import DialogDetilPreRelease from '@/components/dialogs/detail-pre-release-docs'
import { useAuth } from '@/contexts/AuthContext'
import { useReadPreReleaseApproval } from '@/api/services/pre-release/mutation'
import Permission from '@/core/components/Permission'
import AdditionalInfoCard from './component/AdditionalInfoCard'
import { FR_DETAIL_QUERY_KEY } from '@/api/services/rnm/service'
import RnMQueryMethods from '@/api/services/rnm/query'
import UnitTakingDocCard from './component/UnitTakingDocCard'
import WoPdfDocument from './component/WoPdfDocument'
import CompanyQueryMethods, { UNIT_QUERY_KEY } from '@/api/services/company/query'

const WoDetailPage = () => {
  const { woDetail, woLogs, refetchWoDetail, selectedWoId, woSegments, frDetail } = useWo()
  const { userProfile } = useAuth()
  const { onApprovePreRelease, onRejectPreRelease, loadingApproval, isDetailPage, isApprovalPage } = usePreRelease()
  const navigate = useNavigate()
  const { state, pathname } = useLocation()

  const [{ state: preReleaseState, selectedPreRelease }, setPreReleaseState] = useState<{
    state: boolean
    selectedPreRelease: PreReleaseType | null
  }>({
    state: false,
    selectedPreRelease: null
  })

  const isFromPreRelease = !!state?.isFromPreRelease
  const isFormPreReleaseApproval = !!state?.isFromPreReleaseApproval

  const { data: fieldReportData } = useQuery({
    enabled: !!woDetail?.fieldReport?.id,
    queryKey: [FR_DETAIL_QUERY_KEY, woDetail?.fieldReport?.id],
    queryFn: () => RnMQueryMethods.getFrDetail(woDetail?.fieldReport?.id)
  })

  const { data: findPreRelease } = useQuery({
    enabled: !!woDetail?.id,
    queryKey: [PRE_RELEASE_QUERY_LIST_KEY, woDetail?.id],
    queryFn: async () => {
      const res = await PreReleaseQueryMethods.getPreReleases({ workOrderId: woDetail?.id })
      return res.items?.[0] ?? null
    },
    placeholderData: null
  })

  const { data: preReleaseData, refetch: refetchPreRelease } = useQuery({
    enabled: !!findPreRelease?.id,
    queryKey: [PRE_RELEASE_QUERY_KEY, findPreRelease?.id],
    queryFn: () => PreReleaseQueryMethods.getOnePreRelease(findPreRelease?.id),
    cacheTime: 0
  })

  const { data: unitData } = useQuery({
    enabled: !!woDetail?.unit?.id,
    queryKey: [UNIT_QUERY_KEY, woDetail?.unit?.id],
    queryFn: () => CompanyQueryMethods.getUnit(woDetail?.unit?.id)
  })

  const { mutate: readMutate } = useReadPreReleaseApproval()

  const handleClickDoc = () => {
    setPreReleaseState({ state: true, selectedPreRelease: preReleaseData })
  }

  const handlePrint = async () => {
    if (frDetail && woDetail && woSegments && unitData) {
      const blob = await pdf(
        <WoPdfDocument frData={frDetail} woDetail={woDetail} woSegments={woSegments} unit={unitData} />
      ).toBlob()
      const url = URL.createObjectURL(blob)
      const printWindow = window.open(url, '_blank')
      printWindow.onload = () => {
        printWindow.print()
        printWindow.onafterprint = () => {
          printWindow.close()
        }
      }
    }
  }

  const configData = useMemo((): { href: string; label: string; color: string; chipLabel: string } => {
    switch (true) {
      case isDetailPage:
        return {
          href: '/wo/pre-release-created',
          label: 'Pre-Release Terbuat',
          chipLabel: getStatusConfig(woDetail?.status).label,
          color: getStatusConfig(woDetail?.status).color
        }
      case isFromPreRelease:
        return {
          href: '/wo/pre-releases',
          label: 'Pengajuan Pre-Release',
          chipLabel: getStatusConfig(preReleaseData?.status).label,
          color: getStatusConfig(preReleaseData?.status).color
        }
      case isFormPreReleaseApproval:
        return {
          href: '/wo/approval-pre-releases',
          label: 'Persetujuan Pre-Release',
          chipLabel: getStatusConfig(preReleaseData?.status).label,
          color: getStatusConfig(preReleaseData?.status).color
        }
      default:
        return {
          href: '/wo/created',
          label: 'Order Terbuat',
          chipLabel: woStatusConfig(woDetail?.status).label,
          color: woStatusConfig(woDetail?.status).color
        }
    }
  }, [isFormPreReleaseApproval, isFromPreRelease, isDetailPage, woDetail, preReleaseData])

  useEffect(() => {
    const ownApproval = preReleaseData?.approvals?.find(approval => approval.userId === userProfile?.id)
    if (ownApproval && ownApproval.isRead === false) {
      readMutate(
        {
          preReleaseId: preReleaseData?.id,
          approvalId: ownApproval.id
        },
        { onSuccess: () => refetchPreRelease() }
      )
    }
  }, [preReleaseData, userProfile])

  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Breadcrumbs aria-label='breadcrumb'>
          <Link to='#' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Work Order</Typography>
          </Link>
          <Link to={configData.href} replace>
            <Typography color='var(--mui-palette-text-disabled)'>{configData.label}</Typography>
          </Link>
          <Typography>Detil Work Order</Typography>
        </Breadcrumbs>
      </Grid>
      <Grid item xs={12}>
        <div className='flex justify-between items-end flex-col sm:flex-row max-sm:items-center gap-2'>
          <div className='flex flex-col'>
            <div className='flex items-center gap-2 flex-col sm:flex-row max-sm:text-center'>
              <Typography variant='h4'>No. WO: {woDetail?.number ?? '-'}</Typography>
              <Chip label={configData.chipLabel} color={configData.color as any} variant='tonal' size='small' />
            </div>
            <Typography className='max-sm:text-center max-sm:mt-2'>
              {formatDate(woDetail?.createdAt ? new Date(woDetail.createdAt) : new Date(), 'eeee, dd/MM/yyyy HH:mm', {
                locale: id
              })}
            </Typography>
          </div>
          <div className='flex gap-2 flex-col sm:flex-row is-full sm:is-auto'>
            <div className='flex gap-2'>
              {isFormPreReleaseApproval ||
              (woDetail?.status === WoStatus.PRE_RELEASED &&
                preReleaseData?.status === PreReleaseStatusType.PROCESSED) ? (
                <>
                  <Button
                    variant='contained'
                    color='error'
                    className='min-w-[144px]'
                    onClick={() => onRejectPreRelease(preReleaseData, refetchWoDetail)}
                    disabled={loadingApproval}
                  >
                    Tolak
                  </Button>
                  <Button
                    variant='contained'
                    color='primary'
                    className='min-w-[144px]'
                    onClick={() => onApprovePreRelease(preReleaseData, refetchWoDetail)}
                    disabled={loadingApproval}
                  >
                    Setujui
                  </Button>
                </>
              ) : (
                <>
                  {woDetail && (
                    <Button
                      color='secondary'
                      variant='outlined'
                      onClick={handlePrint}
                      startIcon={<i className='ic-outline-local-printshop' />}
                      className='is-full sm:is-auto'
                    >
                      Cetak
                    </Button>
                  )}
                  {woDetail?.status === WoStatus.ACTIVE ? (
                    woDetail?.isAllSegmentsCompleted ? (
                      <Permission permission={['work-order.write-pre-release-tmp']}>
                        <Button
                          disabled={!woDetail?.isAllSegmentsCompleted}
                          onClick={() => navigate(`${pathname}/create-pre-release`)}
                          variant='contained'
                        >
                          Buat Pre-Release
                        </Button>
                      </Permission>
                    ) : (
                      <Permission permission={['work-process.create', 'work-process.update']}>
                        <Button
                          variant='contained'
                          color='primary'
                          onClick={() => navigate({ pathname: `/wp/create`, search: `wo=${selectedWoId}` })}
                        >
                          Buat Work Process
                        </Button>
                      </Permission>
                    )
                  ) : null}
                  {preReleaseData?.status === PreReleaseStatusType.ASSIGNED && (
                    <Button
                      disabled={!woDetail?.isAllSegmentsCompleted}
                      onClick={() => navigate(`${pathname}/checklist`)}
                      variant='contained'
                    >
                      Ajukan Pre-Release
                    </Button>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      </Grid>
      {woDetail?.status === WoStatus.COMPLETED && (
        <Grid item xs={12}>
          <UnitTakingDocCard workOrder={woDetail} />
        </Grid>
      )}
      {[WoStatus.PRE_RELEASED, WoStatus.COMPLETED].includes(woDetail?.status as WoStatus) && findPreRelease && (
        <Grid item xs={12}>
          <PreReleaseDocCard onClickDoc={handleClickDoc} />
        </Grid>
      )}
      <Grid item xs={12}>
        <CardAnalisis />
      </Grid>
      <Grid item xs={12}>
        <CardSegment />
      </Grid>
      <Grid item xs={12} md={6}>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <UnitDetail unitData={unitData} />
          </Grid>
          <Grid item xs={12}>
            <DataUnitDetail />
          </Grid>
          <Grid item xs={12}>
            <AdditionalInfoCard woData={woDetail} />
          </Grid>
          <Grid item xs={12}>
            <CreatedByCard />
          </Grid>
        </Grid>
      </Grid>
      <Grid item xs={12} md={6}>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <LocationCard frData={woDetail?.fieldReport} />
          </Grid>
          <Grid item xs={12}>
            <CardProblem frDetail={fieldReportData} />
          </Grid>
          <Grid item xs={12}>
            <CardSchedule />
          </Grid>
          <Grid item xs={12}>
            <ActivityLogCard logList={woLogs} />
          </Grid>
        </Grid>
      </Grid>
      {preReleaseState && (
        <DialogDetilPreRelease
          detailed={isApprovalPage}
          open={preReleaseState}
          setOpen={bool => setPreReleaseState(curr => ({ ...curr, state: bool }))}
          preReleaseData={selectedPreRelease}
        />
      )}
    </Grid>
  )
}

export default WoDetailPage
