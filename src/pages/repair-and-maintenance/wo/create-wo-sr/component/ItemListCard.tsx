import { <PERSON><PERSON>, <PERSON>, CardContent, Typography } from '@mui/material'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import { tableColumns } from '../config/table'
import Table from '@/components/table'
import { useParams } from 'react-router-dom'
import { useWo } from '../../context/WoContext'
import RnMQueryMethods from '@/api/services/rnm/query'
import { useQuery } from '@tanstack/react-query'
import { useFormContext, useWatch } from 'react-hook-form'
import { SrItem, SrRmOPayload } from '@/types/srTypes'
import { useEffect, useMemo, useState } from 'react'
import { WarehouseItemType } from '@/types/appTypes'
import AddWarehouseItemDialog from '@/components/dialogs/add-warehouse-item'
import { WO_SEGMENT_KEY } from '@/api/services/rnm/service'
import { toast } from 'react-toastify'

const ItemListCard = () => {
  const params = useParams()
  const { activeSegment, woDetail, setActiveSegment } = useWo()
  const { reset, getValues, control, setValue } = useFormContext<SrRmOPayload>()

  const items = useWatch({ control, name: 'items' })

  const [{ open: addItemOpen, selectedItem }, setAddItemModalState] = useState({
    open: false,
    selectedItem: {} as WarehouseItemType
  })

  const [dialogItem, setDialogItem] = useState<boolean>(false)
  const [activeItem, setActiveItem] = useState<WarehouseItemType | null>(null)

  const { data: segmentDetail } = useQuery({
    enabled: !!woDetail?.id && (!!activeSegment?.id || !!params?.segmentId),
    queryKey: [WO_SEGMENT_KEY, woDetail?.id, activeSegment?.id ?? params?.segmentId],
    queryFn: () => RnMQueryMethods.getWoSegment(woDetail?.id, activeSegment?.id || params?.segmentId)
  })

  const tableOptions = useMemo(
    () => ({
      data: items ?? [],
      columns: tableColumns({
        onView: itemData => {
          setActiveItem({ ...itemData, itemId: itemData.item.id } as WarehouseItemType)
          setDialogItem(true)
        },
        onDelete: itemData => {
          const tempItems = [...items]
          const itemIndex = tempItems.findIndex(item => item.itemId === itemData.itemId)
          tempItems.splice(itemIndex, 1)
          setValue('items', tempItems)
        }
      }),
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [items]
  )

  const table = useReactTable<any>(tableOptions)

  useEffect(() => {
    if (segmentDetail?.items?.length > 0) {
      reset({
        ...getValues(),
        items:
          segmentDetail?.items
            ?.filter(item => item.type === 'COMPONENT')
            ?.map(item => ({
              ...item,
              itemId: item.item.id,
              serialNumberId: item.serialNumber?.id
            })) ?? []
      })
    } else {
      reset({
        ...getValues(),
        items: []
      })
    }
  }, [segmentDetail?.items])

  useEffect(() => {
    if (segmentDetail) {
      setActiveSegment(segmentDetail)
    }
  }, [segmentDetail])

  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-6'>
          <div className='flex justify-between items-center'>
            <Typography variant='h5'>Barang</Typography>
            <Button
              color='primary'
              variant='outlined'
              onClick={() => {
                setAddItemModalState(current => ({
                  open: true,
                  selectedItem: current.selectedItem,
                  selectedIndex: undefined
                }))
              }}
            >
              Tambah Barang
            </Button>
          </div>
          <div className='shadow-sm rounded-[8px]'>
            <Table
              table={table}
              emptyLabel={
                <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
                  <Typography>Belum ada Barang</Typography>
                  <Typography className='text-sm text-gray-400'>
                    Tambahkan barang yang ingin dimasukkan dalam dokumen ini
                  </Typography>
                </td>
              }
            />
          </div>
        </CardContent>
      </Card>
      {dialogItem && (
        <AddWarehouseItemDialog
          open={dialogItem}
          setOpen={setDialogItem}
          onSubmit={() => {}}
          viewOnly
          withoutUnit
          currentItem={activeItem}
        />
      )}
      {addItemOpen && (
        <AddWarehouseItemDialog
          open={addItemOpen}
          setOpen={open => {
            setAddItemModalState(current => ({
              open,
              selectedItem: !open ? {} : current.selectedItem,
              selectedIndex: undefined
            }))
          }}
          currentItem={selectedItem}
          onSubmit={itemData => {
            if (selectedItem?.itemId) {
              const tempItems = [...getValues('items')]
              const itemIndex = tempItems.findIndex(item => item.itemId === selectedItem.itemId)
              tempItems[itemIndex] = itemData as SrItem
              setValue('items', tempItems)
            } else {
              const existingItem = [...(getValues('items') ?? [])].find(item => item.itemId === itemData.itemId)
              if (existingItem) {
                toast.error('Barang sudah ditambahkan')
              } else {
                setValue('items', [...getValues('items'), { ...itemData, images: [] } as SrItem])
              }
            }
            setAddItemModalState({
              open: false,
              selectedItem: {} as WarehouseItemType
            })
          }}
          withoutUnit
          parentCodeStrict={false}
          siteId={getValues('siteId')}
        />
      )}
    </>
  )
}

export default ItemListCard
