import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>rid, Typo<PERSON> } from '@mui/material'
import { Link, useParams } from 'react-router-dom'
import { useWo } from '../context/WoContext'
import ItemListCard from './component/ItemListCard'
import AdditionalInfoCard from './component/AdditionalInfoCard'
import SegmentCard from './component/SegmentCard'
import UnitCard from './component/UnitCard'
import UnitDataCard from './component/UnitDataCard'
import ServiceRequestCard from './component/ServiceRequestCard'
import ApprovalListCard from './component/ApprovalListCard'
import { useAuth } from '@/contexts/AuthContext'
import { useQuery } from '@tanstack/react-query'
import UserQueryMethods, { DEFAULT_APPROVER_QUERY_KEY } from '@/api/services/user/query'
import { DefaultApprovalScope } from '@/pages/user/default-approval/config/enum'
import { useFormContext, useWatch } from 'react-hook-form'
import { SrRmOPayload } from '@/types/srTypes'
import { useWoSr } from '../context/WoSrContext'
import { useEffect } from 'react'
import LoadingButton from '@mui/lab/LoadingButton'
import * as Sentry from '@sentry/react'
import { toast } from 'react-toastify'

const CreateWoSr = () => {
  const { userProfile, groupedSiteList } = useAuth()
  const params = useParams()
  const { selectedWoId, navigate, woDetail, activeSegment } = useWo()
  const { handleSubmitServiceRequest, creatingSr } = useWoSr()

  const { control, handleSubmit, reset, getValues } = useFormContext<SrRmOPayload>()

  const siteIdwatch = useWatch({ control, name: 'siteId' })

  const findProjectIdBySiteId = (siteId: string) => {
    const group = groupedSiteList.find(group => group.sites.some(site => site.id === siteId))
    return group?.projectId
  }

  const scope = DefaultApprovalScope.ServiceRequisition

  const { data: approverList } = useQuery({
    enabled: !!woDetail?.siteId && !!woDetail?.departmentId,
    queryKey: [
      DEFAULT_APPROVER_QUERY_KEY,
      scope,
      woDetail?.siteId,
      woDetail?.departmentId,
      activeSegment?.divisionId,
      siteIdwatch
    ],
    queryFn: () =>
      UserQueryMethods.getDefaultApproverList({
        limit: 1000,
        // divisionId: activeSegment?.divisionId ?? undefined,
        divisionId: 'null',
        scope,
        departmentId: 'null',
        // departmentId: woDetail?.departmentId,
        projectId: findProjectIdBySiteId(siteIdwatch)
      }),
    placeholderData: []
  })

  useEffect(() => {
    reset({
      ...getValues(),
      workOrderSegmentId: params?.segmentId,
      priority: 4
    })
  }, [woDetail])

  useEffect(() => {
    reset({
      ...getValues(),
      approvals: approverList?.map(approver => ({
        userId: approver.user?.id
      }))
    })
  }, [approverList])

  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Breadcrumbs aria-label='breadcrumb'>
          <Link to='#' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Work Order</Typography>
          </Link>
          <Link to='/wo/created' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Order Terbuat</Typography>
          </Link>
          <Link to={'/wo/created/' + selectedWoId} replace>
            <Typography color='var(--mui-palette-text-disabled)'>Detil Work Order</Typography>
          </Link>
          <Typography>Buat Service Request</Typography>
        </Breadcrumbs>
      </Grid>
      <Grid item xs={12}>
        <div className='flex justify-between items-end flex-col sm:flex-row gap-2 max-sm:items-center'>
          <div className='flex flex-col max-sm:text-center'>
            <Typography variant='h4'>Buat Service Request</Typography>
            <Typography>Buat Service Request untuk diajukan kepada eselon terkait</Typography>
          </div>
          <div className='flex flex-col sm:flex-row gap-2 is-full sm:is-auto'>
            <Button color='secondary' variant='outlined' disabled={creatingSr} onClick={() => navigate(-1)}>
              Batalkan
            </Button>
            <LoadingButton
              startIcon={<></>}
              loading={creatingSr}
              variant='contained'
              onClick={handleSubmit(handleSubmitServiceRequest, errors => {
                console.error(errors)
                Sentry.captureException(errors)
                Object.entries(errors).forEach(([field, error]) => {
                  toast.error(`${field}: ${error?.message}`, {
                    autoClose: 5000
                  })
                })
              })}
            >
              Buat Service Request
            </LoadingButton>
          </div>
        </div>
      </Grid>
      <Grid item xs={12}>
        <ItemListCard />
      </Grid>
      <Grid item xs={12} md={6}>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <AdditionalInfoCard />
          </Grid>
          <Grid item xs={12}>
            <SegmentCard />
          </Grid>
          <Grid item xs={12}>
            <UnitCard />
          </Grid>
        </Grid>
      </Grid>
      <Grid item xs={12} md={6}>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <ServiceRequestCard />
          </Grid>
          <Grid item xs={12}>
            <ApprovalListCard approverList={approverList?.map(approver => approver.user) ?? []} />
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  )
}

export default CreateWoSr
