import { WoPartSwapDtoType } from '@/types/partSwapTypes'
import { zodResolver } from '@hookform/resolvers/zod'
import { createContext, useContext } from 'react'
import { useForm, UseFormReturn } from 'react-hook-form'
import { createPartSwapSchema } from '../create-wo-part-swap/config/schema'
import { useMenu } from '@/components/menu/contexts/menuContext'
import { useCreatePartSwap } from '@/api/services/part-swap/mutation'
import { toast } from 'react-toastify'
import { useNavigate } from 'react-router-dom'
import * as Sentry from '@sentry/react'

interface PartSwapContextProps {
  method: UseFormReturn<WoPartSwapDtoType, any, undefined>
  handleSubmitPartSwap: (data: WoPartSwapDtoType) => void
  creatingPartSwap: boolean
}

const PartSwapContext = createContext<PartSwapContextProps>({} as PartSwapContextProps)

export const useWoPartSwap = () => {
  const context = useContext(PartSwapContext)
  if (context === undefined) {
    throw new Error('usePartSwap must be used within a PartSwapContextProvider')
  }
  return context
}

export const CreatePartSwapProvider = ({ children }: { children: React.ReactNode }) => {
  const navigate = useNavigate()
  const { setConfirmState } = useMenu()
  const method = useForm<WoPartSwapDtoType>({
    resolver: zodResolver(createPartSwapSchema)
  })

  const { mutate: createPartSwap, isLoading: creatingPartSwap } = useCreatePartSwap()

  const handleSubmitPartSwap = (data: WoPartSwapDtoType) => {
    if (data.approvals.length === 0) {
      toast.error('Default Approval belum tersedia. Silahkan hubungi admin terlebih dahulu.')
      return
    }
    Sentry.captureMessage(`Submit Part Swap: ${JSON.stringify(data)}`)
    setConfirmState({
      open: true,
      title: 'Buat Part Swap',
      content: 'Apakah kamu yakin akan membuat Part Swap ini? Pastikan semua detil yang kamu masukkan sudah benar',
      confirmText: 'Buat Part Swap',
      onConfirm: () => {
        createPartSwap(
          { ...data, items: data.items.map(item => ({ ...item, images: undefined })) },
          {
            onSuccess: () => {
              toast.success('Part Swap berhasil dibuat')
              setTimeout(() => {
                navigate('/part-swap/list')
              }, 700)
            }
          }
        )
      }
    })
  }

  const value = {
    method,
    handleSubmitPartSwap,
    creatingPartSwap
  }

  return <PartSwapContext.Provider value={value}>{children}</PartSwapContext.Provider>
}
