import truncateString from '@/core/utils/truncate'
import { ItemMrInput } from '@/pages/material-request/create/config/schema'
import { WoPartSwapDtoType } from '@/types/partSwapTypes'
import { WoSegmentItem } from '@/types/woTypes'
import { IconButton, Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'
import { FieldErrors } from 'react-hook-form'

type DataTypeWithAction = WoSegmentItem & {
  action?: string
}

type OriginSegmentItem = DataTypeWithAction & {
  originItem: WoSegmentItem
  originItemId: string
  originSerialNumber: string
}

type RowActionType = {
  onEdit?: (item: ItemMrInput) => void
  onView?: (item: ItemMrInput) => void
  onEditOrigin?: (index: number, item?: WoSegmentItem) => void
  onViewOrigin?: (item: WoSegmentItem) => void
  errors?: FieldErrors<WoPartSwapDtoType>
}

const columnHelper = createColumnHelper<DataTypeWithAction>()
const columnHelperOrigin = createColumnHelper<OriginSegmentItem>()

export const tableColumnsOrigin = (rowAction: RowActionType) => [
  columnHelperOrigin.accessor('originItem.item.number', {
    header: 'KODE PART',
    cell: ({ row }) => {
      const { errors = {} } = rowAction
      return (
        <div className='flex flex-col gap-0.5'>
          <Typography>{row.original.originItem?.item?.number ?? '-'}</Typography>
          {errors?.items?.[row.index]?.originItemId && (
            <Typography color='error' variant='caption'>
              Pilih part
            </Typography>
          )}
        </div>
      )
    }
  }),
  columnHelperOrigin.accessor('originItem.serialNumber.number', {
    header: 'NO SERIAL',
    cell: ({ row }) => (
      <Typography>{truncateString(row.original.originItem?.serialNumber?.number ?? '-', 10)}</Typography>
    )
  }),
  columnHelperOrigin.accessor('originItem.item.name', {
    header: 'NAMA ITEM',
    cell: ({ row }) => <Typography>{truncateString(row.original.originItem?.item?.name ?? '-', 10)}</Typography>
  }),
  columnHelperOrigin.accessor('action', {
    header: 'Action',
    cell: ({ row }) => (
      <div className='flex items-center gap-0.5'>
        {row.original?.originItem ? (
          <IconButton
            size='small'
            onClick={() =>
              rowAction.onViewOrigin({
                ...row.original?.originItem,
                quantity: row.original.quantity,
                quantityUnit: row.original.quantityUnit
              })
            }
          >
            <i className='ri-eye-line text-secondary' />
          </IconButton>
        ) : (
          <IconButton size='small' onClick={() => rowAction.onEditOrigin(row.index, row.original)}>
            <i className='ic-baseline-edit text-secondary' />
          </IconButton>
        )}
      </div>
    ),
    enableSorting: false
  })
]

export const tableColumns = (rowAction: RowActionType) => [
  columnHelper.accessor('item.number', {
    header: 'KODE PART',
    cell: ({ row }) => <Typography>{truncateString(row.original.item?.number, 10) ?? '-'}</Typography>
  }),
  columnHelper.accessor('serialNumber.number', {
    header: 'NO SERIAL',
    cell: ({ row }) => <Typography>{truncateString(row.original.serialNumber?.number, 10) ?? '-'}</Typography>
  }),
  columnHelper.accessor('item.name', {
    header: 'NAMA ITEM',
    cell: ({ row }) => <Typography>{row.original.item?.name}</Typography>
  }),
  columnHelper.accessor('action', {
    header: 'Action',
    cell: ({ row }) => (
      <div className='flex items-center gap-0.5'>
        {!!rowAction.onEdit && (
          <IconButton size='small' onClick={() => rowAction.onEdit(row.original)}>
            <i className='ic-baseline-edit text-secondary' />
          </IconButton>
        )}
        {!!rowAction.onView && (
          <IconButton size='small' onClick={() => rowAction.onView(row.original)}>
            <i className='ri-eye-line text-secondary' />
          </IconButton>
        )}
      </div>
    ),
    enableSorting: false
  })
]
