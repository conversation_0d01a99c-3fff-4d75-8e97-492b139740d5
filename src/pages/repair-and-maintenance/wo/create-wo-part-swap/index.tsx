import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>rid, Typo<PERSON> } from '@mui/material'
import { Link, useParams } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'
import LoadingButton from '@mui/lab/LoadingButton'
import { useEffect } from 'react'

import { useAuth } from '@/contexts/AuthContext'
import { useWo } from '../context/WoContext'
import { useWoPartSwap } from '../context/WoPartSwapContext'

import ItemListCard from './component/ItemListCard'
import AdditionalInfoCard from './component/AdditionalInfoCard'
import ApprovalListCard from './component/ApprovalListCard'

import UserQueryMethods, { DEFAULT_APPROVER_QUERY_KEY } from '@/api/services/user/query'
import { DefaultApprovalScope } from '@/pages/user/default-approval/config/enum'
import UnitSwapCard from './component/UnitSwapCard'
import * as Sentry from '@sentry/react'
import { toast } from 'react-toastify'
import RnMQueryMethods from '@/api/services/rnm/query'

const CreateWoPartSwap = () => {
  const { userProfile, groupedSiteList } = useAuth()
  const params = useParams()
  const { selectedWoId, navigate, woDetail, activeSegment } = useWo()
  const { handleSubmitPartSwap, method, creatingPartSwap } = useWoPartSwap()

  const { handleSubmit, reset, getValues } = method

  const scope = DefaultApprovalScope.PartSwap

  // const { data: segmentDetail } = useQuery({
  //   enabled: !!selectedWoId && !!params?.segmentId,
  //   queryKey: ['WO_SEGMENT_KEY', params?.segmentId, selectedWoId],
  //   queryFn: async () => RnMQueryMethods.getWoSegment(selectedWoId, params?.segmentId)
  // })

  const findProjectIdBySiteId = (siteId: string) => {
    const group = groupedSiteList.find(group => group.sites.some(site => site.id === siteId))
    return group?.projectId
  }

  const { data: approverList } = useQuery({
    enabled: !!woDetail?.siteId && !!woDetail?.departmentId,
    queryKey: [DEFAULT_APPROVER_QUERY_KEY, scope, woDetail?.siteId, woDetail?.departmentId, activeSegment?.divisionId],
    queryFn: () =>
      UserQueryMethods.getDefaultApproverList({
        limit: 1000,
        // divisionId: activeSegment?.divisionId ?? 'null',
        divisionId: 'null',
        scope,
        projectId: findProjectIdBySiteId(woDetail?.siteId),
        departmentId: 'null'
        // departmentId: woDetail?.departmentId
      }),
    placeholderData: []
  })

  useEffect(() => {
    reset({
      ...getValues(),
      workOrderSegmentId: params?.segmentId,
      destinationUnitHm: woDetail?.unitHm,
      destinationUnitKm: woDetail?.unitKm,
      destinationUnitId: woDetail?.unitId,
      destinationUnitSiteId: woDetail?.siteId,
      priority: 4
    })
  }, [woDetail])

  useEffect(() => {
    reset({
      ...getValues(),
      approvals: approverList?.map(approver => ({
        userId: approver.user?.id
      }))
    })
  }, [approverList])

  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Breadcrumbs aria-label='breadcrumb'>
          <Link to='#' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Work Order</Typography>
          </Link>
          <Link to='/wo/created' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Order Terbuat</Typography>
          </Link>
          <Link to={'/wo/created/' + selectedWoId} replace>
            <Typography color='var(--mui-palette-text-disabled)'>Detil Work Order</Typography>
          </Link>
          <Typography>Buat Part Swap</Typography>
        </Breadcrumbs>
      </Grid>
      <Grid item xs={12}>
        <div className='flex justify-between items-end flex-col sm:flex-row gap-2 max-sm:items-center'>
          <div className='flex flex-col max-sm:text-center'>
            <Typography variant='h4'>Buat Part Swap</Typography>
            <Typography>Buat dokumen permintaan part Swap untuk diajukan kepada role terkait</Typography>
          </div>
          <div className='flex flex-col sm:flex-row gap-2 is-full sm:is-auto'>
            <Button color='secondary' variant='outlined' disabled={creatingPartSwap} onClick={() => navigate(-1)}>
              Batalkan
            </Button>
            <LoadingButton
              startIcon={<></>}
              loading={creatingPartSwap}
              variant='contained'
              onClick={handleSubmit(handleSubmitPartSwap, errors => {
                console.error(errors)
                Sentry.captureException(errors)
                Object.entries(errors).forEach(([field, error]) => {
                  toast.error(`${field}: ${error?.message}`, {
                    autoClose: 5000
                  })
                })
              })}
            >
              Buat Part Swap
            </LoadingButton>
          </div>
        </div>
      </Grid>
      <Grid item xs={12}>
        <UnitSwapCard />
      </Grid>
      <Grid item xs={12}>
        <ItemListCard />
      </Grid>
      <Grid item xs={12} md={6}>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <AdditionalInfoCard />
          </Grid>
        </Grid>
      </Grid>
      <Grid item xs={12} md={6}>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <ApprovalListCard approverList={approverList?.map(approver => approver.user) ?? []} />
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  )
}

export default CreateWoPartSwap
