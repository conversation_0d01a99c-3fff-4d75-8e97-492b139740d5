import { useMemo, useState } from 'react'
import {
  Autocomplete,
  Card,
  CardContent,
  CircularProgress,
  debounce,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography
} from '@mui/material'
import { UnitType } from '@/types/companyTypes'
import { useWoPartSwap } from '../../context/WoPartSwapContext'
import { Controller, useWatch } from 'react-hook-form'
import { useQuery } from '@tanstack/react-query'
import CompanyQueryMethods, {
  SITE_LIST_QUERY_KEY,
  UNIT_LIST_QUERY_KEY,
  UNIT_QUERY_KEY
} from '@/api/services/company/query'
import { hoursToMinutes } from '@/pages/repair-and-maintenance/fr/create/config/utils'
import NumberField from '@/components/numeric/NumberField'
import { DEFAULT_CATEGORY } from '@/data/default/category'

const UnitSwapCard = () => {
  const { method } = useWoPartSwap()
  const { control, reset, getValues } = method
  const [originUnitSearchQuery, setOriginUnitSearchQuery] = useState('')

  const [originUnit, setOriginUnit] = useState<UnitType | null>(null)
  const destinationUnitWatch = useWatch({ control, name: 'destinationUnitId' })

  const {
    data: unitListOrigin,
    isFetching: fetchUnitListOrigin,
    remove: removeOriginUnitList
  } = useQuery({
    enabled: !!originUnitSearchQuery,
    queryKey: [UNIT_LIST_QUERY_KEY, originUnitSearchQuery],
    queryFn: async () => {
      const res = await CompanyQueryMethods.getUnitList({
        search: originUnitSearchQuery,
        limit: Number.MAX_SAFE_INTEGER
      })
      return res.items ?? []
    },
    placeholderData: []
  })

  const { data: destinationUnit, isFetching: fetchDestinationUnit } = useQuery({
    enabled: !!destinationUnitWatch,
    queryKey: [UNIT_QUERY_KEY, destinationUnitWatch],
    queryFn: () => CompanyQueryMethods.getUnit(destinationUnitWatch)
  })

  const { data: siteList } = useQuery({
    enabled: !!destinationUnitWatch,
    queryKey: [SITE_LIST_QUERY_KEY, destinationUnitWatch],
    queryFn: async () => {
      const res = await CompanyQueryMethods.getSiteList({ limit: Number.MAX_SAFE_INTEGER, types: 'WORKSHOP' })
      return res.items
    },
    placeholderData: []
  })

  const [destinationHoursHm, destinationMinutesHm] = useMemo(() => {
    return hoursToMinutes(destinationUnit?.hm ?? 0)
  }, [destinationUnit])

  const [originHoursHm, originMinutesHm] = useMemo(() => {
    return hoursToMinutes(originUnit?.hm ?? 0)
  }, [originUnit])

  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-6'>
          <div className='flex justify-between items-center'>
            <Typography variant='h5'>Unit</Typography>
          </div>
          <Grid container spacing={4}>
            <Grid item xs={12} md={6}>
              <div className='bg-[#4C4E640D] rounded-[8px] p-4 flex flex-col gap-2'>
                <Typography variant='h5'>Unit Asal</Typography>
                <Typography variant='body1'>Unit yang diambil partnya</Typography>
                <Grid container spacing={4}>
                  <Grid item xs={12}>
                    <Controller
                      control={control}
                      name='originUnitId'
                      render={({ field: { value, onChange }, fieldState: { error } }) => (
                        <Autocomplete
                          filterOptions={x => x}
                          isOptionEqualToValue={(option, value) => option.id === value.id}
                          options={unitListOrigin ?? []}
                          fullWidth
                          value={originUnit}
                          onInputChange={debounce((e, newValue, reason) => {
                            if (reason === 'input') {
                              setOriginUnitSearchQuery(newValue)
                            }
                          }, 700)}
                          onChange={(e, newValue: UnitType) => {
                            onChange(newValue.id)
                            reset({
                              ...getValues(),
                              originUnitHm: newValue?.hm ?? 0,
                              originUnitKm: newValue?.km ?? 0
                            })
                            setOriginUnit(newValue)
                            removeOriginUnitList()
                          }}
                          noOptionsText='Unit tidak ditemukan'
                          loading={fetchUnitListOrigin}
                          renderInput={params => (
                            <TextField
                              {...params}
                              label=''
                              placeholder='Cari kode atau nomor lambung unit'
                              variant='outlined'
                              fullWidth
                              className='bg-white rounded-lg'
                              InputProps={{
                                ...params.InputProps,
                                startAdornment: <i className='ri-search-line text-textSecondary size-5 mx-2' />,
                                endAdornment: <>{false ? <CircularProgress /> : null}</>,
                                onKeyDown: e => {
                                  if (e.key === 'Enter') {
                                    e.stopPropagation()
                                  }
                                }
                              }}
                              error={!!error}
                            />
                          )}
                          getOptionLabel={(option: UnitType) =>
                            `${option.number} | ${option.type} | ${option.category?.name ?? DEFAULT_CATEGORY.name}`
                          }
                          renderOption={(props, option) => {
                            const { key, ...optionProps } = props
                            return (
                              <li key={key} {...optionProps}>
                                <Typography>
                                  {option.number} | {option.type} | {option.category?.name ?? DEFAULT_CATEGORY.name}
                                </Typography>
                              </li>
                            )
                          }}
                        />
                      )}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      disabled
                      value={originUnit?.number ?? '-'}
                      fullWidth
                      label='Kode Unit'
                      className='bg-white rounded-lg'
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      disabled
                      value={originUnit?.category?.name ?? DEFAULT_CATEGORY.name}
                      fullWidth
                      label='Kategori Unit'
                      className='bg-white rounded-lg'
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      disabled
                      value={originUnit?.brandName ?? '-'}
                      fullWidth
                      label='Merk Unit'
                      className='bg-white rounded-lg'
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      disabled
                      value={originUnit?.subCategory?.name ?? '-'}
                      fullWidth
                      label='Tipe Unit'
                      className='bg-white rounded-lg'
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      disabled
                      value={originUnit?.hullNumber ?? '-'}
                      fullWidth
                      label='Nomor Lambung'
                      className='bg-white rounded-lg'
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <Controller
                      control={control}
                      name='originUnitSiteId'
                      render={({ field: { value, onChange }, fieldState: { error } }) => (
                        <FormControl fullWidth>
                          <InputLabel id='unit-location-origin'>Lokasi Unit</InputLabel>
                          <Select
                            value={value}
                            onChange={e => onChange(e.target.value)}
                            error={!!error}
                            labelId='unit-location-origin'
                            id='unit-location-origin'
                            inputProps={{ className: 'bg-white' }}
                            label='Lokasi Unit'
                          >
                            {siteList?.map(unit => (
                              <MenuItem key={unit.id} value={unit.id}>
                                {unit.name} Workshop
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>
                      )}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <Controller
                      control={control}
                      name='originUnitKm'
                      render={({ field: { value, onChange }, fieldState: { error } }) => (
                        <TextField
                          value={value}
                          onChange={e => onChange(+e.target.value)}
                          className='bg-white rounded-lg'
                          fullWidth
                          label='KM'
                          InputProps={{ endAdornment: 'km', inputComponent: NumberField as any }}
                        />
                      )}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Controller
                      control={control}
                      name='originUnitHm'
                      render={({ field: { value, onChange }, fieldState: { error } }) => (
                        <TextField
                          value={value}
                          onChange={e => onChange(+e.target.value)}
                          className='bg-white rounded-lg'
                          label='HM'
                          InputProps={{ endAdornment: 'jam', inputComponent: NumberField as any }}
                        />
                      )}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      defaultValue={originMinutesHm ?? 0}
                      className='bg-white rounded-lg'
                      label='HM'
                      InputProps={{ endAdornment: 'mnt', inputComponent: NumberField as any }}
                    />
                  </Grid>
                </Grid>
              </div>
            </Grid>
            <Grid item xs={12} md={6}>
              <div className='bg-[#4C4E640D] rounded-[8px] p-4 flex flex-col gap-2'>
                <Typography variant='h5'>Unit Tujuan</Typography>
                <Typography variant='body1'>Unit yang sedang kamu service</Typography>
                <Grid container spacing={4}>
                  <Grid item xs={12}>
                    <Autocomplete
                      disabled
                      filterOptions={x => x}
                      value={destinationUnit}
                      isOptionEqualToValue={(option, value) => option.id === value.id}
                      options={[destinationUnit]}
                      freeSolo={!originUnitSearchQuery}
                      fullWidth
                      noOptionsText='Barang tidak ditemukan'
                      loading={fetchDestinationUnit}
                      renderInput={params => (
                        <TextField
                          {...params}
                          label=''
                          placeholder='Cari kode atau nomor lambung unit'
                          variant='outlined'
                          fullWidth
                          className='bg-white rounded-lg'
                          InputProps={{
                            ...params.InputProps,
                            startAdornment: <i className='ri-search-line text-textSecondary size-5 mx-2' />,
                            endAdornment: <>{false ? <CircularProgress /> : null}</>,
                            onKeyDown: e => {
                              if (e.key === 'Enter') {
                                e.stopPropagation()
                              }
                            }
                          }}
                        />
                      )}
                      getOptionLabel={(option: UnitType) =>
                        `${option.number} | ${option.type} | ${option.category?.name ?? DEFAULT_CATEGORY.name}`
                      }
                      renderOption={(props, option) => {
                        const { key, ...optionProps } = props
                        return (
                          <li key={key} {...optionProps}>
                            <Typography>
                              {option.number} | {option.type} | {option.category?.name ?? DEFAULT_CATEGORY.name}
                            </Typography>
                          </li>
                        )
                      }}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      disabled
                      fullWidth
                      label='Kode Unit'
                      value={destinationUnit?.number ?? '-'}
                      className='bg-white rounded-lg'
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      disabled
                      fullWidth
                      label='Kategori Unit'
                      value={destinationUnit?.category?.name ?? DEFAULT_CATEGORY.name}
                      className='bg-white rounded-lg'
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      disabled
                      fullWidth
                      label='Merk Unit'
                      value={destinationUnit?.brandName ?? '-'}
                      className='bg-white rounded-lg'
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      disabled
                      fullWidth
                      label='Tipe Unit'
                      value={destinationUnit?.subCategory?.name ?? '-'}
                      className='bg-white rounded-lg'
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      disabled
                      fullWidth
                      label='Nomor Lambung'
                      value={destinationUnit?.hullNumber ?? '-'}
                      className='bg-white rounded-lg'
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <Controller
                      control={control}
                      name='destinationUnitSiteId'
                      render={({ field: { value, onChange }, fieldState: { error } }) => {
                        return (
                          <FormControl fullWidth>
                            <InputLabel id='unit-location-origin'>Lokasi Unit</InputLabel>
                            <Select
                              key={value}
                              labelId='unit-location-origin'
                              id='unit-location-origin'
                              inputProps={{ className: 'bg-white' }}
                              label='Lokasi Unit'
                              value={value}
                              onChange={e => onChange(e.target.value)}
                              error={!!error}
                            >
                              {siteList?.map(unit => (
                                <MenuItem key={unit.id} value={unit.id}>
                                  {unit.name} Workshop
                                </MenuItem>
                              ))}
                            </Select>
                          </FormControl>
                        )
                      }}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      value={destinationUnit?.km ?? 0}
                      className='bg-white rounded-lg'
                      fullWidth
                      label='KM'
                      InputProps={{ endAdornment: 'km' }}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      value={destinationHoursHm ?? 0}
                      className='bg-white rounded-lg'
                      label='HM'
                      InputProps={{ endAdornment: 'jam' }}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      value={destinationMinutesHm ?? 0}
                      className='bg-white rounded-lg'
                      label='HM'
                      InputProps={{ endAdornment: 'mnt' }}
                    />
                  </Grid>
                </Grid>
              </div>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </>
  )
}

export default UnitSwapCard
