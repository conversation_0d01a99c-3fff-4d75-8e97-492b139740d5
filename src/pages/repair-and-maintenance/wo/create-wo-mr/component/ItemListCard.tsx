import { <PERSON><PERSON>, <PERSON>, CardContent, Typography } from '@mui/material'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import { tableColumns } from '../config/table'
import Table from '@/components/table'
import { memo, useEffect, useMemo, useState } from 'react'
import { useWo } from '../../context/WoContext'
import { useQuery } from '@tanstack/react-query'
import RnMQueryMethods from '@/api/services/rnm/query'
import { WoSegmentItem } from '@/types/woTypes'
import { useWoMr } from '../../context/WoMrContext'
import { useParams } from 'react-router-dom'
import AddWarehouseItemDialog from '@/components/dialogs/add-warehouse-item'
import { WarehouseItemType } from '@/types/appTypes'

const ItemListCard = () => {
  const params = useParams()
  const { activeSegment, woDetail, setActiveSegment } = useWo()
  const { setPartialPayload, payload } = useWoMr()

  const [dialogItem, setDialogItem] = useState<boolean>(false)
  const [{ open: addItemOpen, selectedItem }, setAddItemModalState] = useState({
    open: false,
    selectedItem: {} as WarehouseItemType
  })

  const [activeItem, setActiveItem] = useState<WarehouseItemType | null>(null)

  const { data: segmentDetail } = useQuery({
    enabled: !!woDetail?.id && (!!activeSegment?.id || !!params?.segmentId),
    queryKey: ['WO_SEGMENT_KEY', woDetail?.id, activeSegment?.id ?? params?.segmentId],
    queryFn: () => {
      return RnMQueryMethods.getWoSegment(woDetail?.id, activeSegment?.id || params?.segmentId)
    }
  })

  const tableOptions = useMemo(
    () => ({
      data: payload?.items ?? [],
      columns: tableColumns({
        onView: itemData => {
          setActiveItem({ ...itemData, itemId: itemData.item.id } as WarehouseItemType)
          setDialogItem(true)
        },
        onDelete: itemData => {
          const tempItems = [...payload?.items]
          const itemIndex = tempItems.findIndex(item => item.itemId === itemData.itemId)
          tempItems.splice(itemIndex, 1)
          setPartialPayload('items', tempItems)
        }
      }),
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [payload]
  )

  const table = useReactTable<any>(tableOptions)

  useEffect(() => {
    setPartialPayload(
      'items',
      segmentDetail?.items?.map(item => ({
        ...item,
        itemId: item.item.id,
        quantity: item.quantity,
        quantityUnit: item.quantityUnit,
        note: item.note,
        images: item.images?.map(image => ({
          uploadId: image.uploadId
        })),
        largeUnitQuantity: item.largeUnitQuantity,
        serialNumber: item.serialNumber?.number
      }))
    )
  }, [segmentDetail])

  useEffect(() => {
    if (segmentDetail) {
      setActiveSegment(segmentDetail)
    }
  }, [segmentDetail])

  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-6'>
          <div className='flex justify-between items-center'>
            <Typography variant='h5'>Barang</Typography>
            <Button
              color='primary'
              variant='outlined'
              onClick={() => {
                setAddItemModalState(current => ({
                  open: true,
                  selectedItem: current.selectedItem,
                  selectedIndex: undefined
                }))
              }}
            >
              Tambah Barang
            </Button>
          </div>
          <div className='shadow-sm rounded-[8px]'>
            <Table
              table={table}
              emptyLabel={
                <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
                  <Typography>Belum ada Barang</Typography>
                  <Typography className='text-sm text-gray-400'>
                    Tambahkan barang yang ingin dimasukkan dalam Material Request ini
                  </Typography>
                </td>
              }
              disablePagination
            />
          </div>
        </CardContent>
      </Card>
      {dialogItem && (
        <AddWarehouseItemDialog
          open={dialogItem}
          setOpen={setDialogItem}
          onSubmit={() => {}}
          viewOnly
          withoutUnit
          currentItem={activeItem}
        />
      )}
      {addItemOpen && (
        <AddWarehouseItemDialog
          open={addItemOpen}
          currentItem={selectedItem}
          setOpen={open => {
            setAddItemModalState(current => ({
              open,
              selectedItem: !open ? {} : current.selectedItem,
              selectedIndex: undefined
            }))
          }}
          onSubmit={itemData => {
            setPartialPayload('items', [
              ...payload?.items,
              {
                ...itemData,
                images: []
              }
            ])
            setAddItemModalState({
              open: false,
              selectedItem: {} as WarehouseItemType
            })
          }}
          withoutUnit
          parentCodeStrict={false}
          siteId={woDetail?.siteId}
        />
      )}
    </>
  )
}

export default memo(ItemListCard)
