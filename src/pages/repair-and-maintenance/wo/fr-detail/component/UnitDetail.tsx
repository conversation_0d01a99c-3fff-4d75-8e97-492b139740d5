import CompanyQueryMethods from '@/api/services/company/query'
import { DEFAULT_CATEGORY } from '@/data/default/category'
import { UnitType } from '@/types/companyTypes'
import { Card, CardContent, Typography } from '@mui/material'
import { useQuery } from '@tanstack/react-query'

type UnitDetailProps = {
  unitData?: UnitType
}

const UnitDetail = ({ unitData }: UnitDetailProps) => {
  const { data: unitDetail } = useQuery({
    enabled: !!unitData?.id,
    queryKey: ['UNIT_QUERY_DETAIL_KEY', unitData?.id],
    queryFn: () => CompanyQueryMethods.getUnit(unitData?.id)
  })

  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Detil Unit</Typography>
        </div>
        <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
          <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
            <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
              <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                Kode Unit
              </small>
            </label>
            <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
            <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
              {unitDetail?.number ?? '-'}
            </p>
          </div>
          <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
            <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
              <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                Kode Activa
              </small>
            </label>
            <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
            <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
              {unitDetail?.asset?.code ?? '-'}
            </p>
          </div>
          <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
            <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
              <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                Kategori Unit
              </small>
            </label>
            <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
            <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
              {unitDetail?.category?.name ?? DEFAULT_CATEGORY.name}
            </p>
          </div>
          <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
            <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
              <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                Jenis Unit
              </small>
            </label>
            <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
            <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
              {unitDetail?.subCategory?.name ?? '-'}
            </p>
          </div>
          <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
            <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
              <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                Type Equipment
              </small>
            </label>
            <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
            <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
              {unitDetail?.equipmentType ?? '-'}
            </p>
          </div>
          <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
            <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
              <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                Merk Unit
              </small>
            </label>
            <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
            <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
              {unitDetail?.brandName ?? '-'}
            </p>
          </div>
          <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
            <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
              <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                Tipe Unit
              </small>
            </label>
            <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
            <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
              {unitDetail?.type ?? '-'}
            </p>
          </div>
          <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
            <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
              <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                Nomor Lambung
              </small>
            </label>
            <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
            <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
              {unitDetail?.hullNumber ?? '-'}
            </p>
          </div>
          <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
            <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
              <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                Nomor Rangka
              </small>
            </label>
            <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
            <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
              {unitDetail?.chassisNumber ?? '-'}
            </p>
          </div>
          <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
            <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
              <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                Nomor Mesin
              </small>
            </label>
            <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
            <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
              {unitDetail?.engineNumber ?? '-'}
            </p>
          </div>
          <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
            <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
              <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                Plat Nomor
              </small>
            </label>
            <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
            <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
              {unitDetail?.plateNumber ?? '-'}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default UnitDetail
