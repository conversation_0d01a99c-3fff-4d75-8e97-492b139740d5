import { defaultListData } from '@/api/queryClient'
import ParameterQueryMethods from '@/api/services/parameter/query'
import { PARAMETER_QUERY_KEY } from '@/api/services/parameter/service'
import {
  useCreatePreRelease,
  useUnitRelease,
  useUpdatePreReleaseApprovalStatus
} from '@/api/services/pre-release/mutation'
import RnMQueryMethods from '@/api/services/rnm/query'
import useMobileScreen from '@/components/dialogs/hooks/useMobileScreen'
import { useMenu } from '@/components/menu/contexts/menuContext'
import usePartialState from '@/core/hooks/usePartialState'
import { ListResponse } from '@/types/api'
import { ParameterType } from '@/types/parameterTypes'
import { PreReleaseDtoType, PreReleaseParams, PreReleaseType } from '@/types/preReleaseTypes'
import { WoLogType, WoParams, WorkOrderType, WoStatus } from '@/types/woTypes'
import { TextField, Typography } from '@mui/material'
import { QueryObserverResult, RefetchOptions, RefetchQueryFilters, useQuery } from '@tanstack/react-query'
import { createContext, ReactNode, useContext, useEffect, useState } from 'react'
import { NavigateFunction, useLocation, useNavigate, useParams } from 'react-router-dom'
import { toast } from 'react-toastify'
import { useAuth } from '@/contexts/AuthContext'
import { WO_LOGS_QUERY_KEY, WO_QUERY_DETAIL_KEY } from '@/api/services/rnm/service'
import { PRE_RELEASE_QUERY_LIST_KEY } from '@/api/services/pre-release/service'
import PreReleaseQueryMethods from '@/api/services/pre-release/query'

type PreReleaseContextProps = {
  isMobile: boolean
  woParams: WoParams
  navigate: NavigateFunction
  setPartialWoParams: (fieldName: keyof WoParams, value: any) => void
  setWoParams: React.Dispatch<React.SetStateAction<WoParams>>
  setPartialPreReleaseParams: (fieldName: keyof PreReleaseParams, value: any) => void
  setPreReleaseParams: React.Dispatch<React.SetStateAction<PreReleaseParams>>
  preReleaseParams: PreReleaseParams
  woList: ListResponse<WorkOrderType>
  woDetail: WorkOrderType
  woLogs: WoLogType[]
  preleaseList: ListResponse<PreReleaseType>
  onSubmitPreRelease: (dto: PreReleaseDtoType, cb?: () => {}) => void
  onApprovePreRelease: (preRelease: PreReleaseType, cb?: () => void) => void
  onRejectPreRelease: (preRelease: PreReleaseType, cb?: () => void) => void
  loadingCreatePrerelease: boolean
  loadingApproval: boolean
  refetchWoList: <TPageData>(
    options?: RefetchOptions & RefetchQueryFilters<TPageData>
  ) => Promise<QueryObserverResult<ListResponse<WorkOrderType>, unknown>>
  refetchWoDetail: <TPageData>(
    options?: RefetchOptions & RefetchQueryFilters<TPageData>
  ) => Promise<QueryObserverResult<WorkOrderType, unknown>>
  isDetailPage: boolean
  isCreatePage: boolean
  isApprovalPage: boolean
  isUnitTakePage: boolean
}

const PreReleaseContext = createContext<PreReleaseContextProps>({} as PreReleaseContextProps)

export const usePreRelease = () => {
  const context = useContext(PreReleaseContext)
  if (context === undefined) {
    throw new Error('usePreRelease must be used within a PreReleaseContextProvider')
  }
  return context
}

export const PreReleaseProvider = ({ children }: { children: ReactNode }) => {
  const { isMobile } = useMobileScreen()
  const navigate = useNavigate()
  const { setConfirmState } = useMenu()
  const { pathname } = useLocation()
  const { userProfile } = useAuth()
  const params = useParams()

  const isApprovalPage = pathname?.includes('approval')
  const isUnitTakePage = pathname?.includes('unit-taking')
  const isCreatePage = pathname?.includes('create-pre-release')
  const isDetailPage = pathname?.includes('pre-release-created')

  const [woParams, setPartialWoParams, setWoParams] = usePartialState<WoParams>({
    page: 1,
    limit: 10
  })

  const [preReleaseParams, setPartialPreReleaseParams, setPreReleaseParams] = usePartialState<PreReleaseParams>({
    page: 1,
    limit: 10
  })

  const { data: preleaseList, refetch: refetchPreRelease } = useQuery({
    queryKey: [PRE_RELEASE_QUERY_LIST_KEY, JSON.stringify(preReleaseParams), isApprovalPage],
    queryFn: async () => {
      if (isApprovalPage) {
        return await PreReleaseQueryMethods.getPreReleasesToMe(preReleaseParams)
      }
      return await PreReleaseQueryMethods.getPreReleases(preReleaseParams)
    },
    placeholderData: defaultListData as ListResponse<PreReleaseType>
  })

  const { data: woList, refetch: refetchWoList } = useQuery({
    queryKey: ['WO_LIST_PARAMS_KEY', JSON.stringify(woParams), isApprovalPage, isUnitTakePage, isDetailPage],
    queryFn: () => {
      if (isUnitTakePage) {
        return RnMQueryMethods.getWoList({ ...woParams, status: WoStatus.READY_TO_RELEASE })
      }
      if (isApprovalPage) {
        return RnMQueryMethods.getWoList({ ...woParams, status: WoStatus.PRE_RELEASED })
      }
      if (isDetailPage) {
        return RnMQueryMethods.getWoList({ ...woParams, isAllSegmentsCompleted: true })
      }
      return RnMQueryMethods.getWoList({ ...woParams, isAllSegmentsCompleted: true, status: WoStatus.PRE_RELEASED })
    },
    placeholderData: defaultListData as ListResponse<WorkOrderType>
  })

  const { data: woDetail, refetch: refetchWoDetail } = useQuery({
    enabled: !!params?.woId,
    queryKey: [WO_QUERY_DETAIL_KEY, params?.woId],
    queryFn: () => RnMQueryMethods.getWoDetail(params?.woId)
  })

  const { data: woLogs } = useQuery({
    enabled: !!woDetail?.id,
    queryKey: [WO_LOGS_QUERY_KEY, woDetail?.id],
    queryFn: async () => {
      const res = await RnMQueryMethods.getWoDetailLogs(woDetail?.id, { limit: Number.MAX_SAFE_INTEGER })
      return res?.items ?? []
    }
  })

  const { mutate: CreatePreRelease, isLoading: loadingCreatePrerelease } = useCreatePreRelease()
  const { mutate: UpdatePreReleaseApprovalStatus, isLoading: loadingApproval } = useUpdatePreReleaseApprovalStatus()
  const { mutate: ReleaseUnit, isLoading: loadingUnitRelease } = useUnitRelease()

  const onSubmitPreRelease = (dto: PreReleaseDtoType, cb?: () => {}) => {
    setConfirmState({
      open: true,
      title: 'Buat Pre-Release',
      content: 'Apakah kamu yakin akan membuat pre-relase? Pastikan kamu sudah mengisi semua data dengan benar',
      confirmText: 'Buat Pre-Release',
      onConfirm: () => {
        CreatePreRelease(dto, {
          onSuccess: () => {
            toast.success('Pre-Release berhasil dibuat')
            refetchWoList()
            refetchPreRelease()
            cb?.()
            setTimeout(() => {
              navigate('/wo/pre-releases')
            }, 700)
          }
        })
      }
    })
  }

  const onApprovePreRelease = (preRelease: PreReleaseType, cb?: () => void) => {
    setConfirmState({
      open: true,
      title: 'Setujui Pre-Release',
      content:
        'Apakah kamu yakin akan menyetujui Pre-Release ini dan menyerahkan unit kembali ke operator site? Action ini tidak dapat diubah',
      confirmText: 'Setujui Pre-Release',
      onConfirm: () => {
        const ownApproval = preRelease?.approvals?.find(approval => approval.userId === userProfile?.id)
        UpdatePreReleaseApprovalStatus(
          {
            approvalId: ownApproval?.id,
            preReleaseId: preRelease.id,
            status: 'APPROVED'
          },
          {
            onSuccess: () => {
              toast.success('Pre-Release berhasil disetujui')
              cb?.()
            }
          }
        )
      }
    })
  }

  const onRejectPreRelease = (preRelease: PreReleaseType, cb?: () => void) => {
    let rejectionNote = ''
    setConfirmState({
      open: true,
      title: 'Tolak Pre-Release',
      content: (
        <div className='flex flex-col gap-2'>
          <Typography>Apakah kamu yakin akan menolak Pre-Release ini? Action ini tidak dapat diubah</Typography>
          <TextField onBlur={e => (rejectionNote = e.target.value)} label='Alasan' />
        </div>
      ) as any,
      confirmText: 'Tolak Pre-Release',
      onConfirm: () => {
        const ownApproval = preRelease?.approvals?.find(approval => approval.userId === userProfile?.id)
        UpdatePreReleaseApprovalStatus(
          {
            approvalId: ownApproval?.id,
            preReleaseId: preRelease.id,
            status: 'REJECTED',
            rejectionNote
          },
          {
            onSuccess: () => {
              toast.success('Pre-Release berhasil ditolak')
              cb?.()
            }
          }
        )
      }
    })
  }

  useEffect(() => {
    setPreReleaseParams({
      ...preReleaseParams,
      siteIds: userProfile?.sites?.map(site => site.id).join(',')
    })
    setWoParams({
      ...woParams,
      siteIds: userProfile?.sites?.map(site => site.id).join(',')
    })
  }, [userProfile])

  const value = {
    isMobile,
    navigate,
    woParams,
    setPartialWoParams,
    setWoParams,
    woList,
    woDetail,
    woLogs,
    loadingCreatePrerelease,
    loadingApproval,
    isDetailPage,
    isCreatePage,
    isApprovalPage,
    isUnitTakePage,
    preleaseList,
    setPartialPreReleaseParams,
    setPreReleaseParams,
    preReleaseParams,
    refetchWoList,
    refetchWoDetail,
    onSubmitPreRelease,
    onApprovePreRelease,
    onRejectPreRelease
  }

  return <PreReleaseContext.Provider value={value}>{children}</PreReleaseContext.Provider>
}
