import { Card, CardContent, Grid, Typography } from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import CompanyQueryMethods, { UNIT_QUERY_KEY } from '@/api/services/company/query'
import { usePreRelease } from '../../context/PreReleaseContext'
import { DEFAULT_CATEGORY } from '@/data/default/category'

const UnitDetail = () => {
  const { woDetail } = usePreRelease()

  const { data: unit } = useQuery({
    enabled: !!woDetail?.unitId,
    queryKey: [UNIT_QUERY_KEY, woDetail?.unitId],
    queryFn: () => CompanyQueryMethods.getUnit(woDetail?.unitId)
  })

  return (
    <>
      <Grid item xs={12}>
        <Card>
          <CardContent className='flex flex-col gap-4'>
            <Typography variant='h5'>Detil Unit</Typography>
            <div className='grid grid-cols-3 gap-2'>
              <div className='flex flex-col gap-1 items-start self-stretch relative  h-[45px] mb-4 bg-transparent'>
                <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                  <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                    Kode Unit
                  </small>
                </label>
                <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
                <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                  {unit?.number ?? '-'}
                </p>
              </div>
              <div className='flex flex-col gap-1 items-start self-stretch relative  h-[45px] mb-4 bg-transparent'>
                <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                  <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                    Kode Activa
                  </small>
                </label>
                <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
                <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                  {unit?.asset?.code ?? '-'}
                </p>
              </div>
              <div className='flex flex-col gap-1 items-start self-stretch relative  h-[45px] mb-4 bg-transparent'>
                <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                  <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                    Kategori Unit
                  </small>
                </label>
                <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
                <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                  {unit?.category?.name ?? DEFAULT_CATEGORY.name}
                </p>
              </div>
              <div className='flex flex-col gap-1 items-start self-stretch relative  h-[45px] mb-4 bg-transparent'>
                <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                  <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                    Jenis Unit
                  </small>
                </label>
                <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
                <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                  {unit?.subCategory?.name ?? '-'}
                </p>
              </div>
              <div className='flex flex-col gap-1 items-start self-stretch relative  h-[45px] mb-4 bg-transparent'>
                <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                  <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                    Type Equipment
                  </small>
                </label>
                <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
                <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                  {unit?.equipmentType ?? '-'}
                </p>
              </div>
              <div className='flex flex-col gap-1 items-start self-stretch relative  h-[45px] mb-4 bg-transparent'>
                <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                  <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                    Merk Unit
                  </small>
                </label>
                <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
                <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                  {unit?.brandName ?? '-'}
                </p>
              </div>
              <div className='flex flex-col gap-1 items-start self-stretch relative  h-[45px] mb-4 bg-transparent'>
                <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                  <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                    Tipe Unit
                  </small>
                </label>
                <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
                <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                  {unit?.type ?? '-'}
                </p>
              </div>
              <div className='flex flex-col gap-1 items-start self-stretch relative  h-[45px] mb-4 bg-transparent'>
                <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                  <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                    Nomor Lambung
                  </small>
                </label>
                <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
                <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                  {unit?.hullNumber ?? '-'}
                </p>
              </div>
              <div className='flex flex-col gap-1 items-start self-stretch relative  h-[45px] mb-4 bg-transparent'>
                <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                  <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                    Nomor Rangka
                  </small>
                </label>
                <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
                <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                  {unit?.chassisNumber ?? '-'}
                </p>
              </div>
              <div className='flex flex-col gap-1 items-start self-stretch relative  h-[45px] mb-4 bg-transparent'>
                <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                  <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                    Nomor Mesin
                  </small>
                </label>
                <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
                <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                  {unit?.engineNumber ?? '-'}
                </p>
              </div>
              <div className='flex flex-col gap-1 items-start self-stretch relative  h-[45px] mb-4 bg-transparent'>
                <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                  <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                    Plat Nomor
                  </small>
                </label>
                <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
                <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                  {unit?.plateNumber ?? '-'}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </Grid>
      <Grid item xs={12}>
        <Card>
          <CardContent className='flex flex-col gap-4'>
            <Typography variant='h5'>Data Unit</Typography>
            <div className='grid grid-cols-3 gap-x-2 gap-y-4'>
              <div className='flex flex-col gap-1 items-start self-stretch relative  h-[45px] mb-4 bg-transparent'>
                <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                  <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>KM</small>
                </label>
                <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
                <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                  {woDetail?.unitKm ?? '-'} KM
                </p>
              </div>
              <div className='flex flex-col gap-1 items-start self-stretch relative  h-[45px] mb-4 bg-transparent'>
                <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                  <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>HM</small>
                </label>
                <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
                <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                  {woDetail?.unitHm ?? '-'} Jam
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </Grid>
    </>
  )
}

export default UnitDetail
