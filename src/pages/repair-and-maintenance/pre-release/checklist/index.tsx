import * as Sentry from '@sentry/react'
import LoadingButton from '@mui/lab/LoadingButton'
import { Breadcrumbs, Grid, Typography } from '@mui/material'
import { Link, useParams } from 'react-router-dom'
import { usePreRelease } from '../context/PreReleaseContext'
import AttentionCard from '../../wo/create-pre-release/component/AttentionCard'
import WorkDetail from './component/WorkDetail'
import { useQuery } from '@tanstack/react-query'
import PreReleaseQueryMethods from '@/api/services/pre-release/query'
import { PRE_RELEASE_QUERY_KEY } from '@/api/services/pre-release/service'
import ApprovalListCard from '@/pages/material-request/create/components/ApprovalListCard'
import UnitDetail from './component/UnitDetail'
import CheckPointTable from './component/Checkpoint'
import NoteCard from './component/NoteCard'
import { DefaultApprovalScope } from '@/pages/user/default-approval/config/enum'
import { useAuth } from '@/contexts/AuthContext'
import UserQueryMethods, { DEFAULT_APPROVER_QUERY_KEY } from '@/api/services/user/query'
import { FormProvider, useForm } from 'react-hook-form'
import { PreReleaseChecklistDto, PreReleaseDtoType } from '@/types/preReleaseTypes'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { useEffect } from 'react'
import { useUpdatePreReleaseChecklist } from '@/api/services/pre-release/mutation'
import { toast } from 'react-toastify'

const PreReleaseChecklist = () => {
  const params = useParams()
  const { userProfile } = useAuth()
  const { woDetail, navigate, refetchWoDetail } = usePreRelease()

  const methods = useForm<PreReleaseChecklistDto>({
    resolver: zodResolver(
      z.object({
        note: z.string().min(1),
        checkPoints: z
          .array(
            z.object({
              id: z.number(),
              isChecked: z.boolean(),
              note: z.string()
            })
          )
          .min(1)
      })
    )
  })

  const { handleSubmit } = methods

  const { mutate: updatePreReleaseChecklist, isLoading: loadingUpdatePreReleaseChecklist } =
    useUpdatePreReleaseChecklist()

  const { data: pr } = useQuery({
    enabled: !!woDetail?.id,
    queryKey: [PRE_RELEASE_QUERY_KEY, woDetail?.id],
    queryFn: async () => {
      const res = await PreReleaseQueryMethods.getPreReleases({ workOrderId: woDetail?.id })
      return res.items?.[0] ?? null
    },
    placeholderData: null
  })

  const { data: prData, refetch: refetchPreRelease } = useQuery({
    enabled: !!pr?.id,
    queryKey: [PRE_RELEASE_QUERY_KEY, pr?.id],
    queryFn: async () => {
      const res = await PreReleaseQueryMethods.getOnePreRelease(pr?.id)
      return res
    }
  })

  const scope = DefaultApprovalScope.WorkOrderPreRelease

  const { data: approverList } = useQuery({
    enabled: !!woDetail?.siteId && !!userProfile?.departmentId,
    queryKey: [DEFAULT_APPROVER_QUERY_KEY, scope, woDetail?.siteId, userProfile?.departmentId],
    queryFn: () =>
      UserQueryMethods.getDefaultApproverList({
        limit: 1000,
        divisionId: 'null',
        scope,
        siteId: woDetail?.siteId,
        departmentId: 'null'
        // departmentId: woDetail?.departmentId ?? userProfile?.departmentId
      }),
    placeholderData: []
  })

  const onSubmitChecklist = (dto: PreReleaseChecklistDto) => {
    Sentry.captureMessage(`Submit Pre Release Checklist: ${JSON.stringify(dto)}`)
    updatePreReleaseChecklist(
      {
        prId: prData?.id,
        ...dto
      },
      {
        onSuccess: () => {
          toast.success('Pre-Release berhasil diupdate')
          refetchWoDetail()
          refetchPreRelease()
          setTimeout(() => {
            navigate('/wo/pre-releases/' + woDetail?.id)
          }, 700)
        }
      }
    )
  }

  useEffect(() => {
    if (prData?.checkPoints) {
      methods.reset({
        ...methods.getValues(),
        note: prData.note,
        checkPoints: prData.checkPoints?.map(item => ({
          id: item.id,
          isChecked: item?.isChecked ?? false,
          note: item?.note ?? '',
          name: item.templateCheckPoint.name,
          description: item.templateCheckPoint.description
        }))
      })
    }
  }, [prData])

  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Breadcrumbs aria-label='breadcrumb'>
          <Link to='#' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Work Order</Typography>
          </Link>
          <Link to='/wo/pre-releases' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Pengajuan Pre-Release</Typography>
          </Link>
          <Link to={'/wo/pre-releases/' + params?.woId} state={{ isFromPreRelease: true }} replace>
            <Typography color='var(--mui-palette-text-disabled)'>Detil Work Order</Typography>
          </Link>
          <Typography>Ajukan Pre-Release</Typography>
        </Breadcrumbs>
      </Grid>
      <Grid item xs={12}>
        <div className='flex justify-between items-end flex-col sm:flex-row max-sm:items-center gap-2'>
          <div className='flex flex-col'>
            <div className='flex items-center gap-2 flex-col sm:flex-row max-sm:text-center'>
              <Typography variant='h4'>Ajukan Pre-Release</Typography>
            </div>
            <Typography className='max-sm:text-center max-sm:mt-2'>
              Isi dokumen pre-release dan ajukan ke supervisor kamu
            </Typography>
          </div>
          <div className='flex gap-2 flex-col sm:flex-row is-full sm:is-auto'>
            <LoadingButton
              loading={loadingUpdatePreReleaseChecklist}
              startIcon={<></>}
              variant='contained'
              className='is-full sm:is-auto'
              onClick={handleSubmit(onSubmitChecklist, errors => {
                console.error(errors)
                Sentry.captureException(errors)
                Object.entries(errors).forEach(([field, error]) => {
                  toast.error(`${field}: ${error?.message}`, {
                    autoClose: 5000
                  })
                })
              })}
            >
              Ajukan Pre-Release
            </LoadingButton>
          </div>
        </div>
      </Grid>
      <Grid item xs={12}>
        <AttentionCard />
      </Grid>
      <FormProvider {...methods}>
        <Grid item xs={12}>
          <Grid container spacing={4}>
            <Grid item xs={12} md={6}>
              <Grid container spacing={4}>
                <Grid item xs={12}>
                  <WorkDetail prData={prData} />
                </Grid>
              </Grid>
            </Grid>
            <Grid item xs={12} md={6}>
              <Grid container spacing={4}>
                <UnitDetail />
              </Grid>
            </Grid>
          </Grid>
        </Grid>
        <Grid item xs={12}>
          <CheckPointTable />
        </Grid>
        <Grid item xs={12} md={6}>
          <NoteCard />
        </Grid>
        <Grid item xs={12} md={6}>
          <ApprovalListCard approverList={approverList?.map(approver => approver.user) ?? []} />
        </Grid>
      </FormProvider>
    </Grid>
  )
}

export default PreReleaseChecklist
