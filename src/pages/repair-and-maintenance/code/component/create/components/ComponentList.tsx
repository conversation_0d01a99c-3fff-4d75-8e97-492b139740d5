import DebouncedInput from '@/components/DebounceInput'
import { Box, Button, Card, FormHelperText, IconButton, Menu, MenuItem, Typography } from '@mui/material'
import { memo, MouseEvent, useCallback, useRef, useState } from 'react'
import { useComponent } from '../../context/CodeContext'
import OpenDialogOnElementClick from '@/components/dialogs/OpenDialogOnElementClick'
import AddRoleDialog from '@/components/dialogs/add-role-dialog'
import MobileDropDown from '@/components/layout/shared/components/MobileDropDown'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import { tableColumns } from '../config/table'
import Table from '@/components/table'
import { useFieldArray, useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { ComponentType, createComponentSchema } from '../config/schema'

const ComponentList = () => {
  const {
    componentParams: { search, page },
    codeFamilyList,
    fields,
    fieldArray
  } = useComponent()

  const [actionBtn, setBtn] = useState<boolean>(false)
  const [addAnchorEl, setAddAnchorEl] = useState<null | HTMLElement>(null)

  const { fields: items, append, remove } = fieldArray

  const table = useReactTable({
    data: items as any,
    columns: tableColumns({
      fields: fields,
      codeFamilies: codeFamilyList,
      onDelete: index => {
        remove(index)
      }
    }),
    initialState: {
      pagination: {
        pageSize: 10,
        pageIndex: page - 1
      }
    },
    manualPagination: true,
    rowCount: 0,
    pageCount: 1,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getFacetedMinMaxValues: getFacetedMinMaxValues()
  })

  const handleAddClose = () => {
    setAddAnchorEl(null)
  }

  const handleAddClick = useCallback((event: MouseEvent<HTMLButtonElement>) => {
    append({ code: null, description: null })
  }, [])

  const actionButton = <></>

  return (
    <>
      <Card>
        <div className='flex justify-between gap-4 p-5 flex-col items-start sm:flex-row sm:items-center'>
          <div className='flex gap-4 items-center is-full sm:is-auto flex-col sm:flex-row'>
            <Typography variant='h5'>List Component</Typography>
          </div>
          <Button variant='outlined' aria-haspopup='true' onClick={handleAddClick} className='is-full sm:is-auto'>
            Tambah List
          </Button>
        </div>
        <Table
          table={table}
          emptyLabel={
            <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
              <Typography> Belum ada Data</Typography>
              <Typography className='text-sm text-gray-400'>Semua data komponen akan ditampilkan di sini</Typography>
            </td>
          }
          disablePagination
          // onRowsPerPageChange={pageSize => {
          //   if (pageSize > totalItems) {
          //     setComponentParams(prev => ({ ...prev, limit: totalItems, page: 1 }))
          //   } else {
          //     setPartialComponentParams('limit', pageSize)

          //     const maxPage = Math.ceil(totalItems / pageSize)
          //     if (page > maxPage) {
          //       setComponentParams(prev => ({ ...prev, page: maxPage }))
          //     }
          //   }
          // }}
          // onPageChange={pageIndex => setPartialComponentParams('page', pageIndex)}
        />
        {(fields.formState.errors.items || fields.formState.errors.items?.root) && (
          <FormHelperText sx={{ my: 4 }} error>
            {fields.formState.errors.items?.root?.message || fields.formState.errors.items?.message}
          </FormHelperText>
        )}
      </Card>
      <MobileDropDown className='z-1' open={actionBtn} onClose={() => setBtn(false)} onOpen={() => setBtn(true)}>
        <Typography sx={{ marginTop: 2 }} align='center' variant='h5'>
          Action
        </Typography>
        <Box className='flex gap-2 p-4 pb-2 flex-col'>{actionButton}</Box>
      </MobileDropDown>
    </>
  )
}

export default memo(ComponentList)
