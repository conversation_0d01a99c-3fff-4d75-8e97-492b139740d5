import { createColumnHelper } from '@tanstack/react-table'
import { JobType } from '../../context/JobContext'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { Grid, IconButton, Typography } from '@mui/material'
import { CodeType } from '@/types/codes'

const columnHelper = createColumnHelper<CodeType>()

type RowAction = {
  detail: (item: CodeType) => void
  delete: (item: CodeType) => void
}

export const tableColumns = (rowActions?: RowAction) => [
  columnHelper.accessor('code', {
    header: 'JOB CODE'
  }),
  columnHelper.accessor('description', {
    header: 'DESKRIPSI'
  }),
  columnHelper.accessor('usage', {
    header: 'USAGE'
  }),
  // columnHelper.accessor('newLifeRecognition', {
  //   header: 'NEW LIFE RECOGNITION',
  //   cell: ({ row }) => (
  //     <Typography color={row.original.newLifeRecognition ? 'primary' : 'error'}>
  //       {row.original.newLifeRecognition ? 'Ya' : 'Tidak'}
  //     </Typography>
  //   )
  // }),
  // columnHelper.accessor('isUseStock', {
  //   header: 'PAKAI STOCK',
  //   cell: ({ row }) => (
  //     <Typography color={row.original.isUseStock ? 'primary' : 'error'}>
  //       {row.original.isUseStock ? 'Ya' : 'Tidak'}
  //     </Typography>
  //   )
  // }),
  columnHelper.accessor('createdAt', {
    header: 'TANGGAL DIBUAT',
    cell: ({ row }) => formatDate(row.original.createdAt, 'dd/MM/yyyy', { locale: id })
  }),
  columnHelper.accessor('id', {
    header: 'ACTION',
    enableSorting: false,
    cell: ({ row }) => (
      <Grid container spacing={1}>
        <IconButton onClick={() => rowActions.delete(row.original)}>
          <i className='ri-delete-bin-7-line text-googlePlus' />
        </IconButton>
        <IconButton onClick={() => rowActions.detail(row.original)}>
          <i className='ri-pencil-line' />
        </IconButton>
      </Grid>
    )
  })
]
