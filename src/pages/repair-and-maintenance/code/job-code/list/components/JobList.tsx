import DebouncedInput from '@/components/DebounceInput'
import { Box, Button, Card, IconButton, Menu, MenuItem, Typography } from '@mui/material'
import Permission from '@/core/components/Permission'
import { memo, MouseEvent, useCallback, useRef, useState } from 'react'
import { useJob } from '../../context/JobContext'
import OpenDialogOnElementClick from '@/components/dialogs/OpenDialogOnElementClick'
import AddRoleDialog from '@/components/dialogs/add-role-dialog'
import MobileDropDown from '@/components/layout/shared/components/MobileDropDown'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import { tableColumns } from '../config/table'
import Table from '@/components/table'
import { useMenu } from '@/components/menu/contexts/menuContext'

const JobCodeList = () => {
  const {
    jobCodeList: { items, totalItems, totalPages },
    componentParams: { search, page },
    setComponentParams,
    setPartialComponentParams,
    isMobile,
    navigate,
    handleEditJobCode,
    onDeleteHandler
  } = useJob()

  const { setConfirmState } = useMenu()

  const [searchExtend, setSearchExtend] = useState<boolean>(false)
  const [actionBtn, setBtn] = useState<boolean>(false)
  const [addAnchorEl, setAddAnchorEl] = useState<null | HTMLElement>(null)
  const open = Boolean(addAnchorEl)

  const [importDialogOpen, setImportDialogOpen] = useState(false)

  const handleRemoveJobCode = (id: string) => {
    setConfirmState({
      open: true,
      title: 'Hapus Job Code',
      content: 'Apakah kamu yakin ingin menghapus job code ini?',
      confirmText: 'Hapus',
      confirmColor: 'error',
      onConfirm: () => {
        onDeleteHandler(id)
      }
    })
  }

  const table = useReactTable({
    data: items ?? [],
    columns: tableColumns({
      detail: item => {
        handleEditJobCode(item)
      },
      delete: item => {
        handleRemoveJobCode(`${item.id}`)
      }
    }),
    initialState: {
      pagination: {
        pageSize: 10,
        pageIndex: page - 1
      }
    },
    manualPagination: true,
    rowCount: totalItems,
    pageCount: totalPages,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getFacetedMinMaxValues: getFacetedMinMaxValues()
  })

  const handleAddClose = () => {
    setAddAnchorEl(null)
  }

  const handleAddClick = useCallback((event: MouseEvent<HTMLButtonElement>) => {
    setAddAnchorEl(event.currentTarget)
    setBtn(false)
  }, [])

  const actionButton = (
    <></>
    // <Button
    //   color='secondary'
    //   variant='outlined'
    //   startIcon={<i className='ri-upload-2-line' />}
    //   className='is-full sm:is-auto'
    // >
    //   Ekspor
    // </Button>
  )

  return (
    <>
      <Card>
        <div className='flex justify-between gap-4 p-5 flex-row items-start sm:flex-row sm:items-center'>
          {searchExtend ? (
            <div className='flex gap-4 items-center is-full flex-col sm:flex-row'>
              <DebouncedInput
                value={search}
                onChange={value => setComponentParams(prev => ({ ...prev, page: 1, search: value as string }))}
                onBlur={() => setSearchExtend(false)}
                placeholder='Cari Job Code'
                className='is-full'
              />
            </div>
          ) : !isMobile ? (
            <div className='flex gap-4 items-center is-full sm:is-auto flex-col sm:flex-row'>
              <DebouncedInput
                value={search}
                onChange={value => setComponentParams(prev => ({ ...prev, page: 1, search: value as string }))}
                placeholder='Cari Job Code'
                className='is-full sm:is-auto'
              />
            </div>
          ) : (
            <IconButton onClick={() => setSearchExtend(true)} className='bg-[#4C4E640D]/5 rounded-[8px]'>
              <i className='ri-search-line' />
            </IconButton>
          )}
          {!searchExtend && (
            <div className='flex items-center justify-end md:justify-between gap-x-4 max-sm:gap-y-4 is-full flex-row sm:is-auto'>
              {!isMobile ? (
                <></>
              ) : (
                // actionButton
                <IconButton onClick={() => setBtn(true)} className='bg-[#4C4E640D]/5 rounded-[8px]'>
                  <i className='pepicons-pop--dots-y' />
                </IconButton>
              )}
              <Permission permission='code.write'>
                <Button
                  variant='contained'
                  aria-haspopup='true'
                  onClick={handleAddClick}
                  aria-expanded={open ? 'true' : undefined}
                  endIcon={<i className='ri-arrow-down-s-line' />}
                  aria-controls={open ? 'user-view-overview-export' : undefined}
                  className='sm:is-auto'
                >
                  Tambah
                </Button>
              </Permission>
              {isMobile ? (
                <MobileDropDown
                  className='z-10'
                  open={open}
                  onOpen={() => setAddAnchorEl(true as any)}
                  onClose={() => setAddAnchorEl(null)}
                >
                  <Typography sx={{ marginTop: 2 }} align='center' variant='h5'>
                    Action
                  </Typography>
                  <Box className='flex gap-2 p-4 pb-2 flex-col'>
                    {/* <Button
                      variant='outlined'
                      startIcon={<i className='mdi-file-document-outline size-5' />}
                      onClick={() => {
                        setImportDialogOpen(true)
                        handleAddClose()
                      }}
                    >
                      Impor dari Excel
                    </Button> */}
                    <Permission permission='code.write'>
                      <Button
                        variant='contained'
                        startIcon={<i className='ic-baseline-add-circle-outline size-5' />}
                        onClick={() => {
                          navigate('add')
                        }}
                      >
                        Tambah Manual
                      </Button>
                    </Permission>
                  </Box>
                </MobileDropDown>
              ) : (
                <Menu open={open} anchorEl={addAnchorEl} onClose={handleAddClose} id='user-view-overview-export'>
                  {/* <MenuItem
                    onClick={() => {
                      setImportDialogOpen(true)
                      handleAddClose()
                    }}
                    className='flex h-12'
                  >
                    <i className='mdi-file-document-outline size-5' />
                    Impor dari Excel
                  </MenuItem> */}
                  <Permission permission='code.write'>
                    <MenuItem
                      onClick={() => {
                        navigate('add')
                      }}
                      className='flex h-12'
                    >
                      <i className='ic-baseline-add-circle-outline size-5' />
                      Tambah Manual
                    </MenuItem>
                  </Permission>
                </Menu>
              )}
            </div>
          )}
        </div>
        <Table
          table={table}
          emptyLabel={
            <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
              <Typography> Belum ada Data</Typography>
              <Typography className='text-sm text-gray-400'>
                Semua data segment dan job code akan ditampilkan di sini
              </Typography>
            </td>
          }
          onRowsPerPageChange={pageSize => {
            if (pageSize > totalItems) {
              setComponentParams(prev => ({ ...prev, limit: totalItems, page: 1 }))
            } else {
              setPartialComponentParams('limit', pageSize)

              const maxPage = Math.ceil(totalItems / pageSize)
              if (page > maxPage) {
                setComponentParams(prev => ({ ...prev, page: maxPage }))
              }
            }
          }}
          onPageChange={pageIndex => setPartialComponentParams('page', pageIndex)}
        />
      </Card>
      <MobileDropDown className='z-1' open={actionBtn} onClose={() => setBtn(false)} onOpen={() => setBtn(true)}>
        <Typography sx={{ marginTop: 2 }} align='center' variant='h5'>
          Action
        </Typography>
        <Box className='flex gap-2 p-4 pb-2 flex-col'>{actionButton}</Box>
      </MobileDropDown>
    </>
  )
}

export default memo(JobCodeList)
