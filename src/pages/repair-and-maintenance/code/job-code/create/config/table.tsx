import { createColumnHelper } from '@tanstack/react-table'
import { CodeJobType, JobType } from '../../context/JobContext'
import {
  Box,
  Checkbox,
  FormControl,
  FormHelperText,
  Grid,
  IconButton,
  ListItemText,
  MenuItem,
  Select,
  TextField
} from '@mui/material'
import { type JobCodeType as FieldComponentType } from './schema'
import { Controller, UseFormReturn } from 'react-hook-form'

const columnHelper = createColumnHelper<FieldComponentType['items']>()

type RowAction = {
  onDelete: (index: number) => void
}

type TableColumnsType = {
  codeFamilies?: CodeJobType[]
  fields: UseFormReturn<FieldComponentType, any, undefined>
} & RowAction

export const tableColumns = (props: TableColumnsType) => {
  const { onDelete, codeFamilies, fields } = props
  return [
    columnHelper.accessor('code', {
      header: 'JOB CODE',
      size: 100,
      cell: ({ row }) => {
        return (
          <Box sx={{ width: '100%' }}>
            <Controller
              control={fields.control}
              name={`items.${row.index}.code`}
              render={({ field: { onChange, value }, fieldState: { error } }) => (
                <FormControl fullWidth>
                  <TextField
                    error={Boolean(error)}
                    size='small'
                    defaultValue={value}
                    onBlur={e => onChange(e.target.value)}
                  />
                </FormControl>
              )}
            />
          </Box>
        )
      }
    }),
    columnHelper.accessor('description', {
      header: 'DESCRIPTION',
      size: 250,
      cell: ({ row }) => {
        return (
          <Controller
            control={fields.control}
            name={`items.${row.index}.description`}
            render={({ field: { onChange, value }, fieldState: { error } }) => (
              <FormControl fullWidth>
                <TextField
                  error={Boolean(error)}
                  size='small'
                  defaultValue={value}
                  onBlur={e => onChange(e.target.value)}
                />
              </FormControl>
            )}
          />
        )
      }
    }),
    columnHelper.accessor('usage', {
      header: 'USAGE',
      size: 250,
      cell: ({ row }) => {
        return (
          <Controller
            control={fields.control}
            name={`items.${row.index}.usage`}
            render={({ field: { onChange, value }, fieldState: { error } }) => (
              <FormControl fullWidth>
                <TextField
                  error={Boolean(error)}
                  size='small'
                  defaultValue={value}
                  onBlur={e => onChange(e.target.value)}
                />
              </FormControl>
            )}
          />
        )
      }
    }),
    // columnHelper.accessor('newLifeRecognition', {
    //   header: 'New Life Recognition',
    //   size: 150,
    //   cell: ({ row }) => {
    //     return (
    //       <Controller
    //         control={fields.control}
    //         name={`items.${row.index}.newLifeRecognition`}
    //         render={({ field: { onChange, value }, fieldState: { error } }) => (
    //           <FormControl fullWidth>
    //             <Select
    //               error={Boolean(error)}
    //               id='numbering-divider'
    //               value={value}
    //               onChange={e => onChange(e.target.value)}
    //               size='small'
    //               inputProps={{
    //                 className: 'bg-white dark:bg-inherit'
    //               }}
    //               MenuProps={{
    //                 PaperProps: {
    //                   className: 'max-h-[200px] overflow-y-auto'
    //                 }
    //               }}
    //             >
    //               {[true, false].map((code, idx) => (
    //                 <MenuItem key={idx} value={String(code)}>
    //                   <ListItemText primary={code ? 'Ya' : 'Tidak'} />
    //                 </MenuItem>
    //               ))}
    //             </Select>
    //           </FormControl>
    //         )}
    //       />
    //     )
    //   }
    // }),
    // columnHelper.accessor('isUseStock', {
    //   header: 'Pakai Stok',
    //   size: 150,
    //   cell: ({ row }) => {
    //     return (
    //       <Controller
    //         control={fields.control}
    //         name={`items.${row.index}.isUseStock`}
    //         render={({ field: { onChange, value }, fieldState: { error } }) => (
    //           <FormControl fullWidth>
    //             <Select
    //               error={Boolean(error)}
    //               id='numbering-divider'
    //               value={value}
    //               onChange={e => onChange(e.target.value)}
    //               size='small'
    //               inputProps={{
    //                 className: 'bg-white dark:bg-inherit'
    //               }}
    //               MenuProps={{
    //                 PaperProps: {
    //                   className: 'max-h-[200px] overflow-y-auto'
    //                 }
    //               }}
    //             >
    //               {[true, false].map((code, idx) => (
    //                 <MenuItem key={idx} value={String(code)}>
    //                   <ListItemText primary={code ? 'Ya' : 'Tidak'} />
    //                 </MenuItem>
    //               ))}
    //             </Select>
    //           </FormControl>
    //         )}
    //       />
    //     )
    //   }
    // }),
    columnHelper.accessor('id', {
      header: '',
      size: 50,
      cell: ({ row }) => (
        <Grid container justifyContent='center' spacing={1}>
          <IconButton onClick={() => onDelete(row.index)}>
            <i className='ri-delete-bin-line text-googlePlus' />
          </IconButton>
        </Grid>
      )
    })
  ]
}
