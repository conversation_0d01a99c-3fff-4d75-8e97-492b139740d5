import * as z from 'zod'

export const createComponentSchema = z.object({
  items: z
    .array(
      z.object({
        code: z
          .string({ message: 'Wajib diisi' })
          .min(1, { message: 'Wajib diisi' })
          .regex(/^[^\s]+$/, { message: 'Spasi tidak diperbolehkan' }),
        description: z.string({ message: 'Wajib diisi' }).min(1, { message: 'Wajib diisi' }),
        usage: z.string({ message: 'Wajib diisi' }).min(1, { message: 'Wajib diisi' }),
        newLifeRecognition: z.string().optional(),
        isUseStock: z.string().optional()
      })
    )
    .min(1, { message: 'Minimal 1 item component' })
})

export type JobCodeType = Required<z.TypeOf<typeof createComponentSchema>>
