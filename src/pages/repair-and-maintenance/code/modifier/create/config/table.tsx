import { createColumnHelper } from '@tanstack/react-table'
import { ModifierType } from '../../context/ModifierContext'
import {
  Checkbox,
  FormControl,
  Grid,
  IconButton,
  InputLabel,
  ListItemText,
  MenuItem,
  Select,
  TextField
} from '@mui/material'
import { CodeComponentType } from '../../../component/context/CodeContext'
import { CodeType } from '@/types/codes'
import { Controller, UseFormReturn } from 'react-hook-form'
import { ModifierFieldType } from './schema'

const columnHelper = createColumnHelper<CodeType>()

type RowAction = {
  delete: (item: CodeType) => void
  codeFamilies: CodeType[]
  field: UseFormReturn<ModifierFieldType, any, undefined>
}

export const tableColumns = (rowAction: RowAction) => {
  return [
    columnHelper.accessor('code', {
      header: 'KODE',
      size: 100,
      cell: ({ row }) => {
        return (
          <Grid container>
            <Grid item xs={12}>
              <Controller
                control={rowAction.field.control}
                name={`items.${row.index}.code`}
                render={({ field: { onChange, value }, fieldState: { error } }) => (
                  <TextField size='small' onBlur={onChange} defaultValue={value} error={!!error} />
                )}
              />
            </Grid>
          </Grid>
        )
      }
    }),
    columnHelper.accessor('description', {
      header: 'DESKRIPSI',
      size: 250,
      cell: ({ row }) => {
        return (
          <Grid container>
            <Grid item xs={12}>
              <Controller
                control={rowAction.field.control}
                name={`items.${row.index}.description`}
                render={({ field: { onChange, value }, fieldState: { error } }) => (
                  <FormControl fullWidth>
                    <TextField size='small' onBlur={onChange} defaultValue={value} error={!!error} />
                  </FormControl>
                )}
              />
            </Grid>
          </Grid>
        )
      }
    }),
    // columnHelper.accessor('family', {
    //   header: 'FAMILY',
    //   size: 250,
    //   cell: ({ row }) => {
    //     return (
    //       <Controller
    //         control={rowAction.field.control}
    //         name={`items.${row.index}.family`}
    //         render={({ field: { onChange, value }, fieldState: { error } }) => {
    //           const codeFamilies = rowAction?.codeFamilies ?? []
    //           return (
    //             <FormControl fullWidth>
    //               <Select
    //                 error={Boolean(error)}
    //                 id='numbering-divider'
    //                 value={value}
    //                 renderValue={selected =>
    //                   selected?.map(f => codeFamilies?.find(c => String(c.id) === f)?.code).join(', ')
    //                 }
    //                 onChange={e => onChange(e.target.value)}
    //                 placeholder='Pilih Family'
    //                 multiple
    //                 size='small'
    //                 inputProps={{
    //                   className: 'bg-white dark:bg-inherit'
    //                 }}
    //                 MenuProps={{
    //                   PaperProps: {
    //                     className: 'max-h-[200px] overflow-y-auto'
    //                   }
    //                 }}
    //               >
    //                 {codeFamilies?.map(family => (
    //                   <MenuItem key={family.id} value={String(family.id)}>
    //                     <Checkbox checked={value?.includes(String(family.id))} />
    //                     <ListItemText primary={family.code} />
    //                   </MenuItem>
    //                 ))}
    //               </Select>
    //             </FormControl>
    //           )
    //         }}
    //       />
    //     )
    //   }
    // }),
    columnHelper.display({
      id: 'actions',
      header: '',
      size: 50,
      cell: ({ row }) => (
        <Grid container>
          <Grid item xs={12}>
            <IconButton onClick={() => rowAction.delete(row.original)}>
              <i className='ri-delete-bin-7-line text-textSecondary' />
            </IconButton>
          </Grid>
        </Grid>
      )
    })
  ]
}
