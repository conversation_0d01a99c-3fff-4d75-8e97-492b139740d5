import { createColumnHelper } from '@tanstack/react-table'
import { ModifierType } from '../../context/ModifierContext'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { Grid, IconButton } from '@mui/material'
import { CodeType } from '@/types/codes'

const columnHelper = createColumnHelper<CodeType>()

type RowAction = {
  delete: (item: CodeType) => void
  detail: (item: CodeType) => void
}

export const tableColumns = (rowAction: RowAction) => {
  return [
    columnHelper.accessor('code', {
      header: 'KODE'
    }),
    columnHelper.accessor('description', {
      header: 'DESKRIPSI'
    }),
    // columnHelper.accessor('family', {
    //   header: 'FAMILY',
    //   cell: ({ row }) => row.original.codeFamilies?.map(family => family.code).join(', ')
    // }),
    columnHelper.accessor('createdAt', {
      header: 'TANGGAL DIBUAT',
      cell: ({ row }) => formatDate(new Date(row.original.createdAt), 'dd/MM/yyyy', { locale: id })
    }),
    columnHelper.display({
      id: 'action',
      header: 'ACTION',
      cell: ({ row }) => (
        <Grid container spacing={1}>
          <IconButton onClick={() => rowAction.delete(row.original)}>
            <i className='ri-delete-bin-7-line text-textSecondary' />
          </IconButton>
          <IconButton onClick={() => rowAction.detail(row.original)}>
            <i className='ri-pencil-line' />
          </IconButton>
        </Grid>
      )
    })
  ]
}
