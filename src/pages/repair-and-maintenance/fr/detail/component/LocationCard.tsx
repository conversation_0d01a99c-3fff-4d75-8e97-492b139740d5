import { Card, CardContent, Typography } from '@mui/material'
import { useFrContext } from '../../context/FrContext'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'

const CardLocation = () => {
  const { activeFr } = useFrContext()

  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Lokasi</Typography>
        </div>
        <div className='flex flex-col gap-6'>
          <div className='grid grid-cols-1 md:grid-cols-2'>
            <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
              <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                  Lokasi Asal Unit
                </small>
              </label>
              <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
              <p className=''>{activeFr?.site?.name ?? '-'}</p>
            </div>
            <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
              <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>Pit</small>
              </label>
              <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
              <p className=''>{activeFr?.locationPit ? `Pit ${activeFr?.locationPit}` : '-'}</p>
            </div>
          </div>
          <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
            <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
              <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                Detil Lokasi
              </small>
            </label>
            <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
            <p className=''>{activeFr?.locationNote ?? '-'}</p>
          </div>
          <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
            <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
              <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                Dikerjakan oleh Workshop
              </small>
            </label>
            <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
            <p className=''>{activeFr?.destinationSite?.name ?? '-'}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default CardLocation
