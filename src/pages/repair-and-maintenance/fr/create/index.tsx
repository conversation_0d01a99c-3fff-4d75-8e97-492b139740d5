import * as Sentry from '@sentry/react'
import { useRouter } from '@/routes/hooks'
import { Breadcrumbs, Button, Chip, Grid, Typography } from '@mui/material'
import { Link } from 'react-router-dom'

import UnitDetail from './component/UnitDetail'
import CardUnitDetail from './component/CardUnitDetail'
import CardProblem from './component/CardProblem'
import CardSchedule from './component/CardSchedule'
import LocationCard from './component/LocationCard'
import { FormProvider, useForm } from 'react-hook-form'
import { createFrSchemaDto, FrDtoType } from './config/schema'
import { zodResolver } from '@hookform/resolvers/zod'
import { useFrContext } from '../context/FrContext'
import { toast } from 'react-toastify'

const UnitDetailPage = () => {
  const router = useRouter()
  const { onSubmitFieldReport, createFrLoading } = useFrContext()

  const method = useForm<FrDtoType>({
    resolver: zodResolver(createFrSchemaDto)
  })

  return (
    <>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <Breadcrumbs aria-label='breadcrumb'>
            <Link to='/fr/created' replace>
              <Typography color='var(--mui-palette-text-disabled)'>FR</Typography>
            </Link>
            <Typography>Buat FR</Typography>
          </Breadcrumbs>
        </Grid>
        <Grid item xs={12}>
          <div className='flex justify-between items-end flex-col sm:flex-row max-sm:items-center gap-2'>
            <div className='flex flex-col'>
              <div className='flex items-center gap-2 flex-col sm:flex-row max-sm:text-center'>
                <Typography variant='h4'>Buat FR</Typography>
              </div>
              <Typography className='max-sm:text-center max-sm:mt-2'>Tambahkan Field Report untuk unit</Typography>
            </div>
            <div className='flex gap-2 flex-col sm:flex-row is-full sm:is-auto'>
              <Link to={'/fr/created'} replace>
                <Button color='secondary' disabled={createFrLoading} variant='outlined' className='is-full sm:is-auto'>
                  Batalkan
                </Button>
              </Link>
              <Button
                variant='contained'
                disabled={createFrLoading}
                className='is-full sm:is-auto'
                onClick={method.handleSubmit(onSubmitFieldReport, errors => {
                  console.error(errors)
                  Sentry.captureException(errors)
                  Object.entries(errors).forEach(([field, error]) => {
                    toast.error(`${field}: ${error?.message}`, {
                      autoClose: 5000
                    })
                  })
                })}
              >
                Buat FR
              </Button>
            </div>
          </div>
        </Grid>
        <FormProvider {...method}>
          <Grid item xs={12} md={6}>
            <Grid container spacing={4}>
              <Grid item xs={12}>
                <UnitDetail unitData={{}} />
              </Grid>
              <Grid item xs={12}>
                <CardUnitDetail />
              </Grid>
            </Grid>
          </Grid>
          <Grid item xs={12} md={6}>
            <Grid container spacing={4}>
              <Grid item xs={12}>
                <LocationCard />
              </Grid>
              <Grid item xs={12}>
                <CardProblem />
              </Grid>
              <Grid item xs={12}>
                <CardSchedule />
              </Grid>
            </Grid>
          </Grid>
        </FormProvider>
      </Grid>
    </>
  )
}

export default UnitDetailPage
