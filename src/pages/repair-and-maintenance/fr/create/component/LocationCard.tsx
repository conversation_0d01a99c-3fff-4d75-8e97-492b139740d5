import { useMemo } from 'react'
import {
  Card,
  CardContent,
  Grid,
  Typography,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  FormHelperText,
  TextField,
  ListSubheader,
  ListItemText
} from '@mui/material'
import { useAuth } from '@/contexts/AuthContext'
import { useFormContext, Controller } from 'react-hook-form'
import { FrDtoType } from '../config/schema'
import NumberField from '@/components/numeric/NumberField'

const LocationCard = () => {
  const { userProfile, groupedSiteList } = useAuth()
  const { control } = useFormContext<FrDtoType>()

  const workshops = useMemo(() => userProfile?.sites?.filter(site => site.type === 'WORKSHOP') ?? [], [userProfile])

  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Lokasi</Typography>
        </div>
        <Grid container spacing={4}>
          <Grid item xs={12} md={6}>
            <Controller
              control={control}
              name='siteId'
              rules={{ required: true }}
              render={({ field: { value, onChange }, fieldState: { error } }) => (
                <FormControl fullWidth>
                  <InputLabel id='role-select'>Lokasi Unit</InputLabel>
                  <Select
                    key={value}
                    fullWidth
                    id='select-siteId'
                    value={value}
                    onChange={e => onChange(e.target.value)}
                    label='Lokasi Unit'
                    size='medium'
                    labelId='siteId-select'
                    inputProps={{ placeholder: 'Lokasi Unit' }}
                    defaultValue=''
                    error={!!error}
                  >
                    {groupedSiteList.map(group => {
                      let children = []
                      children.push(
                        <ListSubheader
                          className='bg-green-50 text-primary font-semibold'
                          key={group.projectId ?? 'no_project'}
                        >
                          {group.project?.name || 'Tanpa Proyek'}
                        </ListSubheader>
                      )
                      group.sites.forEach(site => {
                        children.push(
                          <MenuItem key={site.id} value={site.id}>
                            <ListItemText primary={site.name} />
                          </MenuItem>
                        )
                      })
                      return children
                    })}
                  </Select>
                </FormControl>
              )}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <Controller
              control={control}
              name='locationPit'
              render={({ field, fieldState: { error } }) => (
                <TextField
                  fullWidth
                  {...field}
                  {...(!!error && { error: true })}
                  label='Pit'
                  InputProps={{
                    inputComponent: NumberField as any,
                    inputProps: {
                      isAllowed: ({ floatValue }) => floatValue <= 999
                    }
                  }}
                />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='locationNote'
              render={({ field, fieldState: { error } }) => (
                <TextField fullWidth {...field} label='Detil Lokasi (Opsional)' placeholder='Detil Lokasi (Opsional)' />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='destinationSiteId'
              render={({ field: { value, onChange }, fieldState: { error } }) => (
                <FormControl fullWidth>
                  <InputLabel id='role-select'>Dikerjakan oleh Workshop</InputLabel>
                  <Select
                    key={value}
                    fullWidth
                    id='select-siteId'
                    value={value}
                    onChange={e => onChange(e.target.value)}
                    label='Dikerjakan oleh Workshop'
                    size='medium'
                    labelId='siteId-select'
                    inputProps={{ placeholder: 'Dikerjakan oleh Workshop' }}
                    defaultValue=''
                    error={!!error}
                  >
                    {workshops?.map(site => (
                      <MenuItem key={site.id} value={site.id}>
                        {site.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              )}
            />
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  )
}

export default LocationCard
