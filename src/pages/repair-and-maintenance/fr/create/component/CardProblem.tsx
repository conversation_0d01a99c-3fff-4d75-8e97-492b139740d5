import { useAuth } from '@/contexts/AuthContext'
import {
  Card,
  CardContent,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography
} from '@mui/material'
import { Controller, useFormContext } from 'react-hook-form'
import { FrDtoType } from '../config/schema'
import { useMemo } from 'react'
import { mrPriorityOptions } from '@/pages/repair-and-maintenance/wo/create-wo-mr/config/util'
import AppReactDatepicker from '@/components/libs/styles/AppReactDatepicker'
import { formatISO, toDate } from 'date-fns'

const CardProblem = () => {
  const { userProfile } = useAuth()
  const { control } = useFormContext<FrDtoType>()

  const items = useMemo(() => userProfile?.sites?.filter(site => site.type === 'WORKSHOP') ?? [], [userProfile])

  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Detil Masalah</Typography>
        </div>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='priority'
              render={({ field: { value, onChange }, fieldState: { error } }) => (
                <FormControl fullWidth>
                  <InputLabel id='priority-select'>Prioritas</InputLabel>
                  <Select
                    fullWidth
                    id='select-priority'
                    value={String(value)}
                    onChange={e => onChange(+e.target.value)}
                    label='Prioritas'
                    size='medium'
                    labelId='priority-select'
                    inputProps={{ placeholder: 'Prioritas' }}
                    defaultValue=''
                    error={!!error}
                  >
                    {mrPriorityOptions?.map(priority => (
                      <MenuItem key={priority.value} value={priority.value}>
                        <div className='flex items-center gap-2'>
                          <div className={`size-2 ${priority.color}`} />
                          {priority.label}
                        </div>
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              )}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <Controller
              control={control}
              name='breakdownDate'
              render={({ field: { value, onChange }, fieldState: { error } }) => (
                <AppReactDatepicker
                  boxProps={{ className: 'is-full' }}
                  selected={value ? toDate(value) : undefined}
                  onChange={(date: Date) => onChange(formatISO(date))}
                  dateFormat='dd/MM/yyyy'
                  maxDate={new Date()}
                  customInput={
                    <TextField fullWidth label='Tanggal Breakdown' placeholder='Pilih tanggal' error={!!error} />
                  }
                />
              )}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <Controller
              control={control}
              name='breakdownTime'
              render={({ field: { onChange, value }, fieldState: { error } }) => (
                <AppReactDatepicker
                  boxProps={{ className: 'is-full' }}
                  selected={value ? toDate(value) : undefined}
                  onChange={(date: Date) => onChange(date.toISOString())}
                  showTimeSelect
                  showTimeSelectOnly
                  dateFormat='HH:mm'
                  minDate={new Date()}
                  customInput={
                    <TextField
                      fullWidth
                      label='Jam Breakdown'
                      className='flex-1'
                      error={!!error}
                      InputProps={{
                        readOnly: true
                      }}
                    />
                  }
                />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='initialReport'
              render={({ field: { value, onChange }, fieldState: { error } }) => (
                <TextField
                  error={!!error}
                  label='Laporan Awal'
                  value={value}
                  onChange={onChange}
                  fullWidth
                  multiline
                  rows={3}
                />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='symptom'
              render={({ field: { value, onChange }, fieldState: { error } }) => (
                <TextField
                  error={!!error}
                  label='Sympton/Gejala (Opsional)'
                  value={value}
                  onChange={onChange}
                  fullWidth
                  multiline
                  rows={3}
                />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='cause'
              render={({ field: { value, onChange }, fieldState: { error } }) => (
                <TextField
                  error={!!error}
                  label='Penyebab (Opsional)'
                  value={value}
                  onChange={onChange}
                  fullWidth
                  multiline
                  rows={3}
                />
              )}
            />
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  )
}

export default CardProblem
