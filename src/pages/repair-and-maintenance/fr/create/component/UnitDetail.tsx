import { useState } from 'react'
import { Autocomplete, Card, CardContent, CircularProgress, debounce, Grid, TextField, Typography } from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import CompanyQueryMethods, { ITEM_LIST_QUERY_KEY } from '@/api/services/company/query'
import { ItemType, UnitType } from '@/types/companyTypes'
import { ListResponse } from '@/types/api'
import { defaultListData } from '@/api/queryClient'
import { Controller, useForm, useFormContext } from 'react-hook-form'
import { FrDtoType } from '../config/schema'
import { useFrContext } from '../../context/FrContext'
import { hoursToMinutes } from '../config/utils'
import { DEFAULT_CATEGORY } from '@/data/default/category'

type UnitDetailProps = {
  unitData?: any
}

const UnitDetail = ({ unitData }: UnitDetailProps) => {
  const { setSelectedUnit, selectedUnit } = useFrContext()
  const [unitSearchQuery, setUnitSearchQuery] = useState('')

  const { control, getValues, reset } = useFormContext<FrDtoType>()

  const {
    data: { items: itemList },
    isFetching: fetchItemsLoading,
    remove: removeItemList
  } = useQuery({
    enabled: !!unitSearchQuery,
    queryKey: [ITEM_LIST_QUERY_KEY, unitSearchQuery],
    queryFn: () => {
      return CompanyQueryMethods.getUnitList({
        ...(unitSearchQuery && { search: unitSearchQuery })
      })
    },
    placeholderData: defaultListData as ListResponse<UnitType>
  })

  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Detil Unit</Typography>
        </div>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='unitId'
              render={({ field: { onChange }, fieldState: { error } }) => (
                <Autocomplete
                  key={JSON.stringify({ foo: 'bar' })}
                  filterOptions={x => x}
                  isOptionEqualToValue={(option, value) => option.id === value.id}
                  onInputChange={debounce((e, newValue, reason) => {
                    if (reason === 'input') {
                      setUnitSearchQuery(newValue)
                    }
                  }, 700)}
                  options={itemList || []}
                  freeSolo={!unitSearchQuery}
                  fullWidth
                  value={selectedUnit}
                  onChange={(e, newValue: UnitType) => {
                    onChange(newValue?.id)
                    setSelectedUnit(newValue)
                    const [hour, mnts] = hoursToMinutes(newValue?.hm ?? 0)
                    reset({
                      ...getValues(),
                      unitKm: newValue?.km ?? 0,
                      unitHm: hour ?? 0,
                      unitMnt: mnts ?? 0
                    })
                    removeItemList()
                  }}
                  noOptionsText='Unit tidak ditemukan'
                  loading={fetchItemsLoading}
                  renderInput={params => (
                    <TextField
                      {...params}
                      label=''
                      placeholder='Cari kode atau nomor lambung'
                      variant='outlined'
                      fullWidth
                      InputProps={{
                        ...params.InputProps,
                        startAdornment: <i className='ri-search-line text-textSecondary size-5 mx-2' />,
                        endAdornment: <>{fetchItemsLoading ? <CircularProgress /> : null}</>,
                        onKeyDown: e => {
                          if (e.key === 'Enter') {
                            e.stopPropagation()
                          }
                        }
                      }}
                      error={!!error}
                    />
                  )}
                  getOptionLabel={(option: UnitType) =>
                    `${option.number} | ${option?.type} | ${option?.category?.name ?? DEFAULT_CATEGORY.name}`
                  }
                  renderOption={(props, option) => {
                    const { key, ...optionProps } = props
                    return (
                      <li key={key} {...optionProps}>
                        <Typography>
                          {option.number} | {option.type} | {option.category?.name ?? DEFAULT_CATEGORY.name}
                        </Typography>
                      </li>
                    )
                  }}
                />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <TextField disabled fullWidth label='Kode Unit' value={selectedUnit?.number ?? '-'} />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              disabled
              fullWidth
              label='Kategori Unit'
              value={selectedUnit?.category?.name ?? DEFAULT_CATEGORY.name}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField disabled fullWidth label='Merk Unit' value={selectedUnit?.brandName ?? '-'} />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField disabled fullWidth label='Tipe Unit' value={selectedUnit?.type ?? '-'} />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField disabled fullWidth label='Nomor Lambung' value={selectedUnit?.hullNumber ?? '-'} />
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  )
}

export default UnitDetail
