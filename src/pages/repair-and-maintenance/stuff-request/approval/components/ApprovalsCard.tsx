// MUI Imports
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'

// Type Imports
import { But<PERSON>, Chip, TextField } from '@mui/material'
import { ThemeColor } from '@/core/types'
import { useMenu } from '@/components/menu/contexts/menuContext'
import { toast } from 'react-toastify'
import { useAuth } from '@/contexts/AuthContext'
import { useState } from 'react'
import { ApproverType } from '@/types/userTypes'
import { formatDate } from 'date-fns'
import { id as idlang } from 'date-fns/locale'
import { useUpdateStuffApprovalStatus } from '@/api/services/wp/mutation'
import { useStuffContext } from '../../context/StuffContext'
import ConfirmDialog from '@/components/dialogs/confirm-dialog'
import { useFilePicker } from 'use-file-picker'
import ApproveDialogConfirm from './ApproveDialogConfirm'
import { useUploadImage } from '@/api/services/file/mutation'

type StatusChipType = {
  label: string
  color: ThemeColor
}

// Vars
export const statusChipValue: { [key: string]: StatusChipType } = {
  WAITING: { label: 'Menunggu', color: 'secondary' },
  PENDING: { label: 'Menunggu', color: 'secondary' },
  APPROVED: { label: 'Disetujui', color: 'success' },
  REJECTED: { label: 'Ditolak', color: 'error' }
}

const ApprovalsCard = ({ isCancelation, type }: { isCancelation?: boolean; type: 'TAKE' | 'RETURN' }) => {
  const { userProfile } = useAuth()
  const { setConfirmState } = useMenu()
  const { stuffData, refetchStuffData, refetchStuffList } = useStuffContext()

  const [{ uploadId, takenBy, id }, setField] = useState<{ takenBy: string; uploadId: string; id?: number }>({
    takenBy: '',
    uploadId: ''
  })
  const [confirmDialog, setConfirmDialog] = useState<boolean>(false)
  const { mutate: updStatusMutate, isLoading: loadingApproval } = useUpdateStuffApprovalStatus()
  const { mutateAsync: uploadImage, isLoading: loadingUpload } = useUploadImage()
  const { filesContent, openFilePicker } = useFilePicker({
    accept: 'image/*',
    multiple: false,
    readAs: 'DataURL'
  })

  const isLoading = loadingApproval || loadingUpload
  const updateStatusMutate = updStatusMutate

  const [, setEditApproverModalState] = useState<{
    open: boolean
    selectedApproval?: ApproverType
  }>({
    open: false
  })

  const approvalList = stuffData?.approvals ?? []

  let ownApprovalIndex = -1
  const ownApproval = stuffData?.approvals?.find((approval, index) => {
    ownApprovalIndex = index
    return approval.userId === userProfile?.id
  })

  const handleConfirmApproval = (dto: { takenBy: string; uploadId: string; id?: number }) => {
    uploadImage({
      fieldName: 'imageProofUploadId' + dto.id,
      file: dto.uploadId,
      scope: 'public-image',
      fileName: dto.takenBy + dto.id + '.png'
    }).then(res => {
      updateStatusMutate(
        {
          stuffId: stuffData?.id,
          approvalId: id,
          status: 'APPROVED',
          doneBy: dto.takenBy,
          imageProofUploadId: res.data.id
        },
        {
          onSuccess: () => {
            toast.success('MRO berhasil disetujui')
            refetchStuffData()
            refetchStuffList()
            setConfirmDialog(false)
          }
        }
      )
    })
  }

  const handleApprove = (id: number) => {
    setConfirmState({
      open: true,
      title: 'Setujui' + (type === 'TAKE' ? ' Pengambilan' : ' Pengembalian'),
      content: `Apakah kamu yakin akan menyetujui ${type === 'TAKE' ? 'Pengajuan Pengambilan MRO' : 'Pengajuan Pengembalian MRO'} ini? Action ini tidak bisa diubah`,
      confirmText: 'Setujui',
      onConfirm: () => {
        setConfirmDialog(true)
        setField(curr => ({ ...curr, id }))
      }
    })
  }

  const handleReject = (id: number) => {
    setConfirmState({
      open: true,
      title: 'Tolak' + (type === 'TAKE' ? ' Pengambilan' : ' Pengembalian'),
      content: `Apakah kamu yakin akan menolak ${type === 'TAKE' ? 'Pengajuan Pengambilan MRO' : 'Pengajuan Pengembalian MRO'} ini? Action ini tidak bisa diubah`,
      confirmText: 'Tolak',
      onConfirm: () => {
        updateStatusMutate(
          {
            stuffId: stuffData?.id,
            approvalId: id,
            status: 'REJECTED'
          },
          {
            onSuccess: () => {
              toast.success('MRO berhasil ditolak')
              refetchStuffData()
              refetchStuffList()
            }
          }
        )
      },
      confirmColor: 'error'
    })
  }

  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-6'>
          <div className='flex justify-between items-center'>
            <Typography variant='h5'>{isCancelation ? 'Persetujuan Pembatalan' : 'Pengajuan Persetujuan'}</Typography>
          </div>
          <div className='flex flex-col gap-4'>
            {approvalList.map(approval => {
              const statusValue = statusChipValue[approval.status]
              return (
                <div
                  key={approval.id}
                  className='rounded-lg border border-[#4c4e64]/22 p-4 flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'
                >
                  <div className='flex justify-between items-start self-stretch relative w-full bg-transparent'>
                    <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                      {approval.user?.fullName}
                    </p>
                    {ownApproval?.status === 'WAITING' &&
                    approval.status === 'WAITING' &&
                    stuffData?.status !== 'CANCELED' ? (
                      <div className='flex gap-2 items-center self-center'>
                        <Button
                          variant='contained'
                          size='small'
                          color='error'
                          onClick={() => handleReject(approval.id)}
                        >
                          Tolak
                        </Button>
                        <Button variant='contained' size='small' onClick={() => handleApprove(approval.id)}>
                          Setujui
                        </Button>
                      </div>
                    ) : (
                      <Chip label={statusValue?.label} color={statusValue?.color} variant='tonal' size='small' />
                    )}
                  </div>
                  <div className='flex justify-between items-start w-full'>
                    <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                      <small className='text-sm text-[#4c4e64]/60 dark:text-inherit'>{approval.user?.title}</small>
                    </label>
                    {approval.respondedAt ? (
                      <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                        <small className='text-sm text-[#4c4e64]/60 dark:text-inherit'>
                          {formatDate(approval.respondedAt, 'eeee, dd/MM/yyyy, HH:mm', { locale: idlang })}
                        </small>
                      </label>
                    ) : null}
                  </div>
                  {!!approval?.rejectionNote && (
                    <div>
                      <small>Catatan:</small>
                      <Typography>{approval?.rejectionNote}</Typography>
                    </div>
                  )}
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>
      {confirmDialog && (
        <ApproveDialogConfirm
          open={confirmDialog}
          setOpen={open => setConfirmDialog(open)}
          type={type}
          handleSubmit={handleConfirmApproval}
          id={id}
          loading={isLoading}
        />
      )}
    </>
  )
}

export default ApprovalsCard
