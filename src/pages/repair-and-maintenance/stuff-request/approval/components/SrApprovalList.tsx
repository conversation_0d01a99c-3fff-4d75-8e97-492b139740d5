import { Button, Card, Typography } from '@mui/material'
import { useStuffContext } from '../../context/StuffContext'
import DebouncedInput from '@/components/DebounceInput'
import FilterGroupDialog, { FilterGroupConfig, FilterValues } from '@/components/layout/shared/filter/FilterGroup'
import { StuffRequestParams, StuffRequestType } from '@/types/wpTypes'
import { useEffect, useState } from 'react'
import {
  getCoreRowModel,
  getSortedRowModel,
  getPaginationRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  useReactTable,
  getFacetedMinMaxValues
} from '@tanstack/react-table'
import { tablecolumns } from '../config/table'
import Table from '@/components/table'
import { useLocation } from 'react-router-dom'
import { stuffStatusOptions } from '../config/utils'
import { useAuth } from '@/contexts/AuthContext'

const SrApprovalList = () => {
  const { pathname } = useLocation()
  const { ownSiteList, departmentList } = useAuth()
  const { router, isMobile, stuffListResponse, stuffParams, setPartialStuffParam, setStuffParams } = useStuffContext()
  const { page, search, status, startDate, endDate, siteIds, departmentId } = stuffParams

  const [filterGroupConfig, setFilterGroupConfig] = useState<FilterGroupConfig>({})

  const table = useReactTable({
    data: stuffListResponse?.items ?? [],
    columns: tablecolumns({
      detail: row => router.push(`${pathname}/${row.id}`)
    }),
    initialState: {
      pagination: {
        pageSize: stuffParams.limit ?? 10,
        pageIndex: page - 1
      }
    },
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getFacetedMinMaxValues: getFacetedMinMaxValues()
  })

  const onFilterChanged = ({ date, status, unit, department }: FilterValues) => {
    setStuffParams(prev => {
      return {
        ...prev,
        page: 1,
        startDate: date?.[0],
        endDate: date?.[1],
        departmentId: department.length > 0 ? department[0] : undefined,
        status: status.length > 0 ? (status[0] as StuffRequestParams['status']) : undefined,
        unitId: unit.length > 0 ? unit[0] : undefined
      }
    })
  }

  useEffect(() => {
    setFilterGroupConfig({
      date: {
        options: [],
        values: [startDate, endDate]
      },
      status: {
        options: stuffStatusOptions,
        values: status ? [status] : []
      },
      site: {
        options: ownSiteList.map(site => {
          return { value: site.id, label: site.name }
        }),
        values: siteIds ? [siteIds] : []
      },
      department: {
        options: departmentList?.map(department => ({ value: department.id, label: department.name })) ?? [],
        values: departmentId ? [departmentId] : []
      }
    })
  }, [stuffParams])

  return (
    <Card>
      <div className='flex justify-between gap-4 p-5 flex-col items-start sm:flex-row items-center'>
        <div className='flex gap-4 items-center flex-col sm:flex-row is-full sm:is-auto'>
          <DebouncedInput
            value={search}
            onChange={value => setStuffParams(prev => ({ ...prev, page: 1, search: value as string }))}
            placeholder='Cari Material Request'
            className='is-full sm:w-[240px]'
          />
          <FilterGroupDialog
            config={filterGroupConfig}
            onFilterApplied={onFilterChanged}
            onRemoveFilter={onFilterChanged}
          />
        </div>
        <div className='flex items-center gap-x-4 max-sm:gap-y-4 is-full flex-col sm:is-auto sm:flex-row'>
          {/* <Button
            color='secondary'
            variant='outlined'
            startIcon={<i className='ri-upload-2-line' />}
            className='is-full sm:is-auto'
          >
            Ekspor
          </Button> */}
        </div>
      </div>
      <Table
        table={table}
        emptyLabel={
          <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
            <Typography>Belum ada Material Request</Typography>
            <Typography className='text-sm text-gray-400'>
              Semua material request yang telah dibuat akan ditampilkan di sini
            </Typography>
          </td>
        }
        onRowsPerPageChange={pageSize => {
          if (pageSize > stuffListResponse.totalItems) {
            setStuffParams(prev => ({ ...prev, limit: stuffListResponse.totalItems, page: 1 }))
          } else {
            setPartialStuffParam('limit', pageSize)

            const maxPage = Math.ceil(stuffListResponse.totalItems / pageSize)
            if (page > maxPage) {
              setStuffParams(prev => ({ ...prev, page: maxPage }))
            }
          }
        }}
        onPageChange={pageIndex => setPartialStuffParam('page', pageIndex)}
      />
    </Card>
  )
}

export default SrApprovalList
