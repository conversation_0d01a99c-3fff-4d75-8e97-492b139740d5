import { <PERSON>complete, Card, CardContent, CircularProgress, debounce, Grid, <PERSON><PERSON>ield, Typo<PERSON> } from '@mui/material'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import { useMemo, useState } from 'react'
import { laborColumns } from '../config/table'
import Table from '@/components/table'
import { Controller, useFieldArray, useFormContext } from 'react-hook-form'
import { CreateWpDto } from '@/types/wpTypes'
import { CreateWpDtoType } from '..'
import { useQuery } from '@tanstack/react-query'
import UserQueryMethods from '@/api/services/user/query'
import { UserType } from '@/types/userTypes'
import { mergeArrays } from '@/utils/helper'

const LaborCard = () => {
  const [laborList, setLabors] = useState<UserType[]>([])
  const [laborSearchQuery, setLaborSearchQuery] = useState('')
  let fetchItemsLoading = false

  const { control, setValue, getValues, reset } = useFormContext<CreateWpDtoType>()

  const { data: labors, remove: removeLabor } = useQuery({
    enabled: !!laborSearchQuery,
    queryKey: ['LABOR_LIST_QUERY_KEY', laborSearchQuery],
    queryFn: async () => {
      const res = await UserQueryMethods.getUserList({
        page: 1,
        limit: Number.MAX_SAFE_INTEGER,
        search: laborSearchQuery
      })
      return res.items
    },
    placeholderData: [] as UserType[]
  })

  const tableOptions = useMemo(
    () => ({
      data: laborList ?? [],
      columns: laborColumns({
        onDelete: (id: string) => {
          reset({
            ...getValues(),
            laborIds: getValues('laborIds')?.filter(item => item !== id)
          })
          setLabors(current => current?.filter(item => item.id !== id))
        }
      }),
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [laborList]
  )

  const table = useReactTable<any>(tableOptions)

  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Labor</Typography>
        </div>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='laborIds'
              render={({ fieldState: { error } }) => {
                return (
                  <Autocomplete
                    key={JSON.stringify(laborList)}
                    options={labors ?? []}
                    onInputChange={debounce((e, newValue, reason) => {
                      if (reason === 'input') {
                        setLaborSearchQuery(newValue)
                      }
                    }, 700)}
                    value={null}
                    freeSolo={!laborSearchQuery}
                    fullWidth
                    onChange={(e, newValue: UserType) => {
                      setLaborSearchQuery('')
                      setLabors(current => mergeArrays(current, [newValue], 'id'))
                      reset({
                        ...getValues(),
                        laborIds: [...new Set(getValues('laborIds').concat(newValue.id))]
                      })
                      removeLabor()
                    }}
                    noOptionsText='Labor tidak ditemukan'
                    loading={fetchItemsLoading}
                    renderInput={params => (
                      <TextField
                        {...params}
                        error={!!error}
                        label=''
                        placeholder='Cari nama mekanik'
                        variant='outlined'
                        fullWidth
                        InputProps={{
                          ...params.InputProps,
                          startAdornment: <i className='ri-search-line text-textSecondary size-5 mx-2' />,
                          endAdornment: <>{fetchItemsLoading ? <CircularProgress /> : null}</>,
                          onKeyDown: e => {
                            if (e.key === 'Enter') {
                              e.stopPropagation()
                            }
                          }
                        }}
                      />
                    )}
                    getOptionLabel={(option: UserType) => `${option.fullName} | ${option.title}`}
                    renderOption={(props, option) => {
                      const { key, ...optionProps } = props
                      return (
                        <li key={key} {...optionProps}>
                          <Typography>
                            {option.fullName} | {option.title}
                          </Typography>
                        </li>
                      )
                    }}
                  />
                )
              }}
            />
          </Grid>
          <Grid item xs={12}>
            {laborList.length === 0 ? (
              <div className='flex flex-col gap-3 py-14 items-center justify-center'>
                <Typography variant='h5'>Belum ada labor</Typography>
                <Typography variant='caption'>
                  List labor yang akan mengerjakan WP ini akan ditampilkan di sini
                </Typography>
              </div>
            ) : (
              <div className='shadow-sm rounded-[8px]'>
                <Table
                  table={table}
                  emptyLabel={
                    <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
                      <Typography> Belum ada Data</Typography>
                    </td>
                  }
                  disablePagination
                />
              </div>
            )}
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  )
}

export default LaborCard
