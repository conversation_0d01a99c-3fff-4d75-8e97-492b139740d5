import { createColumnHelper } from '@tanstack/react-table'
import { SegmentType } from '../../context/WpContext'
import { Checkbox, FormControlLabel, IconButton, Radio } from '@mui/material'
import { UserOutlineType, UserType } from '@/types/userTypes'
import { WoSegmentType } from '@/types/woTypes'
import truncateString from '@/core/utils/truncate'

const columnHelper = createColumnHelper<WoSegmentType>()

type RowActionType = {
  onDetail?: (id: string) => void
  onDelete?: (id: string) => void
  onSelectedId?: (id: string) => void
  woId?: string
  woIds?: string[]
  isDetail?: boolean
  single?: boolean
}

export const segmentTable = (rowAction: RowActionType) => {
  const { single = false } = rowAction
  return [
    columnHelper.accessor('id', {
      header: 'NO',
      size: 50,
      cell: ({ row }) => row.index + 1
    }),
    columnHelper.accessor('jobCode.code', {
      header: 'JOB CODE',
      cell: ({ row: { original } }) =>
        original?.jobCode?.code ? `${original.jobCode.code} | ${truncateString(original.jobCode.description, 12)}` : '-'
    }),
    columnHelper.accessor('componentCode.code', {
      header: 'SMCS',
      cell: ({ row: { original } }) =>
        original?.componentCode?.code
          ? `${original.componentCode.code} | ${truncateString(original.componentCode.description, 12)}`
          : '-'
    }),
    columnHelper.accessor('modifierCode.code', {
      header: 'MODIFIER',
      cell: ({ row: { original } }) =>
        original?.modifierCode?.code
          ? `${original.modifierCode.code} | ${truncateString(original.modifierCode.description, 12)}`
          : '-'
    }),
    columnHelper.accessor('componentsCount', {
      header: 'PARTS',
      cell: ({ row }) => `${row.original.componentsCount} Parts`
    }),
    columnHelper.accessor('miscellaneousCount', {
      header: 'MISCELLANEOUS',
      cell: ({ row }) => `${row.original.miscellaneousCount} Miscs`
    }),
    columnHelper.display({
      id: 'detail',
      header: 'DETIL',
      enableSorting: false,
      cell: ({ row }) => (
        <IconButton onClick={() => rowAction.onDetail(row.original.id)}>
          <i className='ri-eye-line text-secondary' />
        </IconButton>
      )
    }),
    ...(!rowAction.isDetail
      ? [
          columnHelper.display({
            id: 'select',
            header: 'PILIH',
            enableSorting: false,
            cell: ({ row }) => {
              if (!!single) {
                return (
                  <Checkbox
                    size='small'
                    disabled={row.original.status !== 'CREATED'}
                    checked={rowAction?.woId === row.original.id}
                    onClick={e => rowAction.onSelectedId(row.original.id)}
                  />
                )
              }
              return (
                <Checkbox
                  size='small'
                  disabled={row.original.status !== 'CREATED'}
                  checked={rowAction?.woIds?.includes(row.original.id)}
                  onClick={e => rowAction.onSelectedId(row.original.id)}
                />
              )
            }
          })
        ]
      : [])
  ]
}

const laborHelper = createColumnHelper<UserType>()

export const laborColumns = (rowAction: RowActionType) => [
  laborHelper.display({
    id: 'numbering',
    header: 'NO',
    cell: ({ row }) => row.index + 1
  }),
  laborHelper.accessor('fullName', {
    header: 'NAMA'
  }),
  laborHelper.accessor('title', {
    header: 'JABATAN'
  }),
  laborHelper.display({
    id: 'delete',
    header: '',
    cell: ({ row }) => (
      <IconButton onClick={() => rowAction.onDelete(row.original.id)}>
        <i className='x-delete-icon text-error' />
      </IconButton>
    )
  })
]
