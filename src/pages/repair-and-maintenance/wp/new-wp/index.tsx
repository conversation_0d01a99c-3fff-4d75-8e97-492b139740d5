import * as z from 'zod'
import { Grid, Typography, <PERSON>readcrum<PERSON>, Button } from '@mui/material'
import { Link } from 'react-router-dom'
import { useWp } from '../context/WpContext'
import SegmentCard from './component/SegmentCard'
import LaborCard from './component/LaborCard'
import EstimatedCard from './component/EstimatedCard'
import { FormProvider, useForm } from 'react-hook-form'
import { CreateWpDto } from '@/types/wpTypes'
import { zodResolver } from '@hookform/resolvers/zod'
import LoadingButton from '@mui/lab/LoadingButton'
import Permission from '@/core/components/Permission'
import * as Sentry from '@sentry/react'
import { toast } from 'react-toastify'
import { useEffect } from 'react'

const createWpSchema = z.object({
  workOrderId: z.string().uuid(),
  workOrderSegmentIds: z.array(z.string().uuid()).min(1, { message: 'Pilih minimal satu segmen' }),
  laborIds: z.array(z.string().uuid()).min(1, { message: 'Pilih minimal satu Labor' }),
  estimatedEndedAt: z.string({ message: 'Wajib diisi' }),
  estimatedHourEndedAt: z.string().optional().nullable()
})

export type CreateWpDtoType = z.infer<typeof createWpSchema>

const CreateNewWp = () => {
  const { navigate, onSubmitWp, isCreateWpLoading, selectedWo } = useWp()

  const methods = useForm<CreateWpDto>({
    defaultValues: {
      laborIds: [],
      workOrderSegmentIds: []
    },
    resolver: zodResolver(createWpSchema),
    mode: 'all'
  })

  const { reset, handleSubmit } = methods

  useEffect(() => {
    if (selectedWo) {
      reset({
        workOrderId: selectedWo?.id
      })
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedWo])

  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Breadcrumbs aria-label='breadcrumb'>
          <Link to='/wp/list' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Work Process</Typography>
          </Link>
          <Typography>Buat Work Process</Typography>
        </Breadcrumbs>
      </Grid>
      <Grid item xs={12}>
        <div className='flex justify-between items-end flex-col sm:flex-row gap-2 max-sm:items-center'>
          <div className='flex flex-col max-sm:text-center'>
            <Typography variant='h4'>Buat Work Process</Typography>
            <Typography>Tambahkan Pekerjaan untuk di assign ke mekanik</Typography>
          </div>
          <div className='flex flex-col sm:flex-row gap-2 is-full sm:is-auto'>
            <Button color='secondary' variant='outlined' disabled={isCreateWpLoading} onClick={() => navigate(-1)}>
              Batalkan
            </Button>
            <Permission permission={['work-process.create', 'work-process.update']}>
              <LoadingButton
                startIcon={<></>}
                loading={isCreateWpLoading}
                variant='contained'
                onClick={handleSubmit(onSubmitWp, errors => {
                  console.error(errors)
                  Sentry.captureException(errors)
                  Object.entries(errors).forEach(([field, error]) => {
                    toast.error(`${field}: ${error?.message}`, {
                      autoClose: 5000
                    })
                  })
                })}
              >
                Buat Work Process
              </LoadingButton>
            </Permission>
          </div>
        </div>
      </Grid>
      <FormProvider {...methods}>
        <Grid item xs={12}>
          <Grid container spacing={4}>
            <Grid item xs={12}>
              <SegmentCard />
            </Grid>

            <Grid item xs={12} md={6}>
              <LaborCard />
            </Grid>
            <Grid item xs={12} md={6}>
              <EstimatedCard />
            </Grid>
          </Grid>
        </Grid>
      </FormProvider>
    </Grid>
  )
}

export default CreateNewWp
