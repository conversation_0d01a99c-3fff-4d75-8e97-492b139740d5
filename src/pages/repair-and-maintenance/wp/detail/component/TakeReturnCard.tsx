import AddItemSegment from '@/components/dialogs/add-item-take-return'
import { ItemType } from '@/types/companyTypes'
import { WoSegmentType } from '@/types/woTypes'
import { But<PERSON>, Card, CardContent, Typography } from '@mui/material'
import {
  getCoreRowModel,
  getSortedRowModel,
  getPaginationRowModel,
  getFacetedRowModel,
  useReactTable,
  getFacetedUniqueValues,
  getFacetedMinMaxValues
} from '@tanstack/react-table'
import { useEffect, useMemo, useState } from 'react'
import { segmentColumsns, takeReturnColumns } from '../config/table'
import Table from '@/components/table'
import DialogAddItemSegmentWo from '@/components/dialogs/add-item-segment-wo'
import { useWp } from '../../context/WpContext'
import { FormProvider, useForm } from 'react-hook-form'
import { useQuery } from '@tanstack/react-query'
import { useAuth } from '@/contexts/AuthContext'
import UserQueryMethods, { DEFAULT_APPROVER_QUERY_KEY } from '@/api/services/user/query'
import { DefaultApprovalScope } from '@/pages/user/default-approval/config/enum'
import { StuffReqPayload } from '@/types/payload'
import { zodResolver } from '@hookform/resolvers/zod'
import { StuffReqPayloadSchema } from '@/components/dialogs/add-item-take-return/config'
import { WP_LIST_QUERY_KEY, WpQueryMethods } from '@/api/services/wp/query'
import { defaultListData } from '@/api/queryClient'
import { ListResponse } from '@/types/api'
import { StuffRequestType } from '@/types/wpTypes'
import ConfirmDialog from '@/components/dialogs/confirm-dialog'
import { useLocation } from 'react-router-dom'
import Permission from '@/core/components/Permission'

const TakeReturnCard = () => {
  const { wpDetail, navigate } = useWp()
  const { userProfile } = useAuth()
  const { pathname } = useLocation()
  const [{ item, list, confirm }, setDialog] = useState<{ item: boolean; list: boolean; confirm: boolean }>({
    item: false,
    list: false,
    confirm: false
  })
  const [currentSegment, setSegment] = useState<WoSegmentType | null>(null)
  const [selectedItem, setSelectedItem] = useState<ItemType | null>(null)
  const methods = useForm<StuffReqPayload>({
    resolver: zodResolver(StuffReqPayloadSchema)
  })
  const { control, handleSubmit, reset, setValue, getValues } = methods

  const scope = DefaultApprovalScope.StuffRequest

  const { data: stuffRequests, refetch: refetchStuffRequests } = useQuery({
    enabled: !!wpDetail?.id,
    queryKey: [WP_LIST_QUERY_KEY, 'STUFF_REQUEST', wpDetail?.id],
    queryFn: () => WpQueryMethods.getStuffRequests({ workProcessId: wpDetail?.id, limit: Number.MAX_SAFE_INTEGER }),
    placeholderData: defaultListData as ListResponse<StuffRequestType>
  })

  const { data: approverList } = useQuery({
    enabled: !!wpDetail?.siteId && !!userProfile?.departmentId,
    queryKey: [DEFAULT_APPROVER_QUERY_KEY, scope, wpDetail?.siteId, userProfile?.departmentId],
    queryFn: () =>
      UserQueryMethods.getDefaultApproverList({
        limit: 1000,
        divisionId: 'null',
        scope,
        siteId: wpDetail?.siteId,
        departmentId: 'null'
        // departmentId: wpDetail?.departmentId ?? userProfile?.departmentId
      }),
    placeholderData: []
  })

  const handleListItem = () => {
    setDialog(curr => ({ ...curr, list: true }))
  }

  const handleItem = () => {
    setDialog(curr => ({ ...curr, item: true }))
  }

  const tableOptions: any = useMemo(
    () => ({
      data: stuffRequests?.items ?? [],
      columns: takeReturnColumns({
        onDetail: row => {
          navigate(`${pathname}/${row.id}`)
        }
      }),
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [stuffRequests?.items, pathname]
  )

  const segmentTableOptions: any = useMemo(
    () => ({
      data: currentSegment ? [currentSegment] : [],
      columns: segmentColumsns,
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [currentSegment]
  )

  const table = useReactTable(tableOptions)

  const tableSegments = useReactTable(segmentTableOptions)

  useEffect(() => {
    reset({
      ...getValues(),
      workProcessId: wpDetail?.id,
      approvals: approverList.map(approver => ({
        userId: approver.user?.id
      }))
    })
  }, [approverList])

  return (
    <FormProvider {...methods}>
      <Card>
        <CardContent className='flex flex-col gap-4'>
          <div className='flex justify-between items-center'>
            <Typography variant='h5'>Ambil dan Kembalikan Barang</Typography>
            <Permission permission={['work-process.update']}>
              <Button
                disabled={!['PENDING', 'IN_PROCESS', 'IN_REVIEW'].includes(wpDetail?.status)}
                onClick={handleListItem}
                size='small'
                variant='outlined'
              >
                Tambah
              </Button>
            </Permission>
          </div>
          {stuffRequests?.items?.length > 0 ? (
            <div className='shadow-sm rounded-md'>
              <Table headerColor='green' table={table} disablePagination />
            </div>
          ) : (
            <div className='flex my-4 flex-col gap-2 items-center justify-center'>
              <Typography variant='h5'>Belum ada Dokumen</Typography>
              <Typography variant='body2' align='center'>
                List dokumen pengambilan dan pengembalian barang untuk Work Process ini akan ditampilkan di sini
              </Typography>
            </div>
          )}
        </CardContent>
      </Card>
      {list && (
        <AddItemSegment
          approverList={approverList?.map(approver => approver.user) ?? []}
          open={list}
          setOpen={open => setDialog(curr => ({ ...curr, list: open }))}
          onOpenDialogItem={() => {
            setDialog(curr => ({ ...curr, item: true }))
          }}
          successCb={() => refetchStuffRequests()}
        />
      )}
      {confirm && (
        <ConfirmDialog
          title='Tambah Barang Lain'
          content={
            (
              <>
                <Typography>
                  Barang sudah ditambahkan! Apakah Kamu mau menambahkan barang lain untuk Segment ini?
                </Typography>
                <div className='shadow-sm'>
                  <Table headerColor='green' table={tableSegments} disablePagination />
                </div>
              </>
            ) as any
          }
          confirmText='Tambah Barang Lain'
          confirmColor='primary'
          cancelText='Tidak'
          open={confirm}
          onCancel={() => setSegment(null)}
          onConfirm={() => setDialog(curr => ({ ...curr, confirm: false, item: true }))}
          setOpen={open => setDialog(curr => ({ ...curr, confirm: open }))}
        />
      )}
      {item && (
        <DialogAddItemSegmentWo
          currentSegment={currentSegment}
          segments={wpDetail?.workOrderSegments}
          selectedItem={selectedItem}
          withStock
          siteId={wpDetail?.site?.id}
          open={item}
          setOpen={open => setDialog(curr => ({ ...curr, item: open }))}
          onSuccessfullAdd={data => {
            setSelectedItem(null)
            setSegment(data.segment)
            setValue('items', [
              ...getValues('items'),
              {
                ...data,
                workOrderSegmentId: data.segment?.id,
                itemId: data.itemId,
                quantity: data.quantity,
                quantityUnit: data.quantityUnit
              }
            ])
            setDialog(curr => ({ ...curr, item: false, confirm: true }))
          }}
          onSuccessfullUpdate={data => {
            setSelectedItem(null)
            setValue(
              'items',
              getValues('items')?.map(item =>
                item.itemId === data.itemId
                  ? {
                      ...data,
                      workOrderSegmentId: data.segment?.id,
                      itemId: data.itemId,
                      quantity: data.quantity,
                      quantityUnit: data.quantityUnit
                    }
                  : item
              )
            )
            setDialog(curr => ({ ...curr, item: false }))
          }}
          onClose={() => {
            setSelectedItem(null)
            setDialog(curr => ({ ...curr, item: false }))
            setSegment(null)
          }}
        />
      )}
    </FormProvider>
  )
}

export default TakeReturnCard
