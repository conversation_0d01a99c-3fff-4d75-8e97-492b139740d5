// MUI Imports
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import TimelineDot from '@mui/lab/TimelineDot'
import TimelineItem from '@mui/lab/TimelineItem'
import TimelineContent from '@mui/lab/TimelineContent'
import TimelineSeparator from '@mui/lab/TimelineSeparator'
import TimelineConnector from '@mui/lab/TimelineConnector'
import Typography from '@mui/material/Typography'

import { Timeline } from '@/components/Timeline'
import { PrLogType, LogTypeStatus } from '@/types/prTypes'
import { Avatar } from '@mui/material'
import { formatDistanceToNow } from 'date-fns'
import { id } from 'date-fns/locale'
import { WarehouseLogType } from '@/types/appTypes'
import { useWp } from '../../context/WpContext'
import { WorkProcessLogStatus } from '@/types/wpTypes'

type Props = {
  logList?: WarehouseLogType[]
}

const ActivityLogCard = ({ logList = [] }: Props) => {
  const { wpLogs } = useWp()
  return (
    <Card>
      <CardHeader title='Log Aktivitas' />
      <CardContent>
        <Timeline>
          {wpLogs?.map(log => {
            let dotColor: 'primary' | 'secondary' | 'error' | 'warning' | 'info' | 'success' | 'inherit' | 'grey' =
              'primary'
            let title = ''
            const changes = ((JSON.parse(log.changes) as string[]) ?? []).map(change =>
              change.replaceAll('"', '').replaceAll('{changed}', '→').replaceAll('{added}', '')
            )
            switch (log.status) {
              case WorkProcessLogStatus.UPDATED:
                title = 'Work Proccess Diperbarui'
                dotColor = 'info'
                break
              case WorkProcessLogStatus.CREATED:
                title = 'Work Proccess Dibuat'
                break
              case WorkProcessLogStatus.REPORT_CREATED:
                title = 'Work Proccess Report Dibuat'
                break
              case WorkProcessLogStatus.REPORT_UPDATED:
                title = 'Work Proccess Report Diperbarui'
                break
              case WorkProcessLogStatus.REPORT_DELETED:
                title = 'Work Proccess Report Dihapus'
                dotColor = 'error'
                break
              case WorkProcessLogStatus.LABOR_SET:
                title = 'Labor Ditambahkan'
                dotColor = 'info'
                break
              case WorkProcessLogStatus.PROCESS_STARTED:
                title = 'Work Proccess Dimulai'
                break
              case WorkProcessLogStatus.PROCESS_ENDED:
                title = 'Work Proccess Selesai'
                break
              case WorkProcessLogStatus.PROCESS_APPROVED:
                title = 'Work Proccess Disetujui'
                dotColor = 'success'
                break
              case WorkProcessLogStatus.PROCESS_REJECTED:
                title = 'Work Proccess Ditolak'
                dotColor = 'error'
                break
              case WorkProcessLogStatus.TIMER_STARTED:
                title = 'Timer Dimulai'
                dotColor = 'grey'
                break
              case WorkProcessLogStatus.TIMER_ENDED:
                title = 'Timer Berhenti'
                dotColor = 'info'
                break
              case WorkProcessLogStatus.TAKE_STUFF_REQUESTED:
                title = 'Ambil Barang'
                break
              case WorkProcessLogStatus.RETURN_STUFF_REQUESTED:
                title = 'Kembalikan Barang'
                break
              default:
                break
            }
            return title ? (
              <TimelineItem key={log.id} className='pt-2'>
                <TimelineSeparator>
                  <TimelineDot color={dotColor} />
                  <TimelineConnector />
                </TimelineSeparator>
                <TimelineContent>
                  <div className='flex flex-wrap items-center justify-between gap-x-2 mbe-1'>
                    <Typography color='text.primary' className='font-medium text-base'>
                      {title}
                    </Typography>
                    <Typography variant='caption'>
                      {formatDistanceToNow(log.createdAt, {
                        locale: id,
                        addSuffix: true
                      })
                        .replace('sekitar ', '')
                        .replace('kurang dari ', '')}
                    </Typography>
                  </div>
                  {changes?.map(change => (
                    <Typography key={change} className='mbe-2 text-sm'>
                      {change}
                    </Typography>
                  ))}
                  {log.user ? (
                    <div className='flex items-center gap-3'>
                      <Avatar src={log?.user?.profilePictureUrl} />
                      <div className='flex flex-col'>
                        <Typography color='text.primary' className='font-medium'>
                          {log.user?.fullName}
                        </Typography>
                        <Typography variant='body2'>{log.user?.title}</Typography>
                      </div>
                    </div>
                  ) : null}
                </TimelineContent>
              </TimelineItem>
            ) : null
          })}
        </Timeline>
      </CardContent>
    </Card>
  )
}

export default ActivityLogCard
