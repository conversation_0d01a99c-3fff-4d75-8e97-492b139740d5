import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON>, CardContent, Grid, Typography } from '@mui/material'
import ChevronRight from '@/components/menu/svg/ChevronRight'
import { useWp } from '../../context/WpContext'
import DetailReportDocumentWp from '@/components/dialogs/detail-report-document-wp'
import truncateString from '@/core/utils/truncate'
import { truncateFromEnd } from '../config/utils'
import { WpReportType } from '@/types/wpTypes'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import Permission from '@/core/components/Permission'

const ReportWorkingCard = () => {
  const { setDialogReport, wpDetail, wpReports, setActiveReport } = useWp()
  const [detilReport, setDetilReport] = useState<{ state: boolean; report: WpReportType | null }>({
    state: false,
    report: null
  })

  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-6'>
          <div className='flex justify-between items-center'>
            <Typography variant='h5'>Laporan Proses Pengerjaan</Typography>
            <Permission permission={['work-process.update']}>
              <Button
                disabled={wpDetail?.status !== 'IN_PROCESS'}
                onClick={() => setDialogReport(true)}
                variant='outlined'
                size='small'
              >
                Tambah
              </Button>
            </Permission>
          </div>
          <Grid container spacing={2}>
            {wpReports?.length > 0 ? (
              wpReports?.map(report => (
                <div
                  role='button'
                  onClick={() => setDetilReport({ report, state: true })}
                  className='cursor-pointer bg-[#4C4E640D] rounded-[8px] p-4 flex justify-between items-center w-full'
                >
                  <div className='flex flex-col gap-2 flex-1'>
                    <div className='flex flex-row justify-between items-baseline'>
                      <Typography variant='h5'>{report.name}</Typography>
                      <small>{formatDate(new Date(report.createdAt), 'dd/MM/yyyy, HH:mm', { locale: id })}</small>
                    </div>
                    <Typography variant='body1'>
                      {truncateFromEnd(report.reportUrl.split('/')[report.reportUrl.split('/').length - 1], 15)}
                    </Typography>
                    <Typography variant='caption' color='primary'>
                      {report.imagesCount} foto proses pengerjaan
                    </Typography>
                  </div>
                  <ChevronRight className='text-primary cursor-pointer' />
                </div>
              ))
            ) : (
              <div className='flex flex-col gap-2 p-5 items-center w-full'>
                <Typography variant='h5'>Belum ada Laporan</Typography>
                <Typography variant='body2'>List Laporan proses pengerjaan wp ini akan ditampilkan disini</Typography>
              </div>
            )}
          </Grid>
        </CardContent>
      </Card>
      {detilReport.state && (
        <DetailReportDocumentWp
          onEditReport={report => {
            setActiveReport(report)
            setDialogReport(true)
          }}
          open={detilReport.state}
          setOpen={bool => setDetilReport(curr => ({ ...curr, state: bool }))}
          report={detilReport.report}
        />
      )}
    </>
  )
}

export default ReportWorkingCard
