import { Card, CardContent, Typography } from '@mui/material'
import {
  createColumnHelper,
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import { useWp } from '../../context/WpContext'
import Table from '@/components/table'
import { LaborType } from '@/types/wpTypes'
import { useMemo } from 'react'

const columnHelper = createColumnHelper<LaborType>()

const tableColumns = [
  columnHelper.accessor('id', {
    size: 20,
    header: 'NO',
    cell: ({ row }) => row.index + 1
  }),
  columnHelper.accessor('fullName', {
    header: 'NAMA'
  }),
  columnHelper.accessor('title', {
    header: 'JABATAN'
  })
]

const LaborCard = () => {
  const { wpDetail } = useWp()

  const labors = wpDetail?.labors

  const tableOptions = useMemo(
    () => ({
      data: labors ?? [],
      columns: tableColumns,
      getCoreRowModel: getCoreRowModel(),
      getFilteredRowModel: getFilteredRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [labors]
  )

  const table = useReactTable<any>(tableOptions)
  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Labors</Typography>
        </div>
        <div className='rounded-[8px] shadow-xs'>
          <Table
            table={table}
            emptyLabel={
              <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
                <Typography>Belum ada Labor</Typography>
                <Typography className='text-sm text-gray-400'>
                  Tambahkan labor yang ingin dimasukkan dalam Work Process ini
                </Typography>
              </td>
            }
            disablePagination
          />
        </div>
      </CardContent>
    </Card>
  )
}

export default LaborCard
