import AppReactDatepicker from '@/components/libs/styles/AppReactDatepicker'
import { Card, CardContent, FormControl, Grid, InputLabel, TextField, Typography } from '@mui/material'
import { id } from 'date-fns/locale'
import { formatDate } from 'date-fns'
import { useWp } from '../../context/WpContext'

const EstimatedCard = () => {
  const { wpDetail } = useWp()
  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Est<PERSON><PERSON>ai</Typography>
        </div>
        <div className='flex flex-col gap-6'>
          <div className='flex flex-col gap-1 items-start self-stretch relative w-full h-[45px] bg-transparent'>
            <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
              <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>Tanggal</small>
            </label>
            <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
            <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
              {wpDetail?.estimatedEndedAt
                ? formatDate(new Date(wpDetail?.estimatedEndedAt), 'eeee, dd/MM/yyyy', { locale: id })
                : '-'}
            </p>
          </div>
          <div className='flex flex-col gap-1 items-start self-stretch relative w-full h-[45px] bg-transparent'>
            <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
              <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>Jam</small>
            </label>
            <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
            <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
              {wpDetail?.estimatedEndedAt
                ? (() => {
                    const date = new Date(wpDetail?.estimatedEndedAt)
                    const formattedTime = formatDate(date, 'HH:mm', { locale: id })
                    return formattedTime === '00:00' ? '23.59' : formattedTime.replace(':', '.')
                  })()
                : '-'}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default EstimatedCard
