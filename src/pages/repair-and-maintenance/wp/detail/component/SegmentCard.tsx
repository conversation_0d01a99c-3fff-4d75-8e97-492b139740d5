import { ItemType } from '@/types/companyTypes'
import { Autocomplete, Card, CardContent, CircularProgress, Divider, Grid, TextField, Typography } from '@mui/material'
import { SegmentType, useWp } from '../../context/WpContext'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import { segmentTable } from '../../new-wp/config/table'
import Table from '@/components/table'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { useMemo, useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import RnMQueryMethods from '@/api/services/rnm/query'
import { WoSegmentType } from '@/types/woTypes'
import DetilSegmentWo from '@/components/dialogs/detil-segment-wo'

const SegmentCard = () => {
  const { wpDetail } = useWp()
  const [selectedSegmentId, setSelectedSegmentId] = useState<string | null>(null)

  const { data: woSegments } = useQuery({
    enabled: !!wpDetail?.workOrder?.id,
    queryKey: ['WO_SEGMENT_KEY', wpDetail?.workOrder?.id],
    queryFn: async () => {
      const res = await RnMQueryMethods.getWoSegments(wpDetail?.workOrder?.id, { limit: Number.MAX_SAFE_INTEGER })
      return res.items
    },
    placeholderData: [] as WoSegmentType[]
  })

  const tableOptions = useMemo(
    () => ({
      data: wpDetail?.workOrderSegments ?? [],
      columns: segmentTable({
        isDetail: true,
        onDetail: (id: string) => setSelectedSegmentId(id)
      }),
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [woSegments]
  )

  const tableSegment = useReactTable<any>(tableOptions)

  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-4'>
          <div className='flex justify-between items-center'>
            <Typography variant='h5'>Segment</Typography>
          </div>
          <Grid container spacing={4}>
            <Grid item xs={12}>
              <div className='shadow-xs rounded-[8px]'>
                <Table
                  table={tableSegment}
                  emptyLabel={
                    <td colSpan={tableSegment.getVisibleFlatColumns().length} className='text-center h-60'>
                      <Typography>Belum ada Segment</Typography>
                      <Typography className='text-sm text-gray-400'>
                        Tambahkan segment yang ingin dimasukkan dalam Work Process ini
                      </Typography>
                    </td>
                  }
                  disablePagination
                />
              </div>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
      {!!selectedSegmentId && (
        <DetilSegmentWo
          open={!!selectedSegmentId}
          setOpen={bool => (!bool ? setSelectedSegmentId(null) : null)}
          selectedSegmentId={selectedSegmentId}
          woId={wpDetail?.workOrder?.id}
        />
      )}
    </>
  )
}

export default SegmentCard
