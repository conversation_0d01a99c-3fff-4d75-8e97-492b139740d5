import { <PERSON><PERSON>, <PERSON>, CardContent, Typography } from '@mui/material'
import { useWp } from '../../context/WpContext'
import { UnitType } from '@/types/companyTypes'
import { hoursToMinutes } from '@/pages/repair-and-maintenance/fr/create/config/utils'

const DataUnitDetail = ({ unit }: { unit: UnitType }) => {
  const { wpDetail } = useWp()
  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Data Unit</Typography>
        </div>
        <div className='flex flex-col gap-6'>
          <div className='flex flex-col gap-1 items-start self-stretch relative w-full h-[45px] bg-transparent'>
            <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
              <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>KM</small>
            </label>
            <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
            <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>{`${unit?.km ?? 0} KM`}</p>
          </div>
          <div className='flex flex-col gap-1 items-start self-stretch relative w-full h-[45px] bg-transparent'>
            <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
              <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>HM</small>
            </label>
            <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
            <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
              {`${hoursToMinutes(unit?.hm ?? 0)[0]} Jam ${hoursToMinutes(unit?.hm ?? 0)[1] ?? 0} Menit`}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default DataUnitDetail
