import truncateString from '@/core/utils/truncate'
import { WoSegmentType } from '@/types/woTypes'
import { StuffRequestType, WpSessionType } from '@/types/wpTypes'
import { Chip, IconButton, Tooltip, Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'
import { formatDate } from 'date-fns'
import { stuffRequestStatus } from './utils'

const columnHelper = createColumnHelper<WpSessionType>()

export const sessionsTableColumns = ({ detail }: { detail: (row: WpSessionType) => void }) => [
  columnHelper.display({
    id: 'startedAt',
    header: 'Tanggal',
    cell: ({ row }) => (row.original?.startedAt ? formatDate(new Date(row.original.startedAt), 'dd/MM/yyyy') : '-')
  }),
  columnHelper.accessor('startedAt', {
    header: 'Mulai',
    cell: ({ row }) => (row.original?.startedAt ? formatDate(new Date(row.original.startedAt), 'HH:mm') : '-')
  }),
  columnHelper.accessor('stoppedAt', {
    header: 'Selesai',
    cell: ({ row }) => (row.original?.stoppedAt ? formatDate(new Date(row.original.stoppedAt), 'HH:mm') : '-')
  }),
  columnHelper.accessor('laborsCount', {
    header: 'Labors',
    cell: ({ row }) => row.original?.laborsCount ?? '-'
  }),
  columnHelper.accessor('note', {
    header: 'Catatan',
    cell: ({ row }) => row.original?.note ?? '-'
  }),
  columnHelper.display({
    id: 'action',
    header: '',
    cell: ({ row }) => (
      <IconButton size='small' onClick={() => detail(row.original)}>
        <i className='ri-eye-line text-textSecondary' />
      </IconButton>
    )
  })
]

const columnHelperTakeReturn = createColumnHelper<StuffRequestType>()

type TakeReturnTableProps = {
  onDetail?: (row: StuffRequestType) => void
}

export const takeReturnColumns = (props: TakeReturnTableProps) => [
  columnHelperTakeReturn.display({
    id: 'number',
    header: 'No.',
    size: 10,
    cell: ({ row }) => (
      <Tooltip title={row.original.number}>
        <Typography onClick={() => props.onDetail?.(row.original)} sx={{ cursor: 'pointer' }} color='primary'>
          {row.original.number}
        </Typography>
      </Tooltip>
    )
  }),
  columnHelperTakeReturn.accessor('type', {
    header: 'Jenis',
    cell: ({ row }) => (row.original.type === 'TAKE' ? 'Ambil' : 'Kembali')
  }),
  columnHelperTakeReturn.accessor('status', {
    header: 'Status',
    cell: ({ row }) => (
      <Chip
        label={stuffRequestStatus(row.original.status).label}
        size='small'
        variant='tonal'
        color={stuffRequestStatus(row.original.status).color as any}
      />
    )
  }),
  columnHelperTakeReturn.accessor('createdAt', {
    header: 'Tgl Dibuat',
    cell: ({ row }) => formatDate(new Date(row.original.createdAt), 'dd/MM/yyyy')
  }),
  columnHelperTakeReturn.display({
    id: 'action',
    header: 'Action',
    cell: ({ row }) => {
      return (
        <IconButton size='small' onClick={() => props.onDetail?.(row.original)}>
          <i className='ri-eye-line text-textSecondary' />
        </IconButton>
      )
    }
  })
]

const segmentHelper = createColumnHelper<WoSegmentType>()

export const segmentColumsns = [
  segmentHelper.display({
    id: 'number',
    header: 'No.',
    cell: ({ row }) => row.index + 1
  }),
  segmentHelper.accessor('division.code', {
    header: 'Divisi',
    cell: ({ row: { original } }) =>
      original?.division?.id ? truncateString(`[${original.division.code}] ${original.division.name}`, 15) : '-'
  }),
  segmentHelper.accessor('jobCode.code', {
    header: 'Job Code',
    cell: ({ row: { original } }) =>
      original?.jobCode?.code ? truncateString(`${original.jobCode.code} | ${original.jobCode.description}`, 15) : '-'
  }),
  segmentHelper.accessor('componentCode.code', {
    header: 'SMCS',
    cell: ({ row: { original } }) =>
      original?.componentCode?.code
        ? truncateString(`${original.componentCode.code} | ${original.componentCode.description}`, 15)
        : '-'
  }),
  segmentHelper.accessor('modifierCode.code', {
    header: 'Modifier',
    cell: ({ row: { original } }) =>
      original?.modifierCode?.code
        ? truncateString(`${original.modifierCode.code} | ${original.modifierCode.description}`, 15)
        : '-'
  })
]
