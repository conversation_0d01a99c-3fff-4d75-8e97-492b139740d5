import { createColumnHelper } from '@tanstack/react-table'
import { id } from 'date-fns/locale'
import { Chip, IconButton, Typography } from '@mui/material'
import { formatDate } from 'date-fns'
import { WpType } from '@/types/wpTypes'
import truncateString from '@/core/utils/truncate'
import { getStatusConfig } from './utils'
import { srPriorityOptions } from '@/pages/service-requisition/list/config/utils'
import { ServiceRequisitionPriority } from '@/types/serviceRequisitionsTypes'

const columnHelper = createColumnHelper<WpType>()

type RowAction = {
  detail: (item: WpType) => void
}

export const tableColumns = (rowActions?: RowAction) => [
  columnHelper.accessor('number', {
    header: 'NO WP',
    cell: ({ row }) => (
      <Typography color='primary' sx={{ cursor: 'pointer' }} onClick={() => rowActions?.detail(row.original)}>
        {row.original?.number}
      </Typography>
    )
  }),
  columnHelper.accessor('workOrderSegment.number', {
    header: 'Jml Segment',
    cell: ({ row }) => <Typography align='center'>{row.original?.segmentsCount}</Typography>
  }),
  columnHelper.accessor('status', {
    header: 'STATUS',
    cell: ({ row }) => (
      <Chip
        label={getStatusConfig(row.original.status).label}
        color={getStatusConfig(row.original.status).color as any}
        variant='tonal'
        size='small'
      />
    )
  }),
  columnHelper.accessor('unit.number', {
    header: 'Kode Unit'
  }),
  columnHelper.accessor('laborsCount', {
    header: 'LABOR',
    cell: ({ row }) => `${row.original.laborsCount} Labor`
  }),
  columnHelper.accessor('priority', {
    header: 'Prioritas',
    cell: ({ row }) => {
      const priority = srPriorityOptions.find(
        option => option.value === String(row.original.priority ?? ServiceRequisitionPriority.P4)
      )
      return priority ? (
        <div className='flex items-center gap-2'>
          <div className={`size-2 ${priority.color}`} />
          <Typography>{priority.label}</Typography>
        </div>
      ) : (
        '-'
      )
    }
  }),
  columnHelper.accessor('createdAt', {
    header: 'TGL DIBUAT',
    cell: ({ row }) => formatDate(row.original.createdAt, 'dd/MM/yyyy', { locale: id })
  }),
  columnHelper.accessor('estimatedEndedAt', {
    header: 'ESTIMASI SELESAI',
    cell: ({ row }) =>
      row.original.estimatedEndedAt ? formatDate(row.original.estimatedEndedAt, 'dd/MM/yyyy', { locale: id }) : '-'
  }),
  columnHelper.accessor('id', {
    header: 'ACTION',
    enableSorting: false,
    cell: ({ row }) => (
      <div className='flex items-center gap-0.5'>
        <IconButton size='small' onClick={() => rowActions.detail(row.original)}>
          <i className='ri-eye-line text-secondary' />
        </IconButton>
      </div>
    )
  })
]
