export function updateEstimatedEndedAt(estimatedEndedAt: string, estimatedHourEndedAt?: string): string {
  if (!estimatedHourEndedAt) {
    return estimatedEndedAt
  }

  const endedAtDate = new Date(estimatedEndedAt)
  const hourEndedAtDate = new Date(estimatedHourEndedAt)

  endedAtDate.setUTCHours(hourEndedAtDate.getUTCHours())

  return endedAtDate.toISOString()
}

export const getStatusConfig = (status: string): { label: string; color: string } => {
  switch (status) {
    case 'PENDING':
      return {
        label: 'Menunggu',
        color: 'warning'
      }
    case 'IN_PROCESS':
      return {
        label: 'Sedang dikerjakan',
        color: 'info'
      }
    case 'IN_REVIEW':
      return {
        label: 'Dalam Review',
        color: 'default'
      }
    case 'APPROVED':
      return {
        label: 'Disetujui',
        color: 'success'
      }
    case 'REJECTED':
      return {
        label: 'Ditolak',
        color: 'error'
      }
    default:
      return {
        label: status,
        color: 'warning'
      }
  }
}
