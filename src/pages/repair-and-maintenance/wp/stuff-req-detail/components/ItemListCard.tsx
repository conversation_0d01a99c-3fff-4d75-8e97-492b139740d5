import { StuffRequestType } from '@/types/wpTypes'
import { Card, CardContent, Typography } from '@mui/material'
import {
  getSortedRowModel,
  getPaginationRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFacetedMinMaxValues,
  useReactTable,
  getCoreRowModel
} from '@tanstack/react-table'
import { itemstableColumns } from '../config/table'
import Table from '@/components/table'
import { useMemo, useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { WO_SEGMENTS_QUERY_KEY } from '@/api/services/rnm/service'
import RnMQueryMethods from '@/api/services/rnm/query'
import { UnitType } from '@/types/companyTypes'
import AddWarehouseItemDialog from '@/components/dialogs/add-warehouse-item'
import { ItemType } from '@/pages/repair-and-maintenance/wo/create-wo/config/schema'

type ItemListCardProps = {
  data: StuffRequestType
}

const ItemListCard = ({ data }: ItemListCardProps) => {
  const [selectedItem, setItem] = useState<ItemType | null>(null)

  const { data: segmentData } = useQuery({
    enabled: data?.items?.length > 0 && !!data?.workOrder?.id,
    queryKey: [WO_SEGMENTS_QUERY_KEY, data?.items?.[0]?.workOrderSegmentId, data?.workOrder],
    queryFn: async () => {
      const res = await RnMQueryMethods.getWoSegment(data?.workOrder.id, data?.items?.[0]?.workOrderSegmentId)
      return res
    }
  })

  const tableOptions = useMemo(
    () => ({
      data: data?.items ?? [],
      columns: itemstableColumns({
        detail: row => setItem({ ...(row as unknown as ItemType), itemId: row.item.id })
      }),
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [data]
  )

  const table = useReactTable(tableOptions as any)

  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-4'>
          <Typography variant='h5'>{data?.type === 'TAKE' ? 'Ambil' : 'Kembalikan'} Barang</Typography>
          <div className='bg-[#4C4E640D] p-4 flex flex-col gap-4'>
            <Typography className='font-semibold'>Untuk Segment</Typography>
            <div className='grid grid-cols-2 md:grid-cols-4 gap-4'>
              <div className='flex flex-col gap-2'>
                <small>No. Segmen</small>
                <Typography>{segmentData?.number}</Typography>
              </div>
              <div className='flex flex-col gap-2'>
                <small>Job Code</small>
                <Typography>
                  {segmentData?.jobCode?.code
                    ? `${segmentData?.jobCode?.code} | ${segmentData?.jobCode?.description}`
                    : '-'}
                </Typography>
              </div>
              <div className='flex flex-col gap-2'>
                <small>SMCS</small>
                <Typography>
                  {segmentData?.componentCode?.code
                    ? `${segmentData?.componentCode?.code} | ${segmentData?.componentCode?.description}`
                    : '-'}
                </Typography>
              </div>
              <div className='flex flex-col gap-2'>
                <small>Modifier</small>
                <Typography>
                  {segmentData?.modifierCode?.code
                    ? `${segmentData?.modifierCode?.code} | ${segmentData?.modifierCode?.description}`
                    : '-'}
                </Typography>
              </div>
            </div>
            <div className='rounded-md shadow-sm'>
              <Table disablePagination table={table} headerColor='green' />
            </div>
          </div>
        </CardContent>
      </Card>
      {!!selectedItem && (
        <AddWarehouseItemDialog
          withoutUnit
          currentItem={{ ...selectedItem, id: 0 }}
          viewOnly
          open={!!selectedItem}
          setOpen={open => setItem(!open && null)}
          onSubmit={() => {}}
        />
      )}
    </>
  )
}

export default ItemListCard
