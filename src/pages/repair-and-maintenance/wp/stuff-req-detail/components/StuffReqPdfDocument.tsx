import { Document, Page, StyleSheet, Text, View, Image } from '@react-pdf/renderer'
import { Table, TableHeader, TableCell, TableRow } from '@ag-media/react-pdf-table'
import { WorkOrderType, WoSegmentType } from '@/types/woTypes'
import { formatDate } from 'date-fns'
import { toTitleCase } from '@/utils/helper'
import { UnitType } from '@/types/companyTypes'
import { LaborType, StuffRequestType, WpType } from '@/types/wpTypes'

// Font.register({
//   family: 'Roboto',
//   fonts: [
//     { src: 'https://cdnjs.cloudflare.com/ajax/libs/ink/3.1.10/fonts/Roboto/roboto-regular-webfont.ttf', fontWeight: 'normal' },
//     { src: 'https://cdnjs.cloudflare.com/ajax/libs/ink/3.1.10/fonts/Roboto/roboto-bold-webfont.ttf', fontWeight: 'bold' }
//   ]
// })

const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    backgroundColor: '#FFFFFF',
    padding: 20,
    fontSize: 9
    // fontFamily: 'Roboto'
  },
  border: {
    border: '1px solid #000',
    borderRadius: 4,
    padding: 4
  },
  header: {
    marginBottom: 10,
    borderBottom: 1,
    paddingBottom: 5,
    borderBottomColor: '#dddddd'
  },
  woNumber: {
    fontSize: 14,
    fontWeight: 'bold'
  },
  woDate: {
    fontSize: 8,
    color: '#555555'
  },
  section: {
    marginBottom: 10,
    padding: 10,
    border: 1,
    borderColor: '#000'
  },
  sectionTitle: {
    fontSize: 11,
    fontWeight: 'bold',
    marginBottom: 8,
    borderBottom: 1,
    paddingBottom: 4,
    borderBottomColor: '#000'
  },
  grid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    margin: -5
  },
  gridCol: {
    width: '50%',
    padding: 5
  },
  field: {
    marginBottom: 5
  },
  fieldLabel: {
    fontWeight: 'bold',
    color: '#333333'
  },
  flexRow: {
    display: 'flex',
    flexDirection: 'row'
  },
  flexCol: {
    display: 'flex',
    flexDirection: 'column'
  },
  table: {
    width: '100%',
    marginBottom: 15,
    border: '1px solid #000'
  },
  tableRow: {
    flexDirection: 'row',
    alignItems: 'center',
    borderTop: '1px solid black'
  },
  tableHeader: {
    fontWeight: 'bold',
    fontSize: 8,
    borderTop: '1px solid black'
  },
  tableCell: {
    fontSize: 8,
    padding: 6,
    border: 'none'
  },
  textRight: {
    textAlign: 'right',
    width: '100%'
  },
  textCenter: {
    textAlign: 'center',
    width: '100%'
  },
  tableCol: {
    padding: 4,
    borderRight: '1px solid #eee'
  },
  tableColHeader: {
    padding: 4,
    borderRight: '1px solid #eee'
  },
  textBold: {
    fontFamily: 'Helvetica-Bold',
    fontWeight: 700,
    fontSize: 9
  },
  textAddress: {
    fontSize: 8,
    width: '100%'
  },
  signatureItem: {
    fontSize: 10,
    minHeight: 65,
    maxWidth: 120,
    flexGrow: 1,
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'space-between'
  },
  signatureName: {
    paddingTop: 4,
    fontSize: 9
  }
})

const header = ['Kode Item', 'No Serial', 'Item', 'Merk', 'Keterangan', 'Qty']

type WoPdfDocumentProps = {
  stuffData: StuffRequestType
  segment: WoSegmentType
  wpData: WpType
  unit?: UnitType
}

const StuffReqPdfDocument = ({ segment, wpData, stuffData, unit }: WoPdfDocumentProps) => {
  return (
    <Document>
      <Page size='A4' style={styles.page}>
        {/* Header */}
        <View style={[styles.flexRow, { justifyContent: 'space-between', gap: 4, marginBottom: 16 }]}>
          <View style={[styles.border, styles.flexRow, { gap: 8 }]}>
            <Image src='/rpu-logo.png' style={{ width: 32, height: 32 }} />
            <View style={[styles.flexCol]}>
              <Text style={[styles.textBold, { color: '#000' }]}>PT. RIMBA PERKASA UTAMA</Text>
              <View style={{ marginVertical: 2 }} />
              <Text style={styles.textAddress}>Jl. Danau Toba No. 148</Text>
              <Text style={[styles.textAddress, { marginTop: 2 }]}>Samarinda</Text>
            </View>
          </View>
          <Text style={[styles.textBold, { fontSize: 14, color: '#000', paddingHorizontal: 8 }]}>
            Dokumen {stuffData?.type === 'TAKE' ? 'Pengambilan' : 'Pengembalian'} Barang
          </Text>
        </View>
        <View style={styles.grid}>
          <View style={styles.gridCol}>
            {/* Reporter */}
            <View style={styles.section}>
              <View style={styles.field}>
                <Text style={styles.fieldLabel}>No Work Proccess</Text>
                <Text>{wpData?.number}</Text>
              </View>
              <View style={styles.field}>
                <Text style={styles.fieldLabel}>Tanggal</Text>
                <Text>{formatDate(wpData?.createdAt, 'dd/MM/yyyy HH:mm')}</Text>
              </View>
              <View style={styles.field}>
                <Text style={styles.fieldLabel}>Lokasi Workshop</Text>
                <Text>{wpData?.site?.name ?? '-'}</Text>
              </View>
              <View style={styles.field}>
                <Text style={styles.fieldLabel}>Unit No</Text>
                <Text>{unit?.number ?? '-'}</Text>
              </View>
            </View>
          </View>
          <View style={styles.gridCol}>
            {/* Tujuan */}
            <View style={styles.section}>
              <View style={styles.field}>
                <Text style={styles.fieldLabel}>
                  No {stuffData?.type === 'TAKE' ? 'Pengambilan' : 'Pengembalian'} Barang
                </Text>
                <Text>{stuffData?.number}</Text>
              </View>
              <View style={styles.field}>
                <Text style={styles.fieldLabel}>Tanggal</Text>
                <Text>{formatDate(wpData?.createdAt, 'dd/MM/yyyy HH:mm')}</Text>
              </View>
              <View style={styles.field}>
                <Text style={styles.fieldLabel}>Departemen</Text>
                <Text>{wpData?.department?.name ?? '-'}</Text>
              </View>
              <View style={styles.field}>
                <Text style={styles.fieldLabel}>Estimasi Selesai</Text>
                <Text style={{ color: 'red' }}>
                  {wpData?.estimatedEndedAt ? formatDate(wpData?.estimatedEndedAt, 'dd/MM/yyyy HH:mm') : '-'}
                </Text>
              </View>
            </View>
          </View>
        </View>

        <Text style={[styles.sectionTitle, { borderBottom: 'none' }]}>Untuk Segmen</Text>

        <View style={styles.grid}>
          <View style={[styles.gridCol, { width: '100%' }]}>
            <View style={[styles.section, styles.grid, { margin: 0 }]}>
              <View style={[styles.field, { width: '25%' }]}>
                <Text style={styles.fieldLabel}>No. Segmen</Text>
                <Text>{segment?.number}</Text>
              </View>
              <View style={[styles.field, { width: '25%' }]}>
                <Text style={styles.fieldLabel}>Job Code</Text>
                <Text>{segment?.jobCode?.description ?? '-'}</Text>
              </View>
              <View style={[styles.field, { width: '25%' }]}>
                <Text style={styles.fieldLabel}>SMCS</Text>
                <Text>{segment?.componentCode?.description ?? '-'}</Text>
              </View>
              <View style={[styles.field, { width: '25%' }]}>
                <Text style={styles.fieldLabel}>Modifier</Text>
                <Text>{segment?.modifierCode?.description ?? '-'}</Text>
              </View>
            </View>
          </View>
        </View>

        {/* Tabel Items */}
        <Table
          tdStyle={{ padding: '10px', textAlign: 'center' }}
          style={[styles.table, { marginTop: 20 }]}
          weightings={[0.4, 0.5, 0.5, 0.5, 0.5, 0.4, 0.3]}
        >
          <TableHeader fixed style={styles.tableHeader}>
            {header.map(header => (
              <TableCell key={`$indexHeader}`} style={[styles.tableCell]}>
                <Text style={['Harga', 'Diskon', 'Total'].includes(header) ? styles.textCenter : styles.fieldLabel}>
                  {header}
                </Text>
              </TableCell>
            ))}
          </TableHeader>
          {stuffData?.items?.map((item, index) => (
            <TableRow
              key={index}
              style={[
                styles.tableRow,
                { borderBottom: index === stuffData?.items?.length - 1 ? '1px solid #000' : 'none' },
                { borderTop: index === 0 ? '1px solid #000' : '0.5px solid #000' }
              ]}
            >
              <TableCell style={[styles.tableCell]}>
                <Text>{item?.item?.number}</Text>
              </TableCell>
              <TableCell style={[styles.tableCell]}>
                <Text>{item?.serialNumberId ?? '-'}</Text>
              </TableCell>
              <TableCell style={[styles.tableCell]}>
                <Text style={{ textAlign: 'left' }}>{item?.item?.name ?? '-'}</Text>
              </TableCell>
              <TableCell style={[styles.tableCell]}>
                <Text style={{ textAlign: 'left' }}>{item?.item?.brandName ?? '-'}</Text>
              </TableCell>
              <TableCell style={[styles.tableCell]}>
                <Text>{item?.note ?? '-'}</Text>
              </TableCell>
              <TableCell style={[styles.tableCell]}>
                <Text>
                  {item?.quantity ?? 0} {item?.quantityUnit}
                </Text>
              </TableCell>
            </TableRow>
          ))}
        </Table>

        {/* <View style={[styles.gridCol, { marginBottom: 10, paddingLeft: 0 }]}>
          <Text style={[styles.fieldLabel]}>Catatan: {woDetail?.note ?? '-'}</Text>
        </View> */}

        <View style={[styles.grid, { marginTop: 20 }]}>
          <View style={[styles.gridCol, { width: '100%' }]}>
            <View style={[styles.section, { border: 'none', flexDirection: 'row', flexWrap: 'wrap' }]}>
              <View style={styles.signatureItem}>
                <Text style={{ flexGrow: 3, marginBottom: 5, fontSize: 9 }}>Dibuat oleh</Text>
                <View style={{ borderBottom: '1px solid #000', width: '60%', marginTop: 5 }} />
                <Text style={styles.signatureName}>{wpData?.createdByUser?.fullName}</Text>
              </View>
              {stuffData?.approvals?.map((app, index) => (
                <View key={index} style={styles.signatureItem}>
                  <Text style={{ flexGrow: 3, marginBottom: 5, fontSize: 9 }}>
                    {index === 0 ? 'Disetujui oleh' : ''}
                  </Text>
                  <View style={{ borderBottom: '1px solid #000', width: '60%', marginTop: 5 }} />
                  <Text style={styles.signatureName}>{app?.user?.fullName}</Text>
                </View>
              ))}
            </View>
          </View>
        </View>
      </Page>
    </Document>
  )
}

export default StuffReqPdfDocument
