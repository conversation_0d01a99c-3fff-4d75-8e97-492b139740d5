// React Imports

// MUI Imports
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'
import TextField from '@mui/material/TextField'

import { object, string, TypeOf } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'

// Util Imports
import Logo from '@/components/layout/shared/Logo'
import { Controller, SubmitHandler, useForm } from 'react-hook-form'
import { useResetPassword } from '@/api/services/auth/mutation'
import LoadingButton from '@mui/lab/LoadingButton'
import { useEffect, useState } from 'react'
import { Button, IconButton, InputAdornment } from '@mui/material'
import { Link, useSearchParams } from 'react-router-dom'
import { useRouter } from '@/routes/hooks'
import { toast } from 'react-toastify'
import { fromUnixTime, isBefore, startOfTomorrow, subDays } from 'date-fns'
import { AddUserInput, addUserSchema } from '../user/list/components/user/list/AddUserDrawer'
import { useAuth } from '@/contexts/AuthContext'
import { useUpdateUser } from '@/api/services/user/mutation'

const passwordValidation = new RegExp(/^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9]).{8,}$/)

const setPasswordSchema = object({
  companyCode: string(),
  username: string(),
  verificationCode: string(),
  password: string({ message: 'Password wajib diisi' })
    .min(8, 'Password wajib minimal 8 karakter')
    .regex(passwordValidation, {
      message: 'Password wajib minimal menggunakan 1 huruf besar, 1 huruf kecil dan 1 angka'
    }),
  retypePassword: string({ message: 'Konfirmasi password wajib diisi' }).min(8, 'Konfirmasi password wajib diisi')
}).refine(data => data.password === data.retypePassword, {
  message: 'Konfirmasi password tidak sesuai',
  path: ['retypePassword']
})

type SetPasswordInput = Required<TypeOf<typeof setPasswordSchema>>

type Props = {
  isUpdatePassword?: boolean
}

const UpdatePasswordPage = ({ isUpdatePassword }: Props) => {
  const { userProfile } = useAuth()
  const [isPasswordShown, setIsPasswordShown] = useState(false)
  const handleClickShowPassword = () => setIsPasswordShown(show => !show)

  const router = useRouter()

  const [searchParams, setSearchParams] = useSearchParams()

  const token = searchParams.get('t')

  const { control, handleSubmit, setValue } = useForm<SetPasswordInput>({
    resolver: zodResolver(setPasswordSchema)
  })

  const {
    reset: resetUser,
    control: controlUser,
    setValue: setValueUser,
    getValues: getValuesUser,
    handleSubmit: handleSubmitUser,
    formState: { errors: errorsUser, isSubmitting: isSubmittingUser }
  } = useForm<AddUserInput>()

  const { mutate, isLoading, isSuccess } = useResetPassword()
  const { mutate: updateUserMutate, isLoading: isLoadingUpdateUser } = useUpdateUser()

  const onSubmitHandler: SubmitHandler<AddUserInput> = (values: AddUserInput) => {
    updateUserMutate(
      {
        userId: userProfile?.id,
        ...values
      },
      {
        onSuccess: () => {
          toast.success('Password berhasil diubah')
          setTimeout(() => {
            router.replace('/')
          }, 2000)
        }
      }
    )
  }

  useEffect(() => {
    if (userProfile) {
      resetUser({
        ...getValuesUser(),
        ...userProfile,
        siteIds: !userProfile ? [] : (userProfile.sites ?? []).map(site => site.id),
        role: 'USER',
        status: 'ACTIVE'
      })
    }
  }, [userProfile])

  return (
    <div className='flex justify-center items-center min-bs-[100dvh] is-full relative p-6'>
      <Card className='flex flex-col sm:is-[460px]'>
        <CardContent className='p-6 sm:!p-12'>
          <>
            <div className='flex justify-center items-center mbe-6'>
              <Logo />
            </div>
            <div className='flex flex-col gap-5'>
              <div>
                <Typography variant='h4'>Atur Password</Typography>
                <Typography className='mbs-1'>Masukkan password baru kamu</Typography>
              </div>
              <form
                onSubmit={e => {
                  e.preventDefault()
                  handleSubmitUser(onSubmitHandler)()
                }}
                className='flex flex-col gap-5'
              >
                <Controller
                  name='currentPassword'
                  rules={{ required: 'Wajib diisi' }}
                  control={controlUser}
                  render={({ field: { onChange, value, ref }, fieldState: { error } }) => (
                    <div className='flex flex-col gap-1'>
                      <TextField
                        fullWidth
                        label='Password Saat Ini'
                        id='confirm-password'
                        type={'password'}
                        onChange={e => onChange(e.target.value)}
                        value={value}
                        ref={ref}
                        error={!!error}
                        helperText={error?.message}
                      />
                    </div>
                  )}
                />

                <Controller
                  name='password'
                  rules={{
                    required: 'Password wajib diisi',
                    minLength: { value: 8, message: 'Password wajib minimal 8 karakter' },
                    pattern: {
                      value: passwordValidation,
                      message: 'Password wajib minimal menggunakan 1 huruf besar, 1 huruf kecil dan 1 angka'
                    }
                  }}
                  control={controlUser}
                  render={({ field: { onChange, value, ref }, fieldState: { error } }) => (
                    <div className='flex flex-col gap-1'>
                      <TextField
                        fullWidth
                        label='Password Baru'
                        id='password'
                        type={isPasswordShown ? 'text' : 'password'}
                        onChange={e => onChange(e.target.value)}
                        value={value}
                        ref={ref}
                        error={!!error}
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position='end'>
                              <IconButton
                                size='small'
                                edge='end'
                                onClick={handleClickShowPassword}
                                onMouseDown={e => e.preventDefault()}
                              >
                                <i className={isPasswordShown ? 'ri-eye-off-line' : 'ri-eye-line'} />
                              </IconButton>
                            </InputAdornment>
                          ),
                          autoComplete: 'new-password'
                        }}
                        helperText={
                          !!error
                            ? error?.message
                            : 'Password wajib minimal 8 karakter, menggunakan huruf besar, huruf kecil dan angka'
                        }
                      />
                    </div>
                  )}
                />
                <Controller
                  name='retypePassword'
                  rules={{
                    required: 'Konfirmasi password wajib diisi',
                    validate: (value, formValues) => value === formValues.password || 'Konfirmasi password tidak sesuai'
                  }}
                  control={controlUser}
                  render={({ field: { onChange, value, ref }, fieldState: { error } }) => (
                    <div className='flex flex-col gap-1'>
                      <TextField
                        fullWidth
                        label='Konfirmasi Password Baru'
                        id='confirm-password'
                        type={isPasswordShown ? 'text' : 'password'}
                        onChange={e => onChange(e.target.value)}
                        value={value}
                        ref={ref}
                        error={!!error}
                        helperText={error?.message}
                      />
                    </div>
                  )}
                />
                <LoadingButton
                  startIcon={<></>}
                  fullWidth
                  variant='contained'
                  type='submit'
                  loading={isLoadingUpdateUser}
                  loadingPosition='start'
                >
                  ATUR PASSWORD
                </LoadingButton>
              </form>
            </div>
          </>
        </CardContent>
      </Card>
      <img src='/images/pages/auth-v1-mask-3-light.png' className='absolute bottom-[5%] z-[-1] is-full max-md:hidden' />
    </div>
  )
}

export default UpdatePasswordPage
