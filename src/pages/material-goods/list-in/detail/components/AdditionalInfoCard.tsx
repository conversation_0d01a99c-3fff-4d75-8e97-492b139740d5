// MUI Imports
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'
import Grid from '@mui/material/Grid'
import { MrType } from '@/types/mrTypes'
import { WarehouseDataType } from '@/types/appTypes'
import { PoType } from '@/pages/purchase-order/config/types'
import { MrPriority, mrPriorityOptions } from '@/pages/material-request/create/config/enum'

type Props = {
  poData?: PoType
}

const AdditionalInfoCard = ({ poData }: Props) => {
  const priority = mrPriorityOptions.find(option => option.value === String(poData?.priority ?? MrPriority.P4))
  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Detil Lainnya</Typography>
        </div>
        <Grid container spacing={6}>
          <Grid item xs={12} md={6}>
            <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
              <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>Vendor</small>
              </label>
              <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
              <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                {poData?.vendor?.name ?? '-'}
              </p>
            </div>
          </Grid>
          <Grid item xs={12} md={6}>
            <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
              <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                  Departemen
                </small>
              </label>
              <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
              <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                {poData?.department?.name ?? '-'}
              </p>
            </div>
          </Grid>
          <Grid item xs={12} md={6}>
            <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
              <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                  Lokasi Warehouse
                </small>
              </label>
              <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
              <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                {poData?.site?.name ?? '-'}
              </p>
            </div>
          </Grid>
          {poData?.forSite?.name && (
            <Grid item xs={12} md={6}>
              <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
                <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                  <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                    Lokasi Penggunaan Barang
                  </small>
                </label>
                <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
                <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                  {poData?.forSite?.name ?? '-'}
                </p>
              </div>
            </Grid>
          )}
          {poData?.projectLabel && (
            <Grid item xs={12} md={6}>
              <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
                <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                  <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                    Label Proyek
                  </small>
                </label>
                <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
                <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                  {poData?.projectLabel?.name ?? '-'}
                </p>
              </div>
            </Grid>
          )}
          {!!poData?.priority && (
            <Grid item xs={12} md={6}>
              <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
                <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                  <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                    Prioritas
                  </small>
                </label>
                <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
                <div className='flex items-center gap-2'>
                  <div className={`size-2 ${priority.color}`} />
                  <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                    {priority.label}
                  </p>
                </div>
              </div>
            </Grid>
          )}
          <Grid item xs={12} md={6}>
            <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
              <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                  Catatan
                </small>
              </label>
              <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
              <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                {poData?.note ?? '-'}
              </p>
            </div>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  )
}

export default AdditionalInfoCard
