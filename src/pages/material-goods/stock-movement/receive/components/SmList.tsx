import { useEffect, useState } from 'react'
import { Typography } from '@mui/material'
import Card from '@mui/material/Card'
import Button from '@mui/material/Button'

import {
  getCoreRowModel,
  useReactTable,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFacetedMinMaxValues,
  getPaginationRowModel,
  getSortedRowModel
} from '@tanstack/react-table'

import DebouncedInput from '@/components/DebounceInput'
import Table from '@/components/table'
import { tableColumns } from '../config/table'
import { useRouter } from '@/routes/hooks'
import FilterGroupDialog, { FilterGroupConfig, FilterValues } from '@/components/layout/shared/filter/FilterGroup'
import { useAuth } from '@/contexts/AuthContext'
import { useSm } from '../../context/SmContext'

const SmList = () => {
  const router = useRouter()
  const { ownSiteList, departmentList } = useAuth()
  const {
    smListResponse: { items: smList, totalItems, totalPages },
    smParams,
    setSmParams,
    setSelectedSmId,
    setPartialSmParams,
    siteList
  } = useSm()

  const { page, search, startDate, endDate, departmentId, priority, originSiteIds, destinationSiteIds } = smParams

  const [filterGroupConfig, setFilterGroupConfig] = useState<FilterGroupConfig>({})

  // TODO: MOVE THIS SHIT
  const table = useReactTable({
    data: smList,
    columns: tableColumns({
      showDetail: id => {
        setSelectedSmId(id)
        router.push(`/mg/sm/receive/${id}`)
      }
    }),
    initialState: {
      pagination: {
        pageSize: smParams.limit ?? 10,
        pageIndex: page - 1
      }
    },
    manualPagination: true,
    rowCount: totalItems,
    pageCount: totalPages,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getFacetedMinMaxValues: getFacetedMinMaxValues()
  })

  const onFilterChanged = ({
    date,
    department,
    requestedSiteIds: requestedSiteIdList,
    requesterSiteIds: requesterSiteIdList
  }: FilterValues) => {
    setSmParams(prev => {
      return {
        ...prev,
        page: 1,
        startDate: date[0],
        endDate: date[1],
        departmentId: department.length > 0 ? department[0] : undefined,
        originSiteIds: requestedSiteIdList?.length > 0 ? requestedSiteIdList[0] : undefined,
        destinationSiteIds: requesterSiteIdList?.length > 0 ? requesterSiteIdList[0] : undefined
      }
    })
  }

  useEffect(() => {
    setSmParams({
      limit: 10,
      page: 1
    })
  }, [])

  useEffect(() => {
    setFilterGroupConfig({
      date: {
        options: [],
        values: [startDate, endDate]
      },
      requestedSiteIds: {
        options: siteList.map(site => {
          return { value: site.id, label: site.name }
        }),
        values: originSiteIds ? [originSiteIds] : []
      },
      requesterSiteIds: {
        options: ownSiteList.map(site => {
          return { value: site.id, label: site.name }
        }),
        values: destinationSiteIds ? [destinationSiteIds] : []
      },
      department: {
        options: departmentList?.map(department => ({ value: department.id, label: department.name })) ?? [],
        values: departmentId ? [departmentId] : []
      }
    })
  }, [smParams])

  return (
    <Card>
      <div className='flex justify-between gap-4 p-5 flex-col items-start sm:flex-row sm:items-center'>
        <div className='flex gap-4 items-center flex-col sm:flex-row is-full sm:is-auto'>
          <DebouncedInput
            value={search}
            onChange={value => setSmParams(prev => ({ ...prev, page: 1, search: value as string }))}
            placeholder='Cari Pindah Barang'
            className='is-full sm:is-auto'
          />
          <FilterGroupDialog config={filterGroupConfig} onFilterApplied={onFilterChanged} />
        </div>
        <div className='flex items-center gap-x-4 max-sm:gap-y-4 is-full flex-col sm:is-auto sm:flex-row'>
          {/* <Button
            color='secondary'
            variant='outlined'
            startIcon={<i className='ri-upload-2-line' />}
            className='is-full sm:is-auto'
          >
            Ekspor
          </Button> */}
        </div>
      </div>
      <Table
        table={table}
        emptyLabel={
          <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
            <Typography>Belum ada Pindah Barang</Typography>
            <Typography className='text-sm text-gray-400'>
              Semua Pindah Barang yang dikirimkan ke gudang kamu akan ditampilkan di sini
            </Typography>
          </td>
        }
        onRowsPerPageChange={pageSize => {
          if (pageSize > totalItems) {
            setSmParams(prev => ({ ...prev, limit: totalItems, page: 1 }))
          } else {
            setPartialSmParams('limit', pageSize)

            const maxPage = Math.ceil(totalItems / pageSize)
            if (page > maxPage) {
              setSmParams(prev => ({ ...prev, page: maxPage }))
            }
          }
        }}
        onPageChange={pageIndex => setPartialSmParams('page', pageIndex)}
      />
    </Card>
  )
}

export default SmList
