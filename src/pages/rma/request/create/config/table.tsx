import NumberField from '@/components/numeric/NumberField'
import { DEFAULT_CATEGORY } from '@/data/default/category'
import { RmaItemPayload, RmaPayload } from '@/pages/rma/config/types'
import { WarehouseItemType } from '@/types/appTypes'
import { isNullOrUndefined } from '@/utils/helper'
import {
  Checkbox,
  colors,
  FormControl,
  FormHelperText,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography
} from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'
import { Control, Controller, UseFieldArrayRemove, UseFormSetValue } from 'react-hook-form'

type DataTypeTable = WarehouseItemType & RmaItemPayload

const columnHelper = createColumnHelper<DataTypeTable>()

type TableProps = {
  control: Control<RmaPayload, any>
  setValue: UseFormSetValue<RmaPayload>
  remove: UseFieldArrayRemove
  items: WarehouseItemType[]
}

export const tableColumns = ({ control, remove, ...props }: TableProps) => [
  columnHelper.accessor('itemId', {
    header: 'Nama Item',
    size: 40,
    cell: ({ row }) => {
      const warehouseItem = props?.items?.find(item => item.id === +row.original.purchaseOrderItemId)
      return (
        <div className='flex flex-col gap-2'>
          <Typography variant='h6'>
            {warehouseItem?.item?.name} - {warehouseItem?.item?.brandName} {warehouseItem?.item?.number}
          </Typography>
          <Typography color='primary'>
            Qty Diterima {warehouseItem?.receivedQuantity} {warehouseItem?.item?.smallUnit}
          </Typography>
          <Typography variant='caption'>Keterangan: {warehouseItem?.note ?? '-'}</Typography>
        </div>
      )
    }
  }),
  columnHelper.accessor('receivedQuantity', {
    header: 'Satuan',
    cell: ({ row: { index, original } }) => {
      const warehouseItem = props?.items?.find(item => item.id === +original.purchaseOrderItemId)
      const item = original

      return (
        <div className='flex flex-col gap-1'>
          <div className='flex gap-4 items-center flex-col sm:flex-row'>
            <Controller
              control={control}
              rules={{ required: 'Satuan harus diisi' }}
              name={`items.${index}.quantityUnit`}
              render={({ field: qtyUnitField, fieldState: { error: errorQtyUnitField } }) => (
                <>
                  <FormControl fullWidth>
                    <InputLabel id='qtyUnit'>Satuan</InputLabel>
                    <Select
                      key={qtyUnitField.value}
                      error={Boolean(errorQtyUnitField)}
                      id='qtyUnit'
                      label='Satuan'
                      placeholder='Pilih Satuan'
                      className='bg-white'
                      {...qtyUnitField}
                      onChange={e => {
                        qtyUnitField.onChange(e.target.value)
                        props.setValue(`items.${index}.quantity`, 0)
                      }}
                    >
                      <MenuItem key={warehouseItem?.item?.largeUnit} value={warehouseItem?.item?.largeUnit}>
                        {warehouseItem?.item?.largeUnit}
                      </MenuItem>
                      <MenuItem key={warehouseItem?.item?.smallUnit} value={warehouseItem?.item?.smallUnit}>
                        {warehouseItem?.item?.smallUnit}
                      </MenuItem>
                    </Select>
                    {errorQtyUnitField?.message ? (
                      <FormHelperText error>{errorQtyUnitField.message}</FormHelperText>
                    ) : null}
                  </FormControl>
                </>
              )}
            />
          </div>
        </div>
      )
    }
  }),
  columnHelper.display({
    id: 'qty',
    header: 'QTY',
    cell: ({ row: { index, original } }) => {
      const warehouseItem = props?.items?.find(item => item.id === +original.purchaseOrderItemId)

      return (
        <Controller
          control={control}
          rules={{ required: 'Satuan harus diisi' }}
          name={`items.${index}.quantityUnit`}
          render={({ field: qtyUnitField, fieldState: { error: errorQtyUnitField } }) => (
            <Controller
              control={control}
              rules={{
                required: 'Qty retur harus diisi.',
                min: { value: 1, message: 'Qty retur harus diisi.' }
              }}
              name={`items.${index}.quantity`}
              render={({ field: qtyField, fieldState: { error: errorQty } }) => (
                <TextField
                  value={qtyField.value}
                  onBlur={e => qtyField.onChange(+e.target.value)}
                  ref={qtyField.ref}
                  fullWidth
                  label='Qty'
                  variant='outlined'
                  placeholder='Masukkan Qty Retur'
                  InputLabelProps={{ shrink: true }}
                  InputProps={{
                    className: 'bg-white',
                    inputComponent: NumberField as any,
                    inputProps: {
                      isAllowed: ({ floatValue }) =>
                        floatValue <=
                          (qtyUnitField.value === warehouseItem?.item?.largeUnit
                            ? warehouseItem.receivedQuantity / warehouseItem.item.largeUnitQuantity
                            : warehouseItem.receivedQuantity) || floatValue === undefined
                    }
                  }}
                  {...(errorQty?.message && { error: true, helperText: errorQty.message })}
                />
              )}
            />
          )}
        />
      )
    }
  }),
  columnHelper.display({
    id: 'status',
    header: 'Status Barang',
    cell: ({ row: { index, original } }) => {
      const warehouseItem = props?.items?.find(item => item.id === +original.purchaseOrderItemId)
      return (
        <Controller
          control={control}
          name={`items.${index}.hasBeenOut`}
          rules={{
            validate: value => {
              if (value == null) {
                return 'Wajib dipilih.'
              }
              return true
            }
          }}
          render={({ field: { onChange, value, ...statusField }, fieldState: { error } }) => (
            <FormControl fullWidth>
              <InputLabel id='hasBeenOut'>Status</InputLabel>
              <Select
                key={value ? 1 : 0}
                error={Boolean(error)}
                id='hasBeenOut'
                label='Status'
                placeholder='Pilih Status Barang'
                className='bg-white'
                {...statusField}
                value={!isNullOrUndefined(value) ? (value ? 1 : 2) : undefined}
                onChange={e => onChange(String(e.target.value) === '1')}
              >
                <MenuItem key={warehouseItem?.item?.largeUnit} value={0}>
                  Belum Keluar
                </MenuItem>
                <MenuItem key={warehouseItem?.item?.smallUnit} value={1}>
                  Sudah Keluar
                </MenuItem>
              </Select>
              {error?.message ? <FormHelperText error>{error.message}</FormHelperText> : null}
            </FormControl>
          )}
        />
      )
    }
  }),
  columnHelper.display({
    id: 'reason',
    header: 'Alasan Retur',
    size: 250,
    cell: ({ row: { index, original } }) => {
      return (
        <Controller
          control={control}
          rules={{
            required: 'Wajib diisi.'
          }}
          name={`items.${index}.note`}
          render={({ field: { onChange, value, ref }, fieldState: { error } }) => (
            <TextField
              className='is-full'
              defaultValue={value}
              onBlur={onChange}
              ref={ref}
              fullWidth
              multiline
              rows={3}
              label='Alasan Retur'
              variant='outlined'
              placeholder='Masukkan Alasan Retur'
              InputLabelProps={{
                shrink: true
              }}
              InputProps={{
                className: 'bg-white'
              }}
              {...(error?.message && { error: true, helperText: error.message })}
            />
          )}
        />
      )
    }
  }),
  columnHelper.display({
    id: 'action',
    size: 1,
    header: '',
    cell: ({ row: { index } }) => {
      return (
        <IconButton size='small' onClick={() => remove(index)}>
          <i className='ic-baseline-delete-forever text-red-500' />
        </IconButton>
      )
    }
  })
]

const goodColumnHelper = createColumnHelper<WarehouseItemType>()

export const goodColumns = () => [
  {
    id: 'select-col',
    size: 2,
    header: ({ table }) => (
      <Checkbox
        checked={table.getIsAllRowsSelected()}
        indeterminate={table.getIsSomeRowsSelected()}
        onChange={table.getToggleAllRowsSelectedHandler()} //or getToggleAllPageRowsSelectedHandler
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        disabled={!row.getCanSelect()}
        onChange={row.getToggleSelectedHandler()}
      />
    )
  },
  goodColumnHelper.accessor('item.number', {
    header: 'Kode Barang',
    cell: ({ row }) => <Typography color={colors.green.A400}>{row.original.item?.number}</Typography>
  }),
  goodColumnHelper.accessor('item.name', {
    header: 'Nama Item',
    cell: ({ row }) => <Typography>{row.original.item?.name}</Typography>
  }),
  goodColumnHelper.accessor('item.brandName', {
    header: 'Merk Item',
    cell: ({ row }) => <Typography>{row.original.item?.brandName}</Typography>
  }),
  goodColumnHelper.accessor('category.name', {
    header: 'Kategori Item',
    cell: ({ row }) => <Typography>{row.original.item?.category?.name ?? DEFAULT_CATEGORY.name}</Typography>
  })
]
