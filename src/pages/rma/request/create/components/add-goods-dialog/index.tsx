// MUI Imports

import Dialog, { DialogProps } from '@mui/material/Dialog'
import DialogTitle from '@mui/material/DialogTitle'
import DialogContent from '@mui/material/DialogContent'
import Typography from '@mui/material/Typography'
import IconButton from '@mui/material/IconButton'
import Button from '@mui/material/Button'
import { FormControl, Grid, InputLabel, MenuItem, Select } from '@mui/material'
import Card from '@mui/material/Card'
import { getCoreRowModel, useReactTable } from '@tanstack/react-table'
import DebouncedInput from '@/components/DebounceInput'
import Table from '@/components/table'
import { goodsTableColumns } from '@/pages/material-goods/list-stock/config/table'
import { ItemType } from '@/types/companyTypes'
import { useEffect, useState } from 'react'
import { useRma } from '../../../context/RmaContext'
import { goodColumns } from '../../config/table'
import { WarehouseItemType } from '@/types/appTypes'

type AddGoodsDialogProps = {
  open: boolean
  setOpen: (open: boolean) => void
  setData: (data: WarehouseItemType[]) => void
}

const AddGoodsDialog = (props: AddGoodsDialogProps) => {
  const { open, setOpen, setData } = props

  const { poData } = useRma()

  const [tempItems, setTempItems] = useState<WarehouseItemType[]>([])
  const [query, setQuery] = useState<string | number>('')

  const handleClose = () => {
    setOpen(!open)
  }

  const handleSearch = (rawValue: string | number) => {
    const value = String(rawValue)
    setQuery(rawValue)
    setTempItems(
      poData?.items?.filter(item =>
        Object.values(item).some(v => String(v).toLowerCase().includes(value.toLowerCase()))
      ) ?? []
    )
  }

  const handleSubmit = () => {
    setData(table.getSelectedRowModel().flatRows.map(data => data.original))
    setOpen(!open)
  }

  useEffect(() => {
    setTempItems(poData?.items ?? [])
  }, [poData])

  const table = useReactTable({
    data: tempItems,
    columns: goodColumns(),
    initialState: {
      pagination: {
        pageSize: 10,
        pageIndex: 0
      }
    },
    manualPagination: true,
    rowCount: (poData?.items ?? []).length,
    pageCount: 1,
    getCoreRowModel: getCoreRowModel()
  })

  return (
    <>
      <Dialog fullWidth maxWidth='md' open={open} onClose={handleClose}>
        <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-4 sm:px-12'>
          Pilih Barang
          <Typography component='span' className='flex flex-col text-center'>
            Tambahkan material/barang yang akan di kembalikan
          </Typography>
        </DialogTitle>
        <IconButton onClick={handleClose} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <DialogContent>
          <div className='flex justify-center gap-4 pb-5 flex-row items-start sm:flex-col sm:items-center'>
            <div className='flex gap-4 items-center flex-col sm:flex-row is-full sm:is-auto'>
              <DebouncedInput
                value={query}
                onChange={value => handleSearch(value)}
                placeholder='Cari'
                className='is-full sm:is-auto'
              />
            </div>
          </div>
          <Card>
            <Table
              table={table}
              emptyLabel={
                <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
                  <Typography> Belum ada Barang</Typography>
                  <Typography className='text-sm text-gray-400'>
                    Semua barang yang telah dibuat akan ditampilkan di sini
                  </Typography>
                </td>
              }
              disablePagination
            />
          </Card>

          <div className='flex flex-col sm:flex-row justify-center gap-4 py-5 sm:p-5 is-full sm:is-auto'>
            <Button color='secondary' variant='outlined' className='is-full sm:is-auto' onClick={handleClose}>
              BATALKAN
            </Button>
            <Button startIcon={<></>} variant='contained' onClick={handleSubmit} className='is-full sm:is-auto'>
              PILIH BARANG
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}

export default AddGoodsDialog
