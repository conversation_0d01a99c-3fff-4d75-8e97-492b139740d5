// React Imports
import { useEffect, useMemo, useState } from 'react'

import CardContent from '@mui/material/CardContent'
import Card from '@mui/material/Card'
import Typography from '@mui/material/Typography'

// Third-party Imports
import {
  createColumnHelper,
  getCoreRowModel,
  useReactTable,
  getFilteredRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFacetedMinMaxValues,
  getPaginationRowModel,
  getSortedRowModel
} from '@tanstack/react-table'
import type { ColumnDef } from '@tanstack/react-table'

// Type Imports
import { Button } from '@mui/material'

// Component Imports

// Util Imports
import type { PermissionType, RoleType } from '@/types/userTypes'
import OpenDialogOnElementClick from '@/components/dialogs/OpenDialogOnElementClick'
import AddRoleDialog from '@/components/dialogs/add-role-dialog'
import { toTitleCase } from '@/utils/helper'
import Table from '@/components/table'
import Permission from '@/core/components/Permission'

type TypeWithAction = {
  name: string
  authorization: string
} & {
  action?: string
}

// Column Definitions
const columnHelper = createColumnHelper<TypeWithAction>()

const PermissionListCard = ({ roleData }: { roleData: RoleType }) => {
  const [permissions, setPermissions] = useState([])

  const columns = useMemo<ColumnDef<TypeWithAction, any>[]>(
    () => [
      columnHelper.accessor('name', {
        header: 'Permission',
        cell: ({ row }) => <Typography>{row.original.name}</Typography>
      }),
      columnHelper.accessor('authorization', {
        header: 'Jenis Permission',
        cell: ({ row }) => <Typography>{row.original.authorization}</Typography>
      })
    ],
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  )

  const table = useReactTable({
    data: permissions,
    columns,
    initialState: {
      pagination: {
        pageSize: 10
      }
    },
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getFacetedMinMaxValues: getFacetedMinMaxValues()
  })

  useEffect(() => {
    if (!!roleData?.permissions) {
      const permissionList = (roleData.permissions ?? []).reduce((group, item) => {
        const { scope } = item
        group[scope] = group[scope] ?? []
        group[scope].push(item)
        return group
      }, {})
      setPermissions(
        Object.keys(permissionList).map(scope => ({
          name: toTitleCase(scope.replace(/-/g, ' ')),
          authorization: (permissionList[scope] as PermissionType[]).map(permission => permission.name).join(', ')
        }))
      )
    }
  }, [roleData])

  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <div className='flex flex-col md:flex-row gap-2 justify-between md:items-center'>
          <Typography variant='h5' className='text-textSecondary'>
            Permission
          </Typography>
        </div>
        <Table table={table} />
      </CardContent>
    </Card>
  )
}

export default PermissionListCard
