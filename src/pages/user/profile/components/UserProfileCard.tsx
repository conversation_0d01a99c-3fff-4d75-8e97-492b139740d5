import UserQueryMethods, { ROLE_LIST_QUERY_KEY, USER_QUERY_KEY } from '@/api/services/user/query'
import { AccountPayload } from '@/types/payload'
import {
  Card,
  CardContent,
  Typography,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  FormHelperText,
  CircularProgress,
  OutlinedInput,
  ListSubheader,
  Checkbox,
  ListItemText,
  InputAdornment,
  IconButton,
  Avatar,
  Button
} from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { useEffect, useState } from 'react'
import { useFormContext, Controller, useWatch } from 'react-hook-form'
import { AddUserInput, MenuProps } from '../../list/components/user/list/AddUserDrawer'
import { useUpdateAccount } from '@/api/services/account/mutation'
import { useAuth } from '@/contexts/AuthContext'
import CompanyQueryMethods, {
  DIVISION_LIST_QUERY_KEY,
  SITE_LIST_QUERY_KEY,
  TITLE_LIST_QUERY_KEY
} from '@/api/services/company/query'
import { defaultListData } from '@/api/queryClient'
import { ListResponse } from '@/types/api'
import { DivisionType, SiteType, TitleType } from '@/types/companyTypes'
import { RoleType } from '@/types/userTypes'
import { ProjectType } from '@/types/projectTypes'
import PhoneNumberDialog from './dialogs/phonenumber-dialog'
import EmailDialog from './dialogs/email-dialog'
import { useMenu } from '@/components/menu/contexts/menuContext'
import { useRouter } from '@/routes/hooks'
import ChangePasswordDialog from './dialogs/password-dialog'

const UserProfileCard = () => {
  const router = useRouter()
  const { departmentList, userProfile: userData } = useAuth()
  const { setConfirmState } = useMenu()
  const {
    reset,
    control,
    setValue,
    getValues,
    formState: { errors, isSubmitting }
  } = useFormContext<AddUserInput>()

  const [isPasswordShown, setIsPasswordShown] = useState(false)
  const [{ email, phoneNumber, password }, setDialogState] = useState<{
    phoneNumber: boolean
    email: boolean
    password: boolean
  }>({
    phoneNumber: false,
    email: false,
    password: false
  })

  const departmentIdWatch = useWatch({
    control,
    name: 'departmentId'
  })

  const { data: divisions } = useQuery({
    enabled: !!departmentIdWatch,
    queryKey: [DIVISION_LIST_QUERY_KEY, departmentIdWatch],
    queryFn: () =>
      CompanyQueryMethods.getDivisionList({
        limit: Number.MAX_SAFE_INTEGER,
        ...(departmentIdWatch && { departmentId: departmentIdWatch })
      }),
    placeholderData: defaultListData as ListResponse<DivisionType>
  })
  const { data: titles, isFetching: isTitlesLoading } = useQuery<ListResponse<TitleType>>({
    queryKey: [TITLE_LIST_QUERY_KEY],
    queryFn: () =>
      CompanyQueryMethods.getTitleList({
        limit: 1000
      }),
    placeholderData: defaultListData as ListResponse<TitleType>
  })
  const {
    data: { items: roleList }
  } = useQuery({
    queryKey: [ROLE_LIST_QUERY_KEY],
    queryFn: () => UserQueryMethods.getRoleList({ limit: 1000 }),
    placeholderData: defaultListData as ListResponse<RoleType>
  })
  const {
    data: { items: siteList }
  } = useQuery({
    queryKey: [SITE_LIST_QUERY_KEY],
    queryFn: () => CompanyQueryMethods.getSiteList({ limit: 1000 }),
    placeholderData: defaultListData as ListResponse<SiteType>
  })

  const groupedSiteList = siteList
    ?.reduce(
      (acc, site) => {
        const existingGroup = acc.find(group => group.projectId === site.projectId)

        if (existingGroup) {
          existingGroup.sites.push(site)
        } else {
          acc.push({
            projectId: site.projectId,
            project: site.project,
            sites: [site]
          })
        }

        return acc
      },
      [] as { projectId?: string; project?: ProjectType; sites: typeof siteList }[]
    )
    .sort((a, b) => {
      if (a.projectId === null) return 1
      if (b.projectId === null) return -1
      return 0
    })

  const handleChangePassword = () => {
    setDialogState(prev => ({ ...prev, password: true }))
  }

  useEffect(() => {
    if (userData) {
      reset({
        ...getValues(),
        ...userData,
        siteIds: !userData ? [] : (userData.sites ?? []).map(site => site.id),
        role: 'USER',
        status: 'ACTIVE'
      })
    }
  }, [userData])

  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-6'>
          <div className='flex justify-between items-center'>
            <Typography variant='h5' className='text-textSecondary'>
              Data User
            </Typography>
          </div>
          <div className='grid grid-cols-2 gap-4'>
            <div className='col-span-2'>
              <div className='flex gap-3 items-center'>
                <Avatar sx={{ width: 64, height: 64 }} src={userData?.profilePictureUrl} alt={userData?.fullName} />
                <Typography variant='h5'>{userData?.fullName}</Typography>
              </div>
            </div>
            <div className='col-span-2'>
              <div className='flex justify-between items-center'>
                <div className='flex flex-col'>
                  <small>Nomor Handphone</small>
                  <Typography>{userData?.phoneNumber}</Typography>
                </div>
                <Button
                  onClick={() => setDialogState(prev => ({ ...prev, phoneNumber: true }))}
                  size='small'
                  variant='outlined'
                >
                  Ubah Nomor
                </Button>
              </div>
            </div>
            <div className='col-span-2'>
              <div className='flex justify-between items-center'>
                <div className='flex flex-col'>
                  <small>Email</small>
                  <Typography>{userData?.email}</Typography>
                </div>
                <Button
                  onClick={() => setDialogState(prev => ({ ...prev, email: true }))}
                  size='small'
                  variant='outlined'
                >
                  Ubah Email
                </Button>
              </div>
            </div>
            <div className='col-span-2'>
              <div className='flex justify-between items-center'>
                <div className='flex flex-col gap-1'>
                  <small>Password</small>
                  <Typography
                    tabIndex={0}
                    role='button'
                    onClick={handleChangePassword}
                    color='primary'
                    className='underline cursor-pointer'
                  >
                    Ganti Password
                  </Typography>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      {phoneNumber && (
        <PhoneNumberDialog
          userData={userData}
          open={phoneNumber}
          setOpen={open => setDialogState(prev => ({ ...prev, phoneNumber: open }))}
        />
      )}
      {email && (
        <EmailDialog
          userData={userData}
          open={email}
          setOpen={open => setDialogState(prev => ({ ...prev, email: open }))}
        />
      )}
      {password && (
        <ChangePasswordDialog
          userData={userData}
          open={password}
          setOpen={open => setDialogState(prev => ({ ...prev, password: open }))}
        />
      )}
    </>
  )
}

export default UserProfileCard
