import { useAuth } from '@/contexts/AuthContext'
import { Card, CardContent, Typography } from '@mui/material'

const CompanyCard = () => {
  const { userProfile } = useAuth()
  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5' className='text-textSecondary'>
            <PERSON><PERSON><PERSON>
          </Typography>
        </div>
        <div className='grid grid-cols-2 gap-4'>
          <div className='flex flex-col gap-2 col-span-2'>
            <small><PERSON><PERSON></small>
            <Typography>{userProfile?.company?.name}</Typography>
          </div>
          <div className='flex flex-col gap-2 col-span-2'>
            <small>Kode <PERSON></small>
            <Typography>{userProfile?.company?.code}</Typography>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default CompanyCard
