import { useAuth } from '@/contexts/AuthContext'
import { Card, CardContent, Typography } from '@mui/material'

const InfoCard = () => {
  const { userProfile } = useAuth()
  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5' className='text-textSecondary'>
            Detil Akun
          </Typography>
        </div>
        <div className='grid grid-cols-2 gap-4'>
          <div className='flex flex-col gap-2 col-span-2'>
            <small>Departemen</small>
            <Typography>{userProfile?.department?.name}</Typography>
          </div>
          <div className='flex flex-col gap-2 col-span-2'>
            <small>Divisi</small>
            <Typography>{userProfile?.division?.name}</Typography>
          </div>
          <div className='flex flex-col gap-2 col-span-2'>
            <small>Jabatan</small>
            <Typography>{userProfile?.title}</Typography>
          </div>
          <div className='flex flex-col gap-2 col-span-2'>
            <small>Role</small>
            <Typography>{userProfile?.permissionGroup?.name}</Typography>
          </div>
          <div className='flex flex-col gap-2 col-span-2'>
            <small>Lokasi</small>
            <div className='flex flex-wrap gap-2'>
              {userProfile?.sites?.map(site => <div className='rounded-lg bg-[#F7F7F9] p-2'>{site.name}</div>)}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default InfoCard
