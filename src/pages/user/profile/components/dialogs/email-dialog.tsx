import { useUpdateUser } from '@/api/services/user/mutation'
import { useMenu } from '@/components/menu/contexts/menuContext'
import { useAuth } from '@/contexts/AuthContext'
import { AddUserInput } from '@/pages/user/list/components/user/list/AddUserDrawer'
import { UserType } from '@/types/userTypes'
import LoadingButton from '@mui/lab/LoadingButton'
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Grid,
  IconButton,
  TextField,
  Typography
} from '@mui/material'
import { useEffect } from 'react'
import { Controller, useFormContext } from 'react-hook-form'
import { toast } from 'react-toastify'

type Props = {
  open: boolean
  setOpen: (open: boolean) => void
  userData: UserType
}

const EmailDialog = ({ open, setOpen, userData }: Props) => {
  const { fetchUserProfile, removeAccount, activeAccount } = useAuth()
  const { setConfirmState } = useMenu()

  const { mutate: updateAccount, isLoading } = useUpdateUser()
  const { control, handleSubmit, getValues, reset } = useFormContext<AddUserInput>()
  const handleClose = () => {
    setOpen(false)
  }

  const onSubmitHandler = (dto: AddUserInput) => {
    setConfirmState({
      open: true,
      title: 'Ubah Email',
      content:
        'Kamu akan keluar dari akun ini dan mohon cek di email baru Kamu untuk proses aktivasi ulang jika kamu mengubah Email',
      confirmText: 'Simpan',
      onConfirm: () => {
        updateAccount(
          {
            userId: 'me',
            currentPassword: dto.currentPassword,
            email: dto.email
          },
          {
            onSuccess: () => {
              toast.success('Email berhasil diubah, silahkan cek email kamu untuk langkah selanjutnya')
              reset()
              handleClose()
              setTimeout(() => {
                removeAccount(activeAccount)
              }, 3000)
            }
          }
        )
      }
    })
  }

  useEffect(() => {
    if (open) {
      reset({
        ...getValues(),
        currentPassword: null,
        email: null
      })
    }
  }, [open])

  return (
    <Dialog open={open} onClose={handleClose}>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
        Ubah Email
        <Typography component='span' className='flex flex-col text-center'>
          Ubah email kamu yang terdaftar di Database Equalindo360
        </Typography>
      </DialogTitle>
      <DialogContent className='pbs-0 sm:pbe-16 sm:px-12 !py-4'>
        <IconButton onClick={() => setOpen(false)} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <Grid container spacing={5}>
          <Grid item xs={12}>
            <div className='flex flex-col gap-2 bg-[#4C4E640D] p-4'>
              <small>Email</small>
              <Typography>{userData?.email}</Typography>
            </div>
          </Grid>
          <Grid item xs={12}>
            <Controller
              name='email'
              control={control}
              rules={{ required: true }}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  {...field}
                  fullWidth
                  required
                  type='email'
                  label='Email Baru'
                  placeholder='<EMAIL>'
                  disabled={isLoading}
                  error={!!error}
                />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              name='currentPassword'
              control={control}
              rules={{ required: true }}
              render={({ field: { onChange, value, ref }, fieldState: { error } }) => (
                <div className='flex flex-col gap-1'>
                  <TextField
                    required
                    fullWidth
                    label='Password Saat Ini'
                    placeholder='Masukkan password saat ini'
                    id='confirm-password'
                    type={'password'}
                    onChange={e => onChange(e.target.value)}
                    value={value}
                    error={!!error}
                    ref={ref}
                  />
                </div>
              )}
            />
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions className='gap-2 sm:gap-6 max-sm:flex-col max-sm:items-center justify-center pbs-0 sm:pbe-10 sm:px-16'>
        <Button onClick={() => setOpen(false)} disabled={isLoading} variant='outlined' className='is-full sm:is-auto'>
          BATALKAN
        </Button>
        <LoadingButton
          startIcon={<></>}
          loading={isLoading}
          loadingPosition='start'
          variant='contained'
          onClick={handleSubmit(onSubmitHandler)}
          className='px-8 is-full !ml-0 sm:is-auto'
        >
          UBAH EMAIL
        </LoadingButton>
      </DialogActions>
    </Dialog>
  )
}

export default EmailDialog
