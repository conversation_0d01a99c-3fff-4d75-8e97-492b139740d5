import { useUpdateUser } from '@/api/services/user/mutation'
import { useMenu } from '@/components/menu/contexts/menuContext'
import { useAuth } from '@/contexts/AuthContext'
import { AddUserInput } from '@/pages/user/list/components/user/list/AddUserDrawer'
import { UserType } from '@/types/userTypes'
import LoadingButton from '@mui/lab/LoadingButton'
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Grid,
  IconButton,
  TextField,
  Typography
} from '@mui/material'
import { useEffect } from 'react'
import { Controller, useFormContext } from 'react-hook-form'
import { toast } from 'react-toastify'

type Props = {
  open: boolean
  setOpen: (open: boolean) => void
  userData: UserType
}

const PhoneNumberDialog = ({ open, setOpen, userData }: Props) => {
  const { fetchUserProfile } = useAuth()
  const { setConfirmState } = useMenu()

  const { mutate: updateAccount, isLoading } = useUpdateUser()
  const { control, handleSubmit, reset, getValues } = useFormContext<AddUserInput>()
  const handleClose = () => {
    setOpen(false)
  }

  const onSubmitHandler = (dto: AddUserInput) => {
    setConfirmState({
      open: true,
      title: 'Ubah Nomor Handphone',
      content: 'Apakah kamu yakin ingin mengubah nomor handphone kamu?',
      confirmText: 'Simpan',
      onConfirm: () => {
        updateAccount(
          {
            userId: 'me',
            phoneNumber: dto.phoneNumber
          },
          {
            onSuccess: () => {
              toast.success('Nomor Handphone berhasil diubah')
              reset()
              fetchUserProfile()
              handleClose()
            }
          }
        )
      }
    })
  }

  useEffect(() => {
    if (open) {
      reset({
        ...getValues(),
        phoneNumber: null
      })
    }
  }, [open])

  return (
    <Dialog open={open} onClose={handleClose}>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
        Ubah Nomor Handphone
        <Typography component='span' className='flex flex-col text-center'>
          Ubah Nomor Handphone kamu yang terdaftar di Data User
        </Typography>
      </DialogTitle>
      <DialogContent className='pbs-0 sm:pbe-16 sm:px-12 !py-4'>
        <IconButton onClick={() => setOpen(false)} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <Grid container spacing={5}>
          <Grid item xs={12}>
            <div className='flex flex-col gap-2 bg-[#4C4E640D] p-4'>
              <small>Nomor Handphone</small>
              <Typography>{userData?.phoneNumber}</Typography>
            </div>
          </Grid>
          <Grid item xs={12}>
            <Controller
              rules={{ required: true }}
              control={control}
              name='phoneNumber'
              render={({ field, fieldState: { error } }) => (
                <TextField
                  {...field}
                  fullWidth
                  error={!!error}
                  type='tel'
                  label='Nomor Handphone'
                  placeholder='Masukkan Nomor Handphone'
                />
              )}
            />
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions className='gap-2 sm:gap-6 max-sm:flex-col max-sm:items-center justify-center pbs-0 sm:pbe-10 sm:px-16'>
        <Button onClick={() => setOpen(false)} disabled={isLoading} variant='outlined' className='is-full sm:is-auto'>
          BATALKAN
        </Button>
        <LoadingButton
          startIcon={<></>}
          loading={isLoading}
          loadingPosition='start'
          variant='contained'
          onClick={handleSubmit(onSubmitHandler)}
          className='px-8 is-full !ml-0 sm:is-auto'
        >
          SIMPAN NOMOR
        </LoadingButton>
      </DialogActions>
    </Dialog>
  )
}

export default PhoneNumberDialog
