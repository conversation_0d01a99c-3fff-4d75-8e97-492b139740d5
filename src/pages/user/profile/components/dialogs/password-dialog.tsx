import { useUpdateUser } from '@/api/services/user/mutation'
import { useMenu } from '@/components/menu/contexts/menuContext'
import { useAuth } from '@/contexts/AuthContext'
import { AddUserInput, passwordValidation } from '@/pages/user/list/components/user/list/AddUserDrawer'
import { UserType } from '@/types/userTypes'
import LoadingButton from '@mui/lab/LoadingButton'
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Grid,
  IconButton,
  InputAdornment,
  TextField,
  Typography
} from '@mui/material'
import { useEffect, useState } from 'react'
import { Controller, useFormContext } from 'react-hook-form'
import { toast } from 'react-toastify'

type Props = {
  open: boolean
  setOpen: (open: boolean) => void
  userData: UserType
}

const ChangePasswordDialog = ({ open, setOpen, userData }: Props) => {
  const { fetchUserProfile } = useAuth()
  const { setConfirmState } = useMenu()

  const { mutate: updateAccount, isLoading } = useUpdateUser()
  const { control, handleSubmit, reset, getValues } = useFormContext<AddUserInput>()
  const [isPasswordShown, setIsPasswordShown] = useState(false)

  const handleClose = () => {
    setOpen(false)
  }

  const onSubmitHandler = (dto: AddUserInput) => {
    setConfirmState({
      open: true,
      title: 'Ganti Password',
      content: 'Apakah kamu yakin akan mengganti password akunmu?',
      confirmText: 'Ganti Password',
      onConfirm: () => {
        updateAccount(
          {
            userId: 'me',
            currentPassword: dto.currentPassword,
            password: dto.password
          },
          {
            onSuccess: () => {
              toast.success('Password berhasil diubah')
              reset()
              fetchUserProfile()
              handleClose()
            }
          }
        )
      }
    })
  }

  useEffect(() => {
    if (open) {
      reset({
        ...getValues()
      })
    }
  }, [open])

  return (
    <Dialog open={open} onClose={handleClose}>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
        Atur Password Baru
        <Typography component='span' className='flex flex-col text-center'>
          Masukkan password baru kamu
        </Typography>
      </DialogTitle>
      <DialogContent className='pbs-0 sm:pbe-16 sm:px-12 !py-4'>
        <IconButton onClick={() => setOpen(false)} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <Grid container spacing={5}>
          <Grid item xs={12}>
            <Controller
              name='currentPassword'
              rules={{ required: 'Wajib diisi' }}
              control={control}
              render={({ field: { onChange, value, ref }, fieldState: { error } }) => (
                <div className='flex flex-col gap-1'>
                  <TextField
                    fullWidth
                    label='Password Saat Ini'
                    id='confirm-password'
                    type={'password'}
                    onChange={e => onChange(e.target.value)}
                    value={value}
                    ref={ref}
                    error={!!error}
                    helperText={error?.message}
                  />
                </div>
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              name='password'
              rules={{
                required: 'Password wajib diisi',
                minLength: { value: 8, message: 'Password wajib minimal 8 karakter' },
                pattern: {
                  value: passwordValidation,
                  message: 'Password wajib minimal menggunakan 1 huruf besar, 1 huruf kecil dan 1 angka'
                }
              }}
              control={control}
              render={({ field: { onChange, value, ref }, fieldState: { error } }) => (
                <div className='flex flex-col gap-1'>
                  <TextField
                    fullWidth
                    label='Password Baru'
                    id='password'
                    type={isPasswordShown ? 'text' : 'password'}
                    onChange={e => onChange(e.target.value)}
                    value={value}
                    ref={ref}
                    error={!!error}
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position='end'>
                          <IconButton
                            size='small'
                            edge='end'
                            onClick={() => setIsPasswordShown(current => !current)}
                            onMouseDown={e => e.preventDefault()}
                          >
                            <i className={isPasswordShown ? 'ri-eye-off-line' : 'ri-eye-line'} />
                          </IconButton>
                        </InputAdornment>
                      ),
                      autoComplete: 'new-password'
                    }}
                    helperText={
                      !!error
                        ? error?.message
                        : 'Password wajib minimal 8 karakter, menggunakan huruf besar, huruf kecil dan angka'
                    }
                  />
                </div>
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              name='retypePassword'
              rules={{
                required: 'Konfirmasi password wajib diisi',
                validate: (value, formValues) => value === formValues.password || 'Konfirmasi password tidak sesuai'
              }}
              control={control}
              render={({ field: { onChange, value, ref }, fieldState: { error } }) => (
                <div className='flex flex-col gap-1'>
                  <TextField
                    fullWidth
                    label='Konfirmasi Password Baru'
                    id='confirm-password'
                    type={isPasswordShown ? 'text' : 'password'}
                    onChange={e => onChange(e.target.value)}
                    value={value}
                    ref={ref}
                    error={!!error}
                    helperText={error?.message}
                  />
                </div>
              )}
            />
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions className='gap-2 sm:gap-6 max-sm:flex-col max-sm:items-center justify-center pbs-0 sm:pbe-10 sm:px-16'>
        <Button onClick={() => setOpen(false)} disabled={isLoading} variant='outlined' className='is-full sm:is-auto'>
          BATALKAN
        </Button>
        <LoadingButton
          startIcon={<></>}
          loading={isLoading}
          loadingPosition='start'
          variant='contained'
          onClick={handleSubmit(onSubmitHandler)}
          className='px-8 is-full !ml-0 sm:is-auto'
        >
          GANTI PASSWORD
        </LoadingButton>
      </DialogActions>
    </Dialog>
  )
}

export default ChangePasswordDialog
