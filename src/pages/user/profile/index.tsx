import { useAuth } from '@/contexts/AuthContext'
import { <PERSON><PERSON>, <PERSON>, CardContent, Grid, Typography } from '@mui/material'
import { AccountPayload } from '@/types/payload'
import { FormProvider, useForm, useWatch } from 'react-hook-form'
import UserProfileCard from './components/UserProfileCard'
import { AddUserInput } from '../list/components/user/list/AddUserDrawer'
import { useUpdateAccount } from '@/api/services/account/mutation'
import { useUpdateUser } from '@/api/services/user/mutation'
import { toast } from 'react-toastify'
import LoadingButton from '@mui/lab/LoadingButton'
import { useMenu } from '@/components/menu/contexts/menuContext'
import { useEffect } from 'react'
import { useQuery } from '@tanstack/react-query'
import UserQueryMethods, { ROLE_QUERY_KEY } from '@/api/services/user/query'
import PermissionListCard from './components/PermissionListCard'
import CompanyCard from './components/CompanyCard'
import InfoCard from './components/InfoCard'

const UserProfilePage = () => {
  const methods = useForm<AddUserInput>()
  const { fetchUserProfile, userProfile, removeAccount, activeAccount } = useAuth()
  const { setConfirmState } = useMenu()

  const { control, getValues, handleSubmit, reset } = methods

  const { mutate: updateAccount, isLoading } = useUpdateUser()

  const {
    data: roleData,
    refetch: fetchRoleData,
    isFetching: fetchRoleDataLoading,
    remove: removeRoleData
  } = useQuery({
    enabled: !!userProfile?.permissionGroupId,
    queryKey: [ROLE_QUERY_KEY, userProfile?.permissionGroupId],
    queryFn: () => UserQueryMethods.getRole(userProfile?.permissionGroupId)
  })

  const handleUserLogout = async () => {
    setConfirmState({
      open: true,
      title: 'Keluar dari Akun',
      content: 'Kamu yakin ingin keluar dari akun ini?',
      confirmText: 'Keluar',
      onConfirm: () => {
        try {
          removeAccount(activeAccount)
          // Sign out from the app
          // await signOut({ callbackUrl: process.env.NEXT_PUBLIC_APP_URL })
        } catch (error) {
          toast.error((error as Error).message)
          // Show above error in a toast like following
          // toastService.error((err as Error).message)
        }
      }
    })
  }

  const onSubmitHandler = (values: AddUserInput) => {
    setConfirmState({
      open: true,
      title: 'Ubah Profil',
      content: 'Apakah kamu yakin ingin mengubah profil kamu?',
      confirmText: 'Ubah Profil',
      onConfirm: () => {
        updateAccount(
          {
            userId: userProfile?.id,
            ...values
          },
          {
            onSuccess: () => {
              toast.success('Profil berhasil diubah')
              reset()
              fetchUserProfile()
            }
          }
        )
      }
    })
  }

  useEffect(() => {
    fetchUserProfile()
  }, [])

  return (
    <FormProvider {...methods}>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <div className='flex justify-between items-end mt-6'>
            <div className='flex flex-col'>
              <Typography variant='h4'>Akun Kamu</Typography>
              <Typography>Lihat detil akun Equalindo360 kamu</Typography>
            </div>
            <div className='flex justify-end'>
              <LoadingButton color='error' variant='outlined' loading={isLoading} onClick={handleUserLogout}>
                Keluar Dari Akun
              </LoadingButton>
            </div>
          </div>
        </Grid>
        <Grid item xs={12} md={6}>
          <Grid container spacing={5}>
            <Grid item xs={12}>
              <UserProfileCard />
            </Grid>
            <Grid item xs={12}>
              <PermissionListCard roleData={roleData} />
            </Grid>
          </Grid>
        </Grid>
        <Grid item xs={12} md={6}>
          <Grid container spacing={5}>
            <Grid item xs={12}>
              <CompanyCard />
            </Grid>
            <Grid item xs={12}>
              <InfoCard />
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </FormProvider>
  )
}

export default UserProfilePage
