import { CurrenciesType } from '@/types/currenciesTypes'
import { Button, Grid, IconButton, Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'

const columnHelper = createColumnHelper<CurrenciesType>()

type RowAction = {
  detail: (row: CurrenciesType) => void
  onDefault: (row: CurrenciesType) => void
  loading?: boolean
}

export const tableColumns = (rowActions: RowAction) => [
  columnHelper.accessor('code', {
    header: 'Kode'
  }),
  columnHelper.accessor('name', {
    header: 'Nama'
  }),
  columnHelper.accessor('symbol', {
    header: 'Simbol',
    cell: ({ row }) => row.original?.symbol ?? '-'
  }),
  columnHelper.accessor('isDefault', {
    header: 'Default',
    cell: ({ row }) =>
      row.original?.isDefault ? (
        <Typography color={'primary'}>Ya</Typography>
      ) : (
        <Typography color={'error'}>Tidak</Typography>
      )
  }),
  columnHelper.display({
    id: 'action',
    header: 'Action',
    cell: ({ row }) => (
      <Grid container spacing={1}>
        <Grid item>
          <Button
            size='small'
            variant='outlined'
            color='primary'
            disabled={row.original.isDefault || rowActions?.loading}
            onClick={() => rowActions?.onDefault(row.original)}
          >
            {row.original.isDefault ? 'Default' : 'Jadikan Default'}
          </Button>
        </Grid>
        <Grid item>
          <IconButton onClick={() => rowActions.detail(row.original)} disabled={rowActions?.loading}>
            <i className='ri-eye-line text-textSecondary' />
          </IconButton>
        </Grid>
      </Grid>
    )
  })
]
