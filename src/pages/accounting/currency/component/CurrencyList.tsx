import { useCallback, useEffect, useMemo, useState } from 'react'
import { Box, IconButton, Typography } from '@mui/material'
import Card from '@mui/material/Card'
import Button from '@mui/material/Button'

import {
  getCoreRowModel,
  useReactTable,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFacetedMinMaxValues,
  getPaginationRowModel,
  getSortedRowModel
} from '@tanstack/react-table'

import DebouncedInput from '@/components/DebounceInput'
import Table from '@/components/table'
// import { tableColumns } from '../config/table'
// import { useVendor } from '../context/VendorContext'
import { useRouter } from '@/routes/hooks'
import { useExportState } from '@/core/hooks/useExportImport'
import { ExportImportScope } from '@/types/exportImportTypes'
import ProcessingImportDialog from '@/components/dialogs/export-dialog'
import ImportDialog from '@/components/dialogs/import-dialog'
import MobileDropDown from '@/components/layout/shared/components/MobileDropDown'
import { useCurrencies } from '../context/CurrencyContext'
import { tableColumns } from '../config/table'
import { CurrenciesType } from '@/types/currenciesTypes'
import { useUpdateCurrency } from '@/api/services/company/mutation'
import { toast } from 'react-toastify'
import { useAuth } from '@/contexts/AuthContext'

const CurrenciesList = () => {
  const { refetchCurrenciesList: refetchCurrency } = useAuth()
  const {
    isMobile,
    currenciesParams,
    setPartialCurrenciesParams,
    setCurrenciesParams,
    handleAddClick,
    currenciesListResponse,
    handleDetail,
    fetchCurrenciesList
  } = useCurrencies()

  const { page, search, limit } = currenciesParams
  const { totalItems, totalPages } = currenciesListResponse

  const [addAnchorEl, setAddAnchorEl] = useState<null | HTMLElement>(null)
  const open = Boolean(addAnchorEl)

  // export boilerplate - start
  const [actionBtn, setBtn] = useState<boolean>(false)
  const [searchExtend, setSearchExtend] = useState<boolean>(false)
  const [exportDialogOpen, setExportDialogOpen] = useState(false)
  const onExportSuccess = useCallback((success: boolean) => {
    if (success) {
      setExportDialogOpen(true)
    }
  }, [])
  const { exportFn, isExporting } = useExportState(ExportImportScope.VENDOR, onExportSuccess)
  // export boilerplate - end

  const [importDialogOpen, setImportDialogOpen] = useState(false)

  const { mutate: updateCurrencyMutate, isLoading: updateCurrencyLoading } = useUpdateCurrency()

  const handleDefault = (data: CurrenciesType) => {
    updateCurrencyMutate(
      {
        id: data.id,
        code: data.code,
        name: data.name,
        symbol: data.symbol,
        isDefault: true,
        companyId: data?.companyId,
        payableAccountId: data.payableAccountId,
        receivableAccountId: data.receivableAccountId,
        purchaseDownPaymentAccountId: data.purchaseDownPaymentAccountId,
        salesDownPaymentAccountId: data.salesDownPaymentAccountId
      },
      {
        onSuccess: () => {
          toast.success(`${data.name} berhasil dijadikan default`)
          refetchCurrency()
          fetchCurrenciesList()
        }
      }
    )
  }

  const handleImportSubmit = () => {
    setImportDialogOpen(false)
  }

  const tableOptions = useMemo(
    () => ({
      data: currenciesListResponse?.items ?? [],
      columns: tableColumns({
        detail: data => handleDetail(data),
        onDefault: data => handleDefault(data),
        loading: updateCurrencyLoading
      }),
      initialState: {
        pagination: {
          pageSize: limit ?? 10,
          pageIndex: page - 1
        }
      },
      state: {
        pagination: {
          pageSize: currenciesListResponse.limit,
          pageIndex: currenciesListResponse.page - 1
        }
      },
      manualPagination: true,
      rowCount: totalItems,
      pageCount: totalPages,
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [limit, page, currenciesListResponse, updateCurrencyLoading]
  )

  // TODO: MOVE THIS SHIT
  const table = useReactTable<any>(tableOptions)

  useEffect(() => {
    setCurrenciesParams({
      limit: 10,
      page: 1
    })
  }, [])

  const actionButton = (
    <>
      {/* <Button
        color='secondary'
        variant='outlined'
        startIcon={<i className='ri-upload-2-line' />}
        className='is-full sm:is-auto'
        onClick={() => exportFn({ search })}
        disabled={isExporting}
      >
        Ekspor
      </Button>
      <Button
        color='primary'
        variant='outlined'
        startIcon={<i className='mdi-file-document-outline' />}
        className='is-full sm:is-auto'
        onClick={() => setImportDialogOpen(true)}
      >
        Impor List
      </Button> */}
    </>
  )

  return (
    <>
      <ProcessingImportDialog
        open={exportDialogOpen}
        onClose={() => setExportDialogOpen(false)}
        contentScope='Vendor'
      />
      <ImportDialog
        open={importDialogOpen}
        scope={ExportImportScope.VENDOR}
        onSubmit={handleImportSubmit}
        setOpen={() => setImportDialogOpen(!importDialogOpen)}
      />
      <Card>
        <div className='flex justify-between gap-4 p-5 flex-row items-start sm:flex-row'>
          {searchExtend ? (
            <div className='flex gap-4 items-center is-full flex-col sm:flex-row'>
              <DebouncedInput
                value={search}
                onChange={value => setCurrenciesParams(prev => ({ ...prev, page: 1, search: value as string }))}
                onBlur={() => setSearchExtend(false)}
                placeholder='Cari'
                className='is-full'
              />
            </div>
          ) : !isMobile ? (
            <div className='flex gap-4 items-center is-full sm:is-auto flex-col sm:flex-row'>
              <DebouncedInput
                value={search}
                onChange={value => setCurrenciesParams(prev => ({ ...prev, page: 1, search: value as string }))}
                placeholder='Cari'
                className='is-full sm:is-auto'
              />
            </div>
          ) : (
            <IconButton onClick={() => setSearchExtend(true)} className='bg-[#4C4E640D]/5 rounded-[8px]'>
              <i className='ri-search-line' />
            </IconButton>
          )}
          {!searchExtend && (
            <div className='flex items-center justify-end md:justify-between gap-x-4 max-sm:gap-y-4 is-full flex-row sm:is-auto'>
              {!isMobile ? (
                actionButton
              ) : (
                <IconButton onClick={() => setBtn(true)} className='bg-[#4C4E640D]/5 rounded-[8px]'>
                  <i className='pepicons-pop--dots-y' />
                </IconButton>
              )}
              <Button
                variant='contained'
                aria-haspopup='true'
                onClick={handleAddClick}
                aria-expanded={open ? 'true' : undefined}
                startIcon={<i className='ri-add-circle-line' />}
                aria-controls={open ? 'user-view-overview-export' : undefined}
                className='is-auto sm:is-auto'
              >
                Tambah Mata Uang
              </Button>
            </div>
          )}
        </div>
        <Table
          table={table}
          emptyLabel={
            <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
              <Typography> Belum ada Mata Uang</Typography>
              <Typography className='text-sm text-gray-400'>
                Semua mata uang yang telah dibuat akan ditampilkan di sini
              </Typography>
            </td>
          }
          onRowsPerPageChange={pageSize => {
            if (pageSize > totalItems) {
              setCurrenciesParams(prev => ({ ...prev, limit: totalItems, page: 1 }))
            } else {
              setPartialCurrenciesParams('limit', pageSize)

              const maxPage = Math.ceil(totalItems / pageSize)
              if (page > maxPage) {
                setCurrenciesParams(prev => ({ ...prev, page: maxPage }))
              }
            }
          }}
          onPageChange={pageIndex => setPartialCurrenciesParams('page', pageIndex)}
        />
      </Card>
      <MobileDropDown className='z-1' open={actionBtn} onClose={() => setBtn(false)} onOpen={() => setBtn(true)}>
        <Typography sx={{ marginTop: 2 }} align='center' variant='h5'>
          Action
        </Typography>
        <Box className='flex gap-2 p-4 pb-2 flex-col'>{actionButton}</Box>
      </MobileDropDown>
    </>
  )
}

export default CurrenciesList
