import { MouseEvent, useCallback, useEffect, useMemo, useState } from 'react'
import { Box, FormControl, IconButton, InputLabel, Menu, MenuItem, Select, Typography } from '@mui/material'
import Card from '@mui/material/Card'
import Button from '@mui/material/Button'

import {
  getCoreRowModel,
  useReactTable,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFacetedMinMaxValues,
  getPaginationRowModel,
  getSortedRowModel
} from '@tanstack/react-table'

import DebouncedInput from '@/components/DebounceInput'
import Table from '@/components/table'
// import { tableColumns } from '../config/table'
// import { useVendor } from '../context/VendorContext'
import { useRouter } from '@/routes/hooks'
import { useExportState } from '@/core/hooks/useExportImport'
import { ExportImportScope } from '@/types/exportImportTypes'
import ProcessingImportDialog from '@/components/dialogs/export-dialog'
import ImportDialog from '@/components/dialogs/import-dialog'
import MobileDropDown from '@/components/layout/shared/components/MobileDropDown'
import { useAccount } from '../context/AccountContext'
import { tableColumns } from '../config/table'

const AccountsList = () => {
  const router = useRouter()

  const {
    isMobile,
    accountParams,
    setPartialAccountsParams,
    setAccountsParams,
    handleAddClick,
    handleDeleteRow,
    accountListResponse,
    handleDetail,
    accountTypeList
  } = useAccount()

  const { page, search, limit, accountTypeIds } = accountParams
  const { totalItems, totalPages } = accountListResponse

  const [addAnchorEl, setAddAnchorEl] = useState<null | HTMLElement>(null)
  const open = Boolean(addAnchorEl)

  // export boilerplate - start
  const [actionBtn, setBtn] = useState<boolean>(false)
  const [searchExtend, setSearchExtend] = useState<boolean>(false)
  const [exportDialogOpen, setExportDialogOpen] = useState(false)
  const onExportSuccess = useCallback((success: boolean) => {
    if (success) {
      setExportDialogOpen(true)
    }
  }, [])
  const { exportFn, isExporting } = useExportState(ExportImportScope.VENDOR, onExportSuccess)
  // export boilerplate - end

  const [importDialogOpen, setImportDialogOpen] = useState(false)

  const handleImportSubmit = () => {
    setImportDialogOpen(false)
  }

  const tableOptions = useMemo(
    () => ({
      data: accountListResponse?.items ?? [],
      columns: tableColumns({
        detail: data => handleDetail(data),
        delete: data => handleDeleteRow(data.id),
        accountType: accountTypeList
      }),
      initialState: {
        pagination: {
          pageSize: limit ?? 10,
          pageIndex: page - 1
        }
      },
      state: {
        pagination: {
          pageSize: accountListResponse.limit,
          pageIndex: accountListResponse.page - 1
        }
      },
      manualPagination: true,
      rowCount: totalItems,
      pageCount: totalPages,
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [limit, page, accountListResponse, accountTypeList]
  )

  // TODO: MOVE THIS SHIT
  const table = useReactTable<any>(tableOptions)

  const handleAddClose = () => {}

  const actionButton = (
    <>
      {/* <Button
        color='secondary'
        variant='outlined'
        startIcon={<i className='ri-upload-2-line' />}
        className='is-full sm:is-auto'
        onClick={() => exportFn({ search })}
        disabled={isExporting}
      >
        Ekspor
      </Button>
      <Button
        color='primary'
        variant='outlined'
        startIcon={<i className='mdi-file-document-outline' />}
        className='is-full sm:is-auto'
        onClick={() => setImportDialogOpen(true)}
      >
        Impor List
      </Button> */}
    </>
  )

  return (
    <>
      <ProcessingImportDialog
        open={exportDialogOpen}
        onClose={() => setExportDialogOpen(false)}
        contentScope='Vendor'
      />
      <ImportDialog
        open={importDialogOpen}
        scope={ExportImportScope.VENDOR}
        onSubmit={handleImportSubmit}
        setOpen={() => setImportDialogOpen(!importDialogOpen)}
      />
      <Card>
        <div className='flex justify-between gap-4 p-5 flex-row items-start sm:flex-row'>
          {searchExtend ? (
            <div className='flex gap-4 items-center is-full flex-col sm:flex-row'>
              <DebouncedInput
                value={search}
                onChange={value => setAccountsParams(prev => ({ ...prev, page: 1, search: value as string }))}
                onBlur={() => setSearchExtend(false)}
                placeholder='Cari'
                className='is-full'
              />
            </div>
          ) : !isMobile ? (
            <div className='flex gap-4 items-center is-full sm:is-auto flex-col sm:flex-row'>
              <DebouncedInput
                value={search}
                onChange={value => setAccountsParams(prev => ({ ...prev, page: 1, search: value as string }))}
                placeholder='Cari'
                className='is-full sm:is-auto'
              />
              <FormControl size='small' fullWidth className='w-[200px]'>
                <InputLabel id='salary-type'>Tipe Akun</InputLabel>
                <Select
                  size='small'
                  key={JSON.stringify(accountTypeIds)}
                  value={accountTypeIds}
                  labelId='salary-type'
                  label='Tipe Akun'
                  required
                  variant='outlined'
                  placeholder='Contoh: Perkiraan'
                  onChange={e => {
                    if (e.target.value === '') {
                      setPartialAccountsParams('accountTypeIds', undefined)
                    } else {
                      setPartialAccountsParams('accountTypeIds', e.target.value)
                    }
                  }}
                >
                  <MenuItem value=''>Semua Tipe</MenuItem>
                  {accountTypeList.map(type => (
                    <MenuItem key={type.id} value={type.id}>
                      {type.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </div>
          ) : (
            <IconButton onClick={() => setSearchExtend(true)} className='bg-[#4C4E640D]/5 rounded-[8px]'>
              <i className='ri-search-line' />
            </IconButton>
          )}
          {!searchExtend && (
            <div className='flex items-center justify-end md:justify-between gap-x-4 max-sm:gap-y-4 is-full flex-row sm:is-auto'>
              {!isMobile ? (
                actionButton
              ) : (
                <IconButton onClick={() => setBtn(true)} className='bg-[#4C4E640D]/5 rounded-[8px]'>
                  <i className='pepicons-pop--dots-y' />
                </IconButton>
              )}
              <Button
                variant='contained'
                aria-haspopup='true'
                onClick={handleAddClick}
                aria-expanded={open ? 'true' : undefined}
                startIcon={<i className='ri-add-circle-line' />}
                aria-controls={open ? 'user-view-overview-export' : undefined}
                className='is-auto sm:is-auto'
              >
                Tambah Akun
              </Button>
            </div>
          )}
        </div>
        <Table
          table={table}
          emptyLabel={
            <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
              <Typography> Belum ada Akun</Typography>
              <Typography className='text-sm text-gray-400'>
                Semua akun untuk perkiraan yang telah dibuat akan ditampilkan di sini
              </Typography>
            </td>
          }
          onRowsPerPageChange={pageSize => {
            if (pageSize > totalItems) {
              setAccountsParams(prev => ({ ...prev, limit: totalItems, page: 1 }))
            } else {
              setPartialAccountsParams('limit', pageSize)

              const maxPage = Math.ceil(totalItems / pageSize)
              if (page > maxPage) {
                setAccountsParams(prev => ({ ...prev, page: maxPage }))
              }
            }
          }}
          onPageChange={pageIndex => setPartialAccountsParams('page', pageIndex)}
        />
      </Card>
      <MobileDropDown className='z-1' open={actionBtn} onClose={() => setBtn(false)} onOpen={() => setBtn(true)}>
        <Typography sx={{ marginTop: 2 }} align='center' variant='h5'>
          Action
        </Typography>
        <Box className='flex gap-2 p-4 pb-2 flex-col'>{actionButton}</Box>
      </MobileDropDown>
    </>
  )
}

export default AccountsList
