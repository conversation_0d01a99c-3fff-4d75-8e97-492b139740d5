import truncateString from '@/core/utils/truncate'
import { AccountMasterType, AccountType } from '@/types/accountTypes'
import { SalaryType } from '@/types/salaryTypes'
import { classNames, formatThousandSeparator, thousandSeparator, toCurrency, toTitleCase } from '@/utils/helper'
import { Chip, Grid, IconButton, Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'

const columnHelper = createColumnHelper<AccountType>()

type RowAction = {
  detail: (row: AccountType) => void
  delete: (row: AccountType) => void
  accountType?: AccountMasterType[]
}

export const tableColumns = (rowActions: RowAction) => [
  columnHelper.accessor('code', {
    header: 'Kode Perkiraan',
    cell: ({ row }) => {
      if (row.original?.parent?.code) {
        return <Typography className='ml-6'>{row.original?.code}</Typography>
      }
      return (
        <Typography className={classNames('ml-2', row.original?.level === 0 && 'font-semibold text-textPrimary')}>
          {row.original?.code}
        </Typography>
      )
    }
  }),
  columnHelper.accessor('name', {
    header: 'Nama',
    cell: ({ row }) => (
      <Typography className={classNames(row.original?.level === 0 && 'font-semibold text-textPrimary')}>
        {row.original?.name}
      </Typography>
    )
  }),
  columnHelper.accessor('accountTypeId', {
    header: 'Tipe Akun',
    cell: ({ row }) => {
      const accountType = rowActions.accountType?.find(a => a.id === row.original?.accountTypeId)
      return (
        <Typography className={classNames(row.original?.level === 0 && 'font-semibold text-textPrimary')}>
          {accountType?.name}
        </Typography>
      )
    }
  }),
  columnHelper.accessor('balance', {
    header: 'Saldo',
    meta: {
      headerAlign: 'right'
    },
    cell: ({ row }) => {
      if (row.original?.balance) {
        return (
          <Typography
            className={classNames(
              row.original?.level === 0 && 'font-semibold text-textPrimary',
              row.original?.balance < 0 ? 'text-error' : ''
            )}
            align='right'
          >
            {row.original?.balance < 0
              ? `(${formatThousandSeparator(Math.abs(row.original.balance))})`
              : formatThousandSeparator(row.original.balance)}
          </Typography>
        )
      }
      return (
        <Typography
          className={classNames('mr-4', row.original?.level === 0 && 'font-semibold text-textPrimary')}
          align='right'
        >
          0
        </Typography>
      )
    }
  }),
  columnHelper.display({
    id: 'action',
    header: 'Action',
    cell: ({ row }) => (
      <Grid container spacing={1}>
        <Grid item>
          <IconButton onClick={() => rowActions.delete(row.original)}>
            <i className='ri-delete-bin-7-line text-textSecondary' />
          </IconButton>
        </Grid>
        <Grid item>
          <IconButton onClick={() => rowActions.detail(row.original)}>
            <i className='ri-eye-line text-textSecondary' />
          </IconButton>
        </Grid>
      </Grid>
    )
  })
]
