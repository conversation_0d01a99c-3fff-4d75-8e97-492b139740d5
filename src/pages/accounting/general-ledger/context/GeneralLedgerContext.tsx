import { defaultListData } from '@/api/queryClient'
import AccountsQueryMethods, {
  GENERAL_LEDGER_LIST_QUERY_KEY,
  GENERAL_LEDGER_QUERY_KEY
} from '@/api/services/account/query'
import usePartialState from '@/core/hooks/usePartialState'
import { useRouter } from '@/routes/hooks'
import { GeneralLedgerLogType, GeneralLedgerParams, GeneralLedgerType } from '@/types/accountTypes'
import { QueryFn } from '@/types/alias'
import { ListResponse } from '@/types/api'
import { GeneralLedgerPayload, GeneralLedgerTypeEnum, ListParams } from '@/types/payload'
import { isNullOrUndefined } from '@/utils/helper'
import { zodResolver } from '@hookform/resolvers/zod'
import { useQuery } from '@tanstack/react-query'
import React, { createContext, useEffect, useState } from 'react'
import { FormProvider, useForm } from 'react-hook-form'
import { useParams } from 'react-router-dom'
import z, { array, number, object, string } from 'zod'

type GeneralLedgerContextProps = {
  generalLedgerParams: GeneralLedgerParams
  setPartialGeneralLedgerParams: (key: keyof GeneralLedgerParams, value: any) => void
  setGeneralLedgerParams: React.Dispatch<React.SetStateAction<GeneralLedgerParams>>
  generalLedgerList: ListResponse<GeneralLedgerType>
  fetchGeneralLedgerList: QueryFn<ListResponse<GeneralLedgerType>>
  fetchGeneralLedgerData: QueryFn<GeneralLedgerType>
  fetchGeneralLedgerLogs: QueryFn<ListResponse<GeneralLedgerLogType>>
  generalLedgerData: GeneralLedgerType
  generalLedgerLogs: ListResponse<GeneralLedgerLogType>
  router: ReturnType<typeof useRouter>
  isEditable: boolean
  setIsEditable: React.Dispatch<React.SetStateAction<boolean>>
  allGeneralLedgers: GeneralLedgerType[]
  fetchAllGeneralLedgers: QueryFn<GeneralLedgerType[]>
}

const GeneralLedgerContext = createContext<GeneralLedgerContextProps>({} as GeneralLedgerContextProps)

export const useGeneralLedger = () => {
  const context = React.useContext(GeneralLedgerContext)
  if (context === undefined) {
    throw new Error('useGeneralLedger must be used within a GeneralLedgerProvider')
  }
  return context
}

const GeneralLedgerSchema = object({
  type: z.enum([
    'GENERAL',
    'SALES_TRANSACTION',
    'PURCHASE_TRANSACTION',
    'CASH_RECEIPT',
    'CASH_PAYMENT',
    'ADJUSTING_ENTRIES',
    'OTHER_TRANSACTION'
  ]),
  description: string({ message: 'Wajib Diisi' }).min(1, { message: 'Wajib Diisi' }),
  transactionDate: string(),
  lines: array(
    object({
      accountId: string(),
      type: z.enum(['DEBIT', 'CREDIT']),
      amount: number(),
      description: string().optional().nullable()
    })
  ).min(1),
  siteId: string().optional().nullable(),
  projectId: string().optional().nullable(),
  projectLabelId: number().optional().nullable(),
  departmentId: string().optional().nullable(),
  currencyId: string().optional().nullable(),
  exchangeRate: number().optional().nullable(),
  unitId: string().optional().nullable()
}).superRefine(({ exchangeRate, currencyId }, ctx) => {
  if (isNullOrUndefined(exchangeRate) && !!currencyId) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: 'Wajib diisi',
      path: ['exchangeRate']
    })
  }
})

export const GeneralLedgerProvider = ({ children }: { children: React.ReactNode }) => {
  const router = useRouter()
  const params = useParams()
  const methods = useForm<GeneralLedgerPayload>({
    resolver: zodResolver(GeneralLedgerSchema)
  })
  const [selectedLedgerId, setSelectedLedgerId] = useState<string>()
  const [isEditable, setIsEditable] = useState(false)
  const [generalLedgerParams, setPartialGeneralLedgerParams, setGeneralLedgerParams] =
    usePartialState<GeneralLedgerParams>({
      limit: 10,
      page: 1,
      type: GeneralLedgerTypeEnum.GENERAL
    })

  const { data: generalLedgerList, refetch: fetchGeneralLedgerList } = useQuery({
    queryKey: [GENERAL_LEDGER_LIST_QUERY_KEY, JSON.stringify(generalLedgerParams)],
    queryFn: () => AccountsQueryMethods.getGeneralLedgerList(generalLedgerParams),
    placeholderData: defaultListData as ListResponse<GeneralLedgerType>
  })

  const { data: allGeneralLedgers, refetch: fetchAllGeneralLedgers } = useQuery({
    queryKey: [GENERAL_LEDGER_LIST_QUERY_KEY, 'ALL_JOURNALS'],
    queryFn: () => {
      const now = new Date()
      const startDate = new Date(now.getFullYear(), 0, 1)
      const endDate = new Date(now.getFullYear(), 11, 31)
      const format = (date: Date) => date.toISOString().slice(0, 10)
      return AccountsQueryMethods.getAllGeneralLedgers({
        startDate: format(startDate),
        endDate: format(endDate)
      })
    },
    placeholderData: [] as GeneralLedgerType[]
  })

  const { data: generalLedgerData, refetch: fetchGeneralLedgerData } = useQuery({
    enabled: !!selectedLedgerId,
    queryKey: [GENERAL_LEDGER_QUERY_KEY, selectedLedgerId],
    queryFn: () => AccountsQueryMethods.getGeneralLedger(selectedLedgerId)
  })

  const { data: generalLedgerLogs, refetch: fetchGeneralLedgerLogs } = useQuery({
    enabled: !!selectedLedgerId,
    queryKey: [GENERAL_LEDGER_LIST_QUERY_KEY, 'LOGS', selectedLedgerId],
    queryFn: () => AccountsQueryMethods.getGeneralLedgerLogs(selectedLedgerId)
  })

  useEffect(() => {
    if (params?.ledgerId) {
      setSelectedLedgerId(params.ledgerId)
    }
  }, [params])

  const value = {
    generalLedgerParams,
    setGeneralLedgerParams,
    setPartialGeneralLedgerParams,
    generalLedgerList,
    fetchGeneralLedgerList,
    fetchGeneralLedgerData,
    fetchGeneralLedgerLogs,
    generalLedgerData,
    generalLedgerLogs,
    router,
    setIsEditable,
    isEditable,
    allGeneralLedgers,
    fetchAllGeneralLedgers
  }
  return (
    <GeneralLedgerContext.Provider value={value}>
      <FormProvider {...methods}>{children}</FormProvider>
    </GeneralLedgerContext.Provider>
  )
}
