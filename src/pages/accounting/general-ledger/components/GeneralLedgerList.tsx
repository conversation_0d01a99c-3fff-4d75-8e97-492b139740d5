import DebouncedInput from '@/components/DebounceInput'
import { <PERSON>ton, Card, Typography } from '@mui/material'
import { useGeneralLedger } from '../context/GeneralLedgerContext'
import { useMemo } from 'react'
import { tableColumns } from '../config/table'
import { pdf } from '@react-pdf/renderer'
import { saveAs } from 'file-saver'
import PdfJournal from './PdfJournal'
import {
  getCoreRowModel,
  getFacetedUniqueValues,
  getFacetedRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  getFacetedMinMaxValues,
  useReactTable
} from '@tanstack/react-table'
import Table from '@/components/table'
import { useRouter } from '@/routes/hooks'
import Permission from '@/core/components/Permission'

const GeneralLedgerList = () => {
  const router = useRouter()
  const {
    generalLedgerParams,
    setPartialGeneralLedgerParams,
    setGeneralLedgerParams,
    generalLedgerList,
    fetchAllGeneralLedgers
  } = useGeneralLedger()
  const { search, page } = generalLedgerParams
  const { items, limit, totalItems, totalPages } = generalLedgerList

  const handlePrint = () => {
    fetchAllGeneralLedgers().then(async response => {
      const data = response?.data ?? []
      const blob = await pdf(<PdfJournal allGeneralLedgers={data} />).toBlob()
      const now = new Date()
      const startDate = new Date(now.getFullYear(), 0, 1)
      const endDate = new Date(now.getFullYear(), 11, 31)
      const format = (date: Date) => date.toISOString().slice(0, 10)
      const filename = `Journal-Report-${format(startDate)}-${format(endDate)}.pdf`
      saveAs(blob, filename)
      1
    })
  }

  const tableOptions = useMemo(
    () => ({
      data: items ?? [],
      columns: tableColumns({ detail: row => router.push(`/accounting/general-ledger/list/${row.id}`) }),
      initialState: {
        pagination: {
          pageSize: generalLedgerParams.limit ?? 10,
          pageIndex: page - 1
        }
      },
      state: {
        pagination: {
          pageSize: limit,
          pageIndex: page - 1
        }
      },
      manualPagination: true,
      rowCount: totalItems,
      pageCount: totalPages,
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [generalLedgerList, generalLedgerParams]
  )

  const table = useReactTable<any>(tableOptions)

  return (
    <Card>
      <div className='flex justify-between gap-4 p-5 flex-col items-start sm:flex-row sm:items-center'>
        <div className='flex gap-4 items-center flex-col sm:flex-row is-full sm:is-auto'>
          <DebouncedInput
            value={search}
            onChange={value => setPartialGeneralLedgerParams('search', value)}
            placeholder='Cari'
            className='is-full sm:is-auto'
          />
        </div>
        <div className='flex items-center gap-x-4 max-sm:gap-y-4 is-full flex-col sm:is-auto sm:flex-row'>
          <Button
            color='secondary'
            variant='outlined'
            startIcon={<i className='ri-upload-2-line' />}
            className='is-full sm:is-auto'
            onClick={handlePrint}
          >
            Ekspor
          </Button>

          <Permission permission='journal.create'>
            <Button
              className='is-full sm:is-auto'
              onClick={() => router.push('/accounting/general-ledger/create')}
              variant='contained'
            >
              Tambah Pencatatan
            </Button>
          </Permission>
        </div>
      </div>
      <Table
        table={table}
        emptyLabel={
          <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
            <Typography>Belum ada Pencatatan</Typography>
            <Typography className='text-sm text-gray-400'>
              Semua Pencatatan Jurnal yang telah dibuat akan ditampilkan di sini
            </Typography>
          </td>
        }
        onRowsPerPageChange={pageSize => {
          if (pageSize > totalItems) {
            setGeneralLedgerParams(prev => ({ ...prev, limit: totalItems, page: 1 }))
          } else {
            setPartialGeneralLedgerParams('limit', pageSize)

            const maxPage = Math.ceil(totalItems / pageSize)
            if (page > maxPage) {
              setGeneralLedgerParams(prev => ({ ...prev, page: maxPage }))
            }
          }
        }}
        onPageChange={pageIndex => setPartialGeneralLedgerParams('page', pageIndex)}
      />
    </Card>
  )
}

export default GeneralLedgerList
