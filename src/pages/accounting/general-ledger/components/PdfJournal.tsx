'use client'

// React Imports
import React, { useMemo } from 'react'

// Third-party Imports
import { Page, Text, View, Document, StyleSheet, Font } from '@react-pdf/renderer'

// Type Imports
import { GeneralLedgerType } from '@/types/accountTypes'

// Font Registration
Font.register({
  family: 'Roboto',
  fonts: [
    {
      src: 'https://cdnjs.cloudflare.com/ajax/libs/ink/3.1.10/fonts/Roboto/roboto-regular-webfont.ttf',
      fontWeight: 'normal'
    },
    {
      src: 'https://cdnjs.cloudflare.com/ajax/libs/ink/3.1.10/fonts/Roboto/roboto-bold-webfont.ttf',
      fontWeight: 'bold'
    }
  ]
})

// Styles
const styles = StyleSheet.create({
  page: {
    fontFamily: 'Roboto',
    fontSize: 8,
    padding: 40,
    flexDirection: 'column',
    backgroundColor: '#fff'
  },
  header: {
    textAlign: 'center',
    marginBottom: 20
  },
  companyName: {
    fontSize: 12,
    fontWeight: 'bold'
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold'
  },
  dateRange: {
    fontSize: 9
  },
  filterContainer: {
    position: 'absolute',
    top: 40,
    right: 40,
    fontSize: 8
  },
  table: {
    width: '100%',
    display: 'flex',
    flexDirection: 'column'
  },
  tableHeader: {
    flexDirection: 'row',
    backgroundColor: '#f0f0f0',
    borderBottomWidth: 1,
    borderBottomColor: '#000',
    padding: 4
  },
  tableRow: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    paddingVertical: 2
  },
  col: {
    padding: 4,
    textAlign: 'left'
  },
  colTgl: { width: '15%' },
  colNoAkun: { width: '25%' },
  colNamaAkun: { width: '25%' },
  colDebit: { width: '15%', textAlign: 'right' },
  colKredit: { width: '15%', textAlign: 'right' },
  colNamaDep: { width: '23%' },
  groupHeader: {
    flexDirection: 'row',
    paddingVertical: 4,
    fontWeight: 'bold'
  },
  groupHeaderText: {
    marginLeft: '10%', // to align with No Akun column
    fontWeight: 'bold'
  },
  groupTotalRow: {
    flexDirection: 'row',
    borderTopWidth: 1,
    borderTopColor: '#000',
    borderBottomWidth: 2,
    borderBottomColor: '#000',
    marginTop: 4,
    marginBottom: 10,
    paddingVertical: 2,
    fontWeight: 'bold'
  },
  totalCell: {
    padding: 4,
    textAlign: 'right'
  }
})

// Helper to format currency
const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('id-ID', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(value)
}

const PdfJournal = ({ allGeneralLedgers = [] }: { allGeneralLedgers: GeneralLedgerType[] }) => {
  const groupedData = useMemo(() => {
    if (!allGeneralLedgers) return []

    // The data is already in the format of GeneralLedgerType which contains lines.
    // We just need to ensure it's not null or undefined.
    return allGeneralLedgers
  }, [allGeneralLedgers])

  return (
    <Document>
      <Page size='A4' style={styles.page}>
        <View style={styles.header}>
          <Text style={styles.companyName}>RIMBA PERKASA UTAMA, PT</Text>
          <Text style={styles.title}>Bukti Jurnal Umum</Text>
          <Text style={styles.dateRange}>Dari 01 Jan 2024 ke 31 Jan 2024</Text>
        </View>
        <View style={styles.filterContainer}>{/* <Text>Filter berdasarkan: Sumber, Tipe</Text> */}</View>

        <View style={styles.table}>
          <View style={styles.tableHeader}>
            <Text style={[styles.col, styles.colTgl, { fontWeight: 'bold' }]}>Tgl</Text>
            <Text style={[styles.col, styles.colNoAkun, { fontWeight: 'bold' }]}>No. Akun</Text>
            <Text style={[styles.col, styles.colNamaAkun, { fontWeight: 'bold' }]}>Nama Akun</Text>
            <Text style={[styles.col, styles.colDebit, { fontWeight: 'bold' }]}>Debit</Text>
            <Text style={[styles.col, styles.colKredit, { fontWeight: 'bold' }]}>Kredit</Text>
            <Text style={[styles.col, styles.colNamaDep, { fontWeight: 'bold' }]}>Departemen</Text>
          </View>

          {groupedData.map((group, groupIndex) => (
            <React.Fragment key={groupIndex}>
              <View style={styles.groupHeader}>
                <Text style={{ ...styles.col, ...styles.colTgl }}>
                  {new Date(group.transactionDate).toLocaleDateString('id-ID', {
                    day: '2-digit',
                    month: 'short',
                    year: 'numeric'
                  })}
                </Text>
                <Text style={{ ...styles.col, ...styles.colNoAkun }}>{group.number}</Text>
                <Text style={{ ...styles.col, ...styles.colNamaAkun, fontWeight: 'bold' }}>{group.description}</Text>
                <Text style={{ ...styles.col, ...styles.colNamaDep, fontWeight: 'bold' }}>
                  {group.department?.name ?? '-'}
                </Text>
              </View>
              {group.lines.map((line, itemIndex: number) => (
                <View style={styles.tableRow} key={itemIndex}>
                  <Text style={[styles.col, styles.colTgl]}>
                    {new Date(group.transactionDate).toLocaleDateString('id-ID', {
                      day: '2-digit',
                      month: 'short',
                      year: 'numeric'
                    })}
                  </Text>
                  <Text style={[styles.col, styles.colNoAkun]}>{line.account.code}</Text>
                  <Text style={[styles.col, styles.colNamaAkun]}>{line.account.name}</Text>
                  <Text style={[styles.col, styles.colDebit]}>{formatCurrency(line.debit || 0)}</Text>
                  <Text style={[styles.col, styles.colKredit]}>{formatCurrency(line.credit || 0)}</Text>
                  <Text style={[styles.col, styles.colNamaDep]}>{line.department?.name ?? '-'}</Text>
                </View>
              ))}
              <View style={styles.groupTotalRow}>
                <Text style={[styles.col, styles.colTgl]}></Text>
                <Text style={[styles.col, styles.colNoAkun]}></Text>
                <Text style={[styles.col, styles.colNamaAkun]}></Text>
                <Text style={[styles.col, styles.colDebit]}>{formatCurrency(group.totalDebit)}</Text>
                <Text style={[styles.col, styles.colKredit]}>{formatCurrency(group.totalCredit)}</Text>
                <Text style={[styles.col, styles.colNamaDep]}></Text>
              </View>
            </React.Fragment>
          ))}
        </View>
      </Page>
    </Document>
  )
}

export default PdfJournal
