import { GeneralLedgerType } from '@/types/accountTypes'
import { GeneralLedgerLineTypePayload, GeneralLedgerPayload } from '@/types/payload'
import { thousandSeparator } from '@/utils/helper'
import { IconButton, Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'
import { Control } from 'react-hook-form'

type GeneralLedgerTypeWithAction = GeneralLedgerLineTypePayload

const columnHelper = createColumnHelper<GeneralLedgerTypeWithAction>()

type RowActionType = {
  control?: Control<GeneralLedgerPayload>
  isEditable?: boolean
  delete?: (row: GeneralLedgerTypeWithAction, idx: number) => void
  edit?: (row: GeneralLedgerTypeWithAction, idx: number) => void
  journal?: GeneralLedgerType
}

export const tableColumns = ({ control, isEditable, journal, ...rowAction }: RowActionType) => [
  columnHelper.accessor('code', {
    header: 'Kode <PERSON>kun',
    meta: {
      headerAlign: 'left'
    }
  }),
  columnHelper.accessor('name', {
    header: '<PERSON><PERSON>kun',
    meta: {
      headerAlign: 'left'
    }
  }),
  column<PERSON>elper.display({
    id: 'debit',
    header: 'Debit',
    meta: {
      headerAlign: 'right'
    },
    cell: ({ row }) => (
      <Typography align='right'>
        {row.original?.type === 'DEBIT'
          ? `${thousandSeparator(Math.abs(row.original.amount), !!journal?.currencyId, journal?.currency?.symbol ?? undefined, 2)}`
          : thousandSeparator(0)}
      </Typography>
    )
  }),
  columnHelper.display({
    id: 'credit',
    header: 'Kredit',
    meta: {
      headerAlign: 'right'
    },
    cell: ({ row }) => (
      <Typography align='right'>
        {row.original?.type === 'CREDIT'
          ? `${thousandSeparator(Math.abs(row.original.amount), !!journal?.currencyId, journal?.currency?.symbol ?? undefined, 2)}`
          : thousandSeparator(0)}
      </Typography>
    )
  }),
  columnHelper.accessor('description', {
    header: 'Keterangan',
    size: 230,
    meta: {
      headerAlign: 'left'
    },
    cell: ({ row }) => (!!row.original.description ? row.original.description : '-')
  }),
  columnHelper.accessor('department.name', {
    header: 'Departemen',
    cell: ({ row }) => row.original.department?.name ?? '-'
  }),
  ...(!!isEditable
    ? [
        columnHelper.display({
          id: 'actions',
          header: 'Action',
          cell: ({ row }) => {
            return (
              <>
                <IconButton onClick={() => rowAction.edit(row.original, row.index)}>
                  <i className='ri-pencil-line' />
                </IconButton>
                <IconButton onClick={() => rowAction.delete(row.original, row.index)}>
                  <i className='ri-delete-bin-7-line' />
                </IconButton>
              </>
            )
          }
        })
      ]
    : [])
]
