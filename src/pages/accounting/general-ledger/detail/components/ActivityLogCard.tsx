// MUI Imports
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import TimelineDot from '@mui/lab/TimelineDot'
import TimelineItem from '@mui/lab/TimelineItem'
import TimelineContent from '@mui/lab/TimelineContent'
import TimelineSeparator from '@mui/lab/TimelineSeparator'
import TimelineConnector from '@mui/lab/TimelineConnector'
import Typography from '@mui/material/Typography'

import { Timeline } from '@/components/Timeline'
import { PrLogType, LogTypeStatus } from '@/types/prTypes'
import { Avatar } from '@mui/material'
import { formatDistanceToNow } from 'date-fns'
import { id } from 'date-fns/locale'
import { WarehouseLogType } from '@/types/appTypes'
import { WorkProcessLogStatus } from '@/types/wpTypes'
import { EndPeriodLogType, GeneralLedgerLogType } from '@/types/accountTypes'

type Props = {
  logList?: GeneralLedgerLogType[]
}

const ActivityLogCard = ({ logList = [] }: Props) => {
  return (
    <Card>
      <CardHeader title='Log Aktivitas' />
      <CardContent>
        <div className='max-h-[560px] overflow-auto'>
          {logList?.length > 0 ? (
            <Timeline>
              {logList?.map(log => {
                let dotColor: 'primary' | 'secondary' | 'error' | 'warning' | 'info' | 'success' | 'inherit' | 'grey' =
                  'primary'
                let title = ''
                const changes = ((JSON.parse(log.changes) as string[]) ?? []).map(change =>
                  change
                    .replaceAll('"', '')
                    .replaceAll('{changed}', '→')
                    .replaceAll('{added}', '')
                    .replaceAll('{removed}', '')
                )
                switch (log.status) {
                  case 'POSTED':
                    title = 'Jurnal Dibuat'
                    dotColor = 'success'
                    break
                  case 'UPDATED':
                    title = 'Jurnal Diperbarui'
                    dotColor = 'warning'
                    break
                  case 'LINE_ADDED':
                    title = 'Akun Transaksi Ditambah'
                    dotColor = 'success'
                    break

                  case 'LINE_REMOVED':
                    title = 'Akun Transaksi Dihapus'
                    dotColor = 'error'
                    break

                  case 'LINE_UPDATED':
                    title = 'Akun Transaksi Diperbarui'
                    dotColor = 'warning'
                    break

                  default:
                    break
                }
                return title ? (
                  <TimelineItem key={log.id} className='pt-2'>
                    <TimelineSeparator>
                      <TimelineDot color={dotColor} />
                      <TimelineConnector />
                    </TimelineSeparator>
                    <TimelineContent>
                      <div className='flex flex-wrap items-center justify-between gap-x-2 mbe-1'>
                        <Typography color='text.primary' className='font-medium text-base'>
                          {title}
                        </Typography>
                        <Typography variant='caption'>
                          {formatDistanceToNow(log.createdAt, {
                            locale: id,
                            addSuffix: true
                          })
                            .replace('sekitar ', '')
                            .replace('kurang dari ', '')}
                        </Typography>
                      </div>
                      {changes?.map(change => (
                        <Typography key={change} className='mbe-2 text-sm'>
                          {change}
                        </Typography>
                      ))}
                      {log.user ? (
                        <div className='flex items-center gap-3'>
                          <Avatar src={log?.user?.profilePictureUrl} />
                          <div className='flex flex-col'>
                            <Typography color='text.primary' className='font-medium'>
                              {log.user?.fullName}
                            </Typography>
                            <Typography variant='body2'>{log.user?.title}</Typography>
                          </div>
                        </div>
                      ) : null}
                    </TimelineContent>
                  </TimelineItem>
                ) : null
              })}
            </Timeline>
          ) : (
            <Typography className='w-full text-center py-8 font-medium'>Belum ada log</Typography>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

export default ActivityLogCard
