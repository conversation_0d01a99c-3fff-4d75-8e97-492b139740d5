import { isNullOrUndefined, thousandSeparator } from '@/utils/helper'
import { <PERSON><PERSON>, Card, CardContent, Typography } from '@mui/material'
import {
  getFacetedRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  getFacetedUniqueValues,
  useReactTable,
  getCoreRowModel,
  getFacetedMinMaxValues
} from '@tanstack/react-table'
import { tableColumns } from '../config/table'
import { useEffect, useMemo, useState } from 'react'
import Table from '@/components/table'
import { useForm, useFormContext, useWatch } from 'react-hook-form'
import { GeneralLedgerLineTypePayload, GeneralLedgerPayload, GeneralLedgerTypeEnum } from '@/types/payload'
import { useGeneralLedger } from '../../context/GeneralLedgerContext'
import Permission from '@/core/components/Permission'
import LoadingButton from '@mui/lab/LoadingButton'
import { useMenu } from '@/components/menu/contexts/menuContext'
import { toast } from 'react-toastify'
import DialogAddLinesTransaction, { LineType } from '../../create/components/dialog-transaction-lines'
import { useUpdateGeneralLedger } from '@/api/services/account/mutation'

const DetailListLedgerCard = () => {
  const { setConfirmState } = useMenu()
  const {
    generalLedgerData,
    fetchGeneralLedgerData,
    fetchGeneralLedgerList,
    fetchGeneralLedgerLogs,
    isEditable,
    setIsEditable
  } = useGeneralLedger()
  const [dialogTransaction, setDialogTransaction] = useState<{ state: boolean; isEdit?: boolean }>({ state: false })
  const [selectedLine, setSelectedLine] = useState<{ line?: GeneralLedgerLineTypePayload; index?: number }>()

  const { mutate: updateGeneralLedger, isLoading: loadingMutate } = useUpdateGeneralLedger()
  const { control, reset, setValue, getValues } = useFormContext<GeneralLedgerPayload>()

  const lines = useWatch({ control, name: 'lines', defaultValue: [] })

  const totalDebit = lines?.filter(line => line.type === 'DEBIT')?.reduce((acc, line) => acc + line.amount, 0) ?? 0
  const totalCredit = lines?.filter(line => line.type === 'CREDIT')?.reduce((acc, line) => acc + line.amount, 0) ?? 0

  const tableOptions: any = useMemo(
    () => ({
      data: lines.filter(line => line.amount !== 0) ?? [],
      columns: tableColumns({
        journal: generalLedgerData,
        isEditable,
        control,
        delete: (row, index) => {
          if (isNullOrUndefined(row.id)) {
            setValue(
              'lines',
              lines.filter((_, idx) => idx !== index)
            )
          } else {
            setValue(
              'lines',
              lines.map(line => (line.id === row.id ? { ...line, amount: 0 } : line))
            )
          }
        },
        edit: (row, index) => {
          setDialogTransaction({ state: true, isEdit: true })
          setSelectedLine({ line: row, index })
        }
      }),
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [lines, isEditable, generalLedgerData]
  )

  const table = useReactTable(tableOptions)

  const handleAddLine = () => {
    setDialogTransaction({ state: true, isEdit: false })
    setSelectedLine({ line: null })
  }

  const handleSubmitLine = (line: LineType) => {
    if (!isNullOrUndefined(selectedLine.index)) {
      reset({
        ...getValues(),
        lines: lines.map(l =>
          l.id === line.id
            ? {
                department: line?.department,
                departmentId: line?.departmentId,
                accountId: line.accountId,
                amount: line.amount,
                description: line.description,
                id: line?.id,
                type: line.type,
                code: line.code,
                name: line.name
              }
            : l
        )
      })
    } else {
      reset({
        ...getValues(),
        lines: [
          ...lines,
          {
            department: line?.department,
            departmentId: line?.departmentId,
            accountId: line.accountId,
            amount: line.amount,
            description: line.description,
            type: line.type,
            code: line.code,
            name: line.name
          }
        ]
      })
    }
    setDialogTransaction({ state: false })
  }

  const toggleTransaction = () => {
    if (isEditable) {
      setConfirmState({
        open: true,
        title: 'Simpan Perubahan',
        content: 'Apakah kamu yakin akan menyimpan perubahan jurnal, action ini tidak dapat diubah',
        confirmText: 'Simpan',
        onConfirm: () => {
          updateGeneralLedger(
            {
              id: generalLedgerData?.id,
              ...getValues()
            },
            {
              onSuccess: () => {
                toast.success('Perubahan berhasil disimpan')
                fetchGeneralLedgerLogs()
                fetchGeneralLedgerData()
                fetchGeneralLedgerList()
                setIsEditable(false)
              }
            }
          )
        }
      })
      return
    }
    setIsEditable(prev => !prev)
  }

  useEffect(() => {
    if (generalLedgerData) {
      reset({
        type: generalLedgerData?.type as GeneralLedgerTypeEnum,
        description: generalLedgerData?.description,
        transactionDate: generalLedgerData?.transactionDate,
        departmentId: generalLedgerData?.department?.id,
        siteId: generalLedgerData?.siteId,
        projectLabelId: generalLedgerData?.projectLabelId,
        unitId: generalLedgerData?.unit?.id,
        lines: generalLedgerData?.lines?.map(line => ({
          accountId: line?.account?.id,
          amount: line.credit || line.debit,
          description: line?.description,
          departmentId: line?.department?.id,
          department: line?.department,
          id: line?.id,
          type: !!line?.credit ? 'CREDIT' : 'DEBIT',
          code: line?.account?.code,
          name: line?.account?.name
        }))
      })
    }
  }, [generalLedgerData])

  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-4'>
          <div className='flex justify-between items-center'>
            <Typography variant='h5'>Detil Transaksi</Typography>
            <Permission permission={['journal.update']}>
              <div className='flex gap-2'>
                {isEditable && (
                  <Button disabled={loadingMutate} size='small' variant='outlined' onClick={handleAddLine}>
                    Tambah Transaksi
                  </Button>
                )}
              </div>
            </Permission>
          </div>
          <div className='flex flex-col md:flex-row justify-between items-center gap-2'>
            <div className='rounded-[8px] bg-[#4C4E640D] is-full sm:is-auto p-4 py-3 flex flex-col flex-1'>
              <small>Total Debit</small>
              <Typography className='font-semibold'>
                {thousandSeparator(
                  totalDebit,
                  !!generalLedgerData?.currencyId,
                  generalLedgerData?.currency?.symbol ?? undefined,
                  2
                )}
              </Typography>
            </div>
            <div className='rounded-[8px] bg-[#4C4E640D] is-full sm:is-auto p-4 py-3 flex flex-col flex-1'>
              <small>Total Kredit</small>
              <Typography className='font-semibold'>
                {thousandSeparator(
                  totalCredit,
                  !!generalLedgerData?.currencyId,
                  generalLedgerData?.currency?.symbol ?? undefined,
                  2
                )}
              </Typography>
            </div>
          </div>
          <div className='shadow-md rounded-[8px]'>
            <Table
              headerColor='green'
              table={table}
              emptyLabel={
                <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
                  <Typography>Belum ada data transaksi</Typography>
                  <Typography className='text-sm text-gray-400'>
                    Tambahkan data transaksi dengan tombol di atas
                  </Typography>
                </td>
              }
            />
          </div>
        </CardContent>
      </Card>
      {dialogTransaction.state && (
        <DialogAddLinesTransaction
          line={selectedLine.line}
          onSubmitLine={handleSubmitLine}
          open={dialogTransaction.state}
          isEdit={dialogTransaction.isEdit}
          setOpen={open => setDialogTransaction({ state: open })}
        />
      )}
    </>
  )
}

export default DetailListLedgerCard
