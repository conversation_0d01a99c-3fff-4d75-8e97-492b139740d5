import { Avatar, Card, CardContent, Typography } from '@mui/material'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { useGeneralLedger } from '../../context/GeneralLedgerContext'

const CreatedByCard = () => {
  const { generalLedgerData } = useGeneralLedger()

  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Dibuat oleh</Typography>
        </div>
        <div className='flex gap-3 items-center w-full'>
          <Avatar src={generalLedgerData?.createdByUser?.profilePictureUrl} />
          <div className='flex flex-col md:flex-row gap-3 items-start md:items-center w-full'>
            <div className='flex flex-col flex-1 gap-0 items-start relative bg-transparent'>
              <div className='flex flex-col gap-0 items-start relative bg-transparent'>
                <p className='tracking-[0.2px] leading-6 text-base text-black dark:text-inherit'>
                  {generalLedgerData?.createdByUser?.fullName}
                </p>
              </div>
              <div className='flex flex-col gap-0 items-start relative bg-transparent'>
                <Typography variant='caption'>{generalLedgerData?.createdByUser?.title}</Typography>
              </div>
            </div>
            <Typography>
              {formatDate(generalLedgerData?.createdAt ?? Date.now(), 'eeee, dd/MM/yyyy, HH:mm', { locale: id })}
            </Typography>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default CreatedByCard
