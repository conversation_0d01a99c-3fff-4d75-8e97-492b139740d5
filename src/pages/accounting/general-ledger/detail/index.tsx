import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Button, Grid, Typography } from '@mui/material'
import { Link } from 'react-router-dom'
import DetailLedgerCard from './components/DetailLedgerCard'
import DetailListLedgerCard from './components/DetailListLedgerCard'
import { useFormContext } from 'react-hook-form'
import { GeneralLedgerPayload, GeneralLedgerTypeEnum } from '@/types/payload'
import { useEffect } from 'react'
import { useCreateGeneralLedger, useUpdateGeneralLedger } from '@/api/services/account/mutation'
import { toast } from 'react-toastify'
import { useGeneralLedger } from '../context/GeneralLedgerContext'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import CreatedByCard from './components/CreatedByCard'
import ActivityLogCard from './components/ActivityLogCard'
import Permission from '@/core/components/Permission'
import { useMenu } from '@/components/menu/contexts/menuContext'
import LoadingButton from '@mui/lab/LoadingButton'

const GeneralLedgerCreatePage = () => {
  const {
    router,
    fetchGeneralLedgerList,
    generalLedgerData,
    generalLedgerLogs,
    setIsEditable,
    isEditable,
    fetchGeneralLedgerData,
    fetchGeneralLedgerLogs
  } = useGeneralLedger()
  const { setConfirmState } = useMenu()
  const { reset, getValues, handleSubmit } = useFormContext<GeneralLedgerPayload>()

  const { mutate: createMutate, isLoading: loadingMutate } = useCreateGeneralLedger()
  const { mutate: updateGeneralLedger, isLoading: loadingUpdateMutate } = useUpdateGeneralLedger()

  const isLoading = loadingMutate || loadingUpdateMutate

  const toggleJournal = () => {
    if (isEditable) {
      setConfirmState({
        open: true,
        title: 'Simpan Perubahan',
        content: 'Apakah kamu yakin akan menyimpan perubahan jurnal, action ini tidak dapat diubah',
        confirmText: 'Simpan',
        onConfirm: () => {
          updateGeneralLedger(
            {
              id: generalLedgerData?.id,
              ...getValues()
            },
            {
              onSuccess: () => {
                toast.success('Perubahan berhasil disimpan')
                fetchGeneralLedgerLogs()
                fetchGeneralLedgerData()
                fetchGeneralLedgerList()
                setIsEditable(false)
              }
            }
          )
        }
      })
      return
    }
    setIsEditable(prev => !prev)
  }

  useEffect(() => {
    reset({
      ...getValues(),
      lines: []
    })
  }, [])

  const onSubmitGeneralLedger = async (data: GeneralLedgerPayload) => {
    createMutate(data, {
      onSuccess: () => {
        toast.success('Data pencatatan berhasil ditambahkan')
        fetchGeneralLedgerList()
        router.push('/accounting/general-ledger')
      }
    })
  }

  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Breadcrumbs>
          <Link to='#' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Buku Besar</Typography>
          </Link>
          <Link to='/accounting/general-ledger/list' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Jurnal Umum</Typography>
          </Link>
          <Typography>Detil Jurnal</Typography>
        </Breadcrumbs>
      </Grid>
      <Grid item xs={12}>
        <div className='flex justify-between items-end flex-col sm:flex-row max-sm:items-center gap-2'>
          <div className='flex flex-col max-sm:text-center'>
            <Typography variant='h4'>No. Pencatatan: {generalLedgerData?.number ?? '-'}</Typography>
            <Typography>
              {generalLedgerData?.createdAt
                ? formatDate(generalLedgerData.createdAt, 'eeee, dd MMMM yyyy', { locale: id })
                : '-'}
            </Typography>
          </div>
          <div className='flex gap-2 is-full sm:is-auto'>
            {isEditable && (
              <Button
                className='is-full sm:is-auto'
                onClick={() => setIsEditable(false)}
                variant='outlined'
                color='error'
              >
                Batal
              </Button>
            )}
            <Permission permission={['journal.update']}>
              {generalLedgerData?.type === GeneralLedgerTypeEnum.GENERAL && (
                <LoadingButton
                  className='is-full sm:is-auto'
                  loading={isLoading}
                  variant={isEditable ? 'contained' : 'outlined'}
                  onClick={toggleJournal}
                >
                  {!isEditable ? 'Edit Jurnal' : 'Simpan'}
                </LoadingButton>
              )}
            </Permission>
          </div>
        </div>
      </Grid>
      <Grid item xs={12}>
        <DetailListLedgerCard />
      </Grid>
      <Grid item xs={12} md={6}>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <DetailLedgerCard />
          </Grid>
          {generalLedgerData?.createdBy && (
            <Grid item xs={12}>
              <CreatedByCard />
            </Grid>
          )}
        </Grid>
      </Grid>
      <Grid item xs={12} md={6}>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <ActivityLogCard logList={generalLedgerLogs?.items ?? []} />
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  )
}

export default GeneralLedgerCreatePage
