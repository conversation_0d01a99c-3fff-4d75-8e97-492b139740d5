import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Button, Grid, Typo<PERSON> } from '@mui/material'
import { Link, useSearchParams } from 'react-router-dom'
import DetailLedgerCard from './components/DetailLedgerCard'
import DetailListLedgerCard from './components/DetailListLedgerCard'
import { useFormContext, useWatch } from 'react-hook-form'
import { GeneralLedgerPayload, GeneralLedgerTypeEnum } from '@/types/payload'
import { useEffect } from 'react'
import { useCreateGeneralLedger } from '@/api/services/account/mutation'
import LoadingButton from '@mui/lab/LoadingButton'
import { toast } from 'react-toastify'
import { useGeneralLedger } from '../context/GeneralLedgerContext'
import { useMenu } from '@/components/menu/contexts/menuContext'
import { useDraft } from '@/pages/draft/context/DraftContext'
import DraftQueryMethods from '@/api/services/draft/query'
import { DRAFT_QUERY_KEY } from '@/api/services/draft/service'
import { DraftScope } from '@/types/draftsTypes'
import { useQuery } from '@tanstack/react-query'

const GeneralLedgerCreatePage = () => {
  const [searchParams] = useSearchParams()
  const { setConfirmState } = useMenu()
  const { router, fetchGeneralLedgerList } = useGeneralLedger()
  const { reset, getValues, handleSubmit, control } = useFormContext<GeneralLedgerPayload>()

  const linesWatch = useWatch({ control, name: 'lines', defaultValue: [] })

  const { mutate: createMutate, isLoading: loadingMutate } = useCreateGeneralLedger()
  const { createDraft, updateDraft, deleteDraft, loadingDraft } = useDraft()

  const { data: draftData } = useQuery({
    enabled: !!searchParams.get('draft'),
    queryKey: [DRAFT_QUERY_KEY, searchParams.get('draft')],
    queryFn: () => DraftQueryMethods.getOneDraft(searchParams.get('draft')),
    cacheTime: 0
  })

  useEffect(() => {
    if (draftData) {
      try {
        const parsedPayload = JSON.parse(draftData.payload)
        reset({
          type: GeneralLedgerTypeEnum.GENERAL,
          ...parsedPayload
        })
      } catch (error) {
        console.error('Error parsing draft data:', error)
        toast.error('Draft data tidak valid, menggunakan data default')
        reset({
          description: '',
          type: GeneralLedgerTypeEnum.GENERAL,
          transactionDate: null,
          lines: []
        })
      }
    } else {
      reset({
        ...getValues(),
        description: '',
        type: GeneralLedgerTypeEnum.GENERAL,
        transactionDate: null,
        lines: []
      })
    }
  }, [draftData, reset])

  const totalDebit = linesWatch.filter(line => line.type === 'DEBIT').reduce((acc, line) => acc + line.amount, 0)
  const totalCredit = linesWatch.filter(line => line.type === 'CREDIT').reduce((acc, line) => acc + line.amount, 0)

  const onSubmitGeneralLedger = async (data: GeneralLedgerPayload) => {
    if (totalDebit !== totalCredit) {
      toast.error('Total Debit dan Total Kredit tidak sama')
      return
    }
    setConfirmState({
      open: true,
      title: 'Buat Pencatatan',
      content:
        'Apakah kamu yakin akan membuat pencatatan ini? Pastikan semua detil yang kamu masukkan untuk pencatatan ini sudah benar',
      confirmText: 'Buat Pencatatan',
      onConfirm: () => {
        createMutate(
          { ...getValues() },
          {
            onSuccess: () => {
              toast.success('Data pencatatan berhasil ditambahkan')
              fetchGeneralLedgerList()
              router.push('/accounting/general-ledger')
              if (draftData) deleteDraft(draftData.id)
            }
          }
        )
      }
    })
  }

  const onSaveDraft = () => {
    const formData = getValues()
    setConfirmState({
      open: true,
      title: 'Simpan Draft Jurnal Umum',
      content: 'Apakah kamu yakin akan menyimpan draft Jurnal Umum ini? Pastikan semua detil sudah benar',
      confirmText: 'Simpan',
      onConfirm: () => {
        try {
          const payload = JSON.stringify(formData)
          if (draftData) {
            updateDraft(
              { draftId: draftData.id, payload, siteId: formData.siteId ?? '' },
              {
                onSuccess: () => {
                  toast.success('Jurnal Umum disimpan sebagai draft.')
                  router.push('/accounting/general-ledger/draft')
                },
                onError: error => {
                  console.error('Error updating draft:', error)
                  toast.error('Gagal menyimpan draft. Silakan coba lagi.')
                }
              }
            )
          } else {
            createDraft(
              { scope: DraftScope['GENERAL-LEDGER'], payload, siteId: formData.siteId ?? undefined },
              {
                onSuccess: () => {
                  toast.success('Jurnal Umum disimpan sebagai draft.')
                  router.push('/accounting/general-ledger/draft')
                },
                onError: error => {
                  console.error('Error creating draft:', error)
                  toast.error('Gagal menyimpan draft. Silakan coba lagi.')
                }
              }
            )
          }
        } catch (error) {
          console.error('Error serializing form data:', error)
          toast.error('Gagal memproses data form. Silakan coba lagi.')
        }
      }
    })
  }

  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Breadcrumbs>
          <Link to='#' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Buku Besar</Typography>
          </Link>
          <Link to='/accounting/general-ledger' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Jurnal Umum</Typography>
          </Link>
          <Typography>Tambah Pencatatan</Typography>
        </Breadcrumbs>
      </Grid>
      <Grid item xs={12}>
        <div className='flex justify-between items-end flex-col sm:flex-row max-sm:items-center gap-2'>
          <div className='flex flex-col max-sm:text-center'>
            <Typography variant='h4'>Tambah Pencatatan Jurnal</Typography>
            <Typography>Lengkapi data pencatatan transaksi jurnal umum</Typography>
          </div>
          <div className='flex gap-2 flex-col sm:flex-row is-full sm:is-auto'>
            <Button disabled={loadingMutate} onClick={() => router.back()} variant='outlined' color='secondary'>
              Batalkan
            </Button>
            <Button
              variant='outlined'
              disabled={loadingMutate || loadingDraft}
              onClick={onSaveDraft}
              className='is-full sm:is-auto'
            >
              Simpan Draft
            </Button>
            <LoadingButton
              loading={loadingMutate}
              variant='contained'
              onClick={handleSubmit(onSubmitGeneralLedger, console.error)}
            >
              Tambah Pencatatan
            </LoadingButton>
          </div>
        </div>
      </Grid>
      <Grid item xs={12}>
        <DetailLedgerCard />
      </Grid>
      <Grid item xs={12}>
        <DetailListLedgerCard />
      </Grid>
    </Grid>
  )
}

export default GeneralLedgerCreatePage
