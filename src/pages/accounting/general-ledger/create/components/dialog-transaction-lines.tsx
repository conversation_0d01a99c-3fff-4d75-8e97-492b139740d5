import AccountsQueryMethods, { ACCOUNT_LIST_QUERY_KEY } from '@/api/services/account/query'
import { ThousandField } from '@/components/numeric/CurrencyField'
import { useAuth } from '@/contexts/AuthContext'
import { AccountType } from '@/types/accountTypes'
import { GeneralLedgerLineTypePayload } from '@/types/payload'
import { zodResolver } from '@hookform/resolvers/zod'
import LoadingButton from '@mui/lab/LoadingButton'
import {
  Dialog,
  Typography,
  DialogTitle,
  DialogContent,
  IconButton,
  Grid,
  Autocomplete,
  debounce,
  TextField,
  RadioGroup,
  FormControlLabel,
  Radio,
  DialogActions,
  Button,
  Select,
  InputLabel,
  MenuItem,
  FormControl,
  ListSubheader,
  ListItemText
} from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { useEffect, useState } from 'react'
import { Controller, useForm, useWatch } from 'react-hook-form'
import z, { number, object, string } from 'zod'
import CompanyQueryMethods, { PROJECT_LABEL_LIST_QUERY_KEY } from '@/api/services/company/query'
import { defaultListData } from '@/api/queryClient'
import { ListResponse } from '@/types/api'
import { UnitType } from '@/types/companyTypes'
import { ProjectLabelType } from '@/types/projectTypes'
import UnitAutocomplete from '@/components/UnitAutocomplete'

type Props = {
  open: boolean
  isEdit?: boolean
  fromCreate?: boolean
  line?: GeneralLedgerLineTypePayload
  setOpen: (open: boolean) => void
  onSubmitLine: (line: LineType) => void
}

const departmentSchema = object({
  id: string().uuid(),
  name: string().optional().nullable(),
  code: string().optional().nullable()
})

const lineSchema = object({
  accountId: string().uuid(),
  department: departmentSchema.optional().nullable(),
  departmentId: string().uuid().optional().nullable(),
  name: string().optional().nullable(),
  code: string().optional().nullable(),
  id: number().optional().nullable(),
  amount: number(),
  type: z.enum(['CREDIT', 'DEBIT']),
  description: string().optional().nullable(),
  currencyId: string().optional().nullable(),
  exchangeRate: number().optional().nullable(),
  siteId: string().optional().nullable(),
  projectLabelId: number().optional().nullable(),
  unitId: string().optional().nullable(),
  unit: object({
    id: string().uuid().optional().nullable(),
    number: string().optional().nullable(),
    brandName: string().optional().nullable(),
    type: string().optional().nullable()
  })
    .optional()
    .nullable()
})

export type LineType = z.infer<typeof lineSchema>

const DialogAddLinesTransaction = (props: Props) => {
  const { open, setOpen, line, isEdit, fromCreate } = props
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedAccount, setSelectedAccount] = useState<AccountType>()
  const { departmentList, groupedSiteList, currenciesList } = useAuth()
  const [selectedUnit, setSelectedUnit] = useState<UnitType | null>(line?.unit)

  const { control, reset, setValue, getValues, handleSubmit } = useForm<LineType>({
    resolver: zodResolver(lineSchema)
  })

  const siteIdWatch = useWatch({
    control,
    name: 'siteId',
    defaultValue: (line as any)?.siteId
  })

  const unitIdWatch = useWatch({
    control,
    name: 'unitId',
    defaultValue: (line as any)?.unitId
  })

  const currencyIdWatch = useWatch({
    control,
    name: 'currencyId',
    defaultValue: (line as any)?.currencyId
  })

  const currentCurrency = currenciesList?.find(currency => currency.id === currencyIdWatch)

  const {
    data: { items: projectLabels }
  } = useQuery({
    queryKey: [PROJECT_LABEL_LIST_QUERY_KEY, siteIdWatch],
    enabled: !!siteIdWatch,
    queryFn: async () => {
      return CompanyQueryMethods.getProjectLabelList({ limit: 100, siteId: siteIdWatch })
    },
    placeholderData: defaultListData as ListResponse<ProjectLabelType>
  })

  useEffect(() => {
    if (control._getWatch('unit')) {
      setSelectedUnit(control._getWatch('unit') as UnitType)
    } else {
      setSelectedUnit(null)
    }
  }, [control._getWatch('unit')])

  useEffect(() => {
    if (currenciesList?.length > 0 && !(line as any)?.currencyId) {
      reset({
        ...getValues(),
        currencyId: currenciesList?.find(currency => currency.isDefault)?.id,
        exchangeRate: 1
      })
    }
  }, [currenciesList, line])

  const handleClose = () => {
    props.setOpen(false)
  }

  const { data: accountsList, remove: removeAccountlist } = useQuery({
    // enabled: !!searchQuery,
    queryKey: [ACCOUNT_LIST_QUERY_KEY, searchQuery, line?.name],
    queryFn: () =>
      AccountsQueryMethods.getAccountList({
        limit: Number.MAX_SAFE_INTEGER,
        level: 1,
        search: line?.name || searchQuery
      })
  })

  useEffect(() => {
    if (!!line) {
      reset({
        ...getValues(),
        accountId: line?.accountId,
        type: line?.type,
        amount: line?.amount,
        description: line?.description,
        department: line?.department || departmentList?.find(department => department.id === line?.departmentId),
        departmentId: line?.departmentId,
        currencyId: (line as any)?.currencyId,
        exchangeRate: (line as any)?.exchangeRate,
        siteId: (line as any)?.siteId,
        projectLabelId: (line as any)?.projectLabelId,
        unitId: (line as any)?.unitId,
        unit: (line as any)?.unit,
        code: line?.code,
        name: line?.name,
        id: line?.id
      })
    }
  }, [line, departmentList])

  return (
    <Dialog fullWidth maxWidth='sm' scroll='body' open={open} onClose={handleClose}>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-16'>
        {!!isEdit ? 'Edit' : 'Tambah'} Transaksi
        <Typography component='span' className='flex flex-col text-center'>
          Lengkapi detil transaksi
        </Typography>
      </DialogTitle>
      <DialogContent className='overflow-visible pbs-0 sm:pbe-6 sm:px-16'>
        <IconButton onClick={handleClose} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='accountId'
              render={({ field: { onChange, value } }) => {
                const sAccount = accountsList?.items?.find(account => account.id === value)
                return (
                  <Autocomplete
                    key={JSON.stringify({ value, sAccount })}
                    value={sAccount ?? selectedAccount}
                    onInputChange={debounce((e, newValue, reason) => {
                      if (reason === 'input') {
                        setSearchQuery(newValue)
                      }
                    }, 700)}
                    disabled={isEdit && !fromCreate}
                    options={accountsList?.items ?? []}
                    getOptionLabel={(option: AccountType) => `[${option.code}] ${option.name}`}
                    freeSolo={!searchQuery}
                    noOptionsText='Akun tidak ditemukan'
                    onChange={(e, newValue: AccountType) => {
                      if (newValue) {
                        onChange(newValue.id)
                        reset({
                          ...getValues(),
                          name: newValue.name,
                          code: newValue.code
                        })
                        setSelectedAccount(newValue)
                        removeAccountlist()
                      }
                    }}
                    renderInput={params => (
                      <TextField
                        {...params}
                        InputProps={{
                          ...params.InputProps,
                          onKeyDown: e => {
                            if (e.key === 'Enter') {
                              e.stopPropagation()
                            }
                          }
                        }}
                        placeholder='Cari akun perkiraan'
                        label='Akun Perkiraan'
                      />
                    )}
                  />
                )
              }}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='type'
              render={({ field: { onChange, value } }) => (
                <RadioGroup value={value} onChange={(_, val) => onChange(val)} className='flex flex-row gap-2'>
                  <FormControlLabel value='DEBIT' control={<Radio />} label={<Typography>Debit</Typography>} />
                  <FormControlLabel value='CREDIT' control={<Radio />} label={<Typography>Kredit</Typography>} />
                </RadioGroup>
              )}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <Controller
              control={control}
              name='currencyId'
              render={({ field: { value, onChange }, fieldState: { error } }) => (
                <FormControl fullWidth error={!!error}>
                  <InputLabel id='role-select'>Mata Uang (Opsional)</InputLabel>
                  <Select
                    key={value}
                    fullWidth
                    id='select-currencyId'
                    value={value || ''}
                    onChange={e => {
                      onChange((e.target as HTMLInputElement).value)
                    }}
                    label='Mata Uang (Opsional)'
                    labelId='select-currencyId'
                    inputProps={{ placeholder: 'Pilih Kode Mata Uang' }}
                  >
                    {currenciesList?.map(currency => (
                      <MenuItem key={currency.id} value={currency.id}>
                        {currency.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              )}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <Controller
              control={control}
              name='exchangeRate'
              render={({ field: { value, onChange }, fieldState: { error } }) => (
                <TextField
                  fullWidth
                  label='Nilai Tukar (Opsional)'
                  placeholder='Nilai Tukar'
                  value={value}
                  disabled={!currencyIdWatch || currentCurrency?.isDefault}
                  onChange={e => onChange(e.target.value)}
                  error={!!error}
                  InputProps={{ inputComponent: ThousandField as any }}
                  InputLabelProps={{ shrink: !!value }}
                />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='amount'
              render={({ field: { onChange, value } }) => (
                <TextField
                  fullWidth
                  value={value}
                  onChange={onChange}
                  InputProps={{ inputComponent: ThousandField as any }}
                  label='Nominal'
                />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              name='siteId'
              control={control}
              render={({ field: { value, onChange }, fieldState: { error } }) => (
                <FormControl fullWidth error={!!error}>
                  <InputLabel id='role-select'>Lokasi (Opsional)</InputLabel>
                  <Select
                    key={value}
                    fullWidth
                    id='select-siteId'
                    value={value || ''}
                    onChange={e => onChange((e.target as HTMLInputElement).value)}
                    label='Lokasi (Opsional)'
                    size='medium'
                    labelId='siteId-select'
                    inputProps={{ placeholder: 'Pilih Lokasi' }}
                    defaultValue=''
                  >
                    {groupedSiteList.map(group => {
                      let children = []
                      children.push(
                        <ListSubheader
                          className='bg-green-50 text-primary font-semibold'
                          key={group.projectId ?? 'no_project'}
                        >
                          {group.project?.name || 'Tanpa Proyek'}
                        </ListSubheader>
                      )
                      group.sites.forEach(site => {
                        children.push(
                          <MenuItem key={site.id} value={site.id}>
                            <ListItemText primary={site.name} />
                          </MenuItem>
                        )
                      })
                      return children
                    })}
                  </Select>
                  {/* {error && <FormHelperText>{error.message}</FormHelperText>} */}
                </FormControl>
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='departmentId'
              render={({ field: { onChange, value } }) => (
                <FormControl fullWidth>
                  <InputLabel id='department-label'>Departemen (Opsional)</InputLabel>
                  <Select
                    labelId='department-label'
                    id='department-select'
                    value={value}
                    onChange={e => {
                      onChange(e.target.value)
                      setValue(
                        'department',
                        departmentList.find(department => department.id === e.target.value)
                      )
                    }}
                    label='Departemen (Opsional)'
                  >
                    {departmentList?.map(department => (
                      <MenuItem key={department.id} value={department.id}>
                        {department.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='projectLabelId'
              render={({ field: { value, onChange }, fieldState: { error } }) => (
                <FormControl fullWidth error={!!error}>
                  <InputLabel id='select-projectLabelId'>Label Proyek (Opsional)</InputLabel>
                  <Select
                    key={value}
                    fullWidth
                    id='select-projectLabelId'
                    value={value || ''}
                    disabled={!siteIdWatch}
                    onChange={e => onChange((e.target as HTMLInputElement).value)}
                    label='Label Proyek (Opsional)'
                    labelId='select-projectLabelId'
                    inputProps={{ placeholder: 'Pilih Label' }}
                  >
                    {projectLabels?.map(label => (
                      <MenuItem key={label.id} value={label.id}>
                        {label.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='unitId'
              rules={{
                validate: value => {
                  return true
                }
              }}
              render={({ fieldState: { error } }) => (
                <UnitAutocomplete
                  value={selectedUnit}
                  label='Kode Unit (Opsional)'
                  onChange={unit => {
                    if (unit) {
                      setValue('unitId', unit.id)
                      setValue('unit', unit)
                      setSelectedUnit(unit)
                    } else {
                      setValue('unitId', undefined)
                      setValue('unit', null)
                      setSelectedUnit(null)
                    }
                  }}
                  error={!!error}
                  // helperText={error?.message}
                />
              )}
            />
          </Grid>
          {!!unitIdWatch && (
            <>
              <Grid item xs={6}>
                <TextField
                  fullWidth
                  label='Merk Unit'
                  variant='outlined'
                  value={selectedUnit?.brandName ?? ''}
                  disabled
                  InputLabelProps={{ shrink: !!selectedUnit?.brandName }}
                />
              </Grid>
              <Grid item xs={6}>
                <TextField
                  fullWidth
                  label='Tipe Unit'
                  variant='outlined'
                  value={selectedUnit?.type ?? ''}
                  disabled
                  InputLabelProps={{ shrink: !!selectedUnit?.type }}
                />
              </Grid>
            </>
          )}
          <Grid item xs={12}>
            <Controller
              control={control}
              name='description'
              render={({ field: { onChange, value } }) => (
                <TextField
                  value={value}
                  onChange={onChange}
                  fullWidth
                  label='Catatan (opsional)'
                  placeholder='Masukkkan catatan'
                  multiline
                  rows={4}
                />
              )}
            />
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions className='justify-center pbs-0 sm:pbe-16 sm:px-16'>
        <Button variant='outlined' onClick={handleClose}>
          Batalkan
        </Button>
        <LoadingButton
          variant='contained'
          color='primary'
          onClick={handleSubmit(props.onSubmitLine, e => console.error({ e }))}
        >
          {!!isEdit ? 'Ubah' : 'Tambahkan'}
        </LoadingButton>
      </DialogActions>
    </Dialog>
  )
}

export default DialogAddLinesTransaction
