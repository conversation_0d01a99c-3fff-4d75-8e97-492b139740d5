import { formatThousandSeparator, isNullOrUndefined, mergeArrays, thousandSeparator } from '@/utils/helper'
import { But<PERSON>, Card, CardContent, FormHelperText, Typography } from '@mui/material'
import {
  getFacetedRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  getFacetedUniqueValues,
  useReactTable,
  getCoreRowModel,
  getFacetedMinMaxValues
} from '@tanstack/react-table'
import { tableColumns } from '../config/table'
import { useEffect, useMemo, useState } from 'react'
import Table from '@/components/table'
import DialogAddLinesTransaction, { LineType } from './dialog-transaction-lines'
import { Controller, useFormContext, useWatch } from 'react-hook-form'
import { GeneralLedgerLineTypePayload, GeneralLedgerPayload } from '@/types/payload'

const DetailListLedgerCard = () => {
  const [dialogTransaction, setDialogTransaction] = useState<{ state: boolean; isEdit?: boolean }>({ state: false })
  const [selectedLine, setSelectedLine] = useState<{ line?: GeneralLedgerLineTypePayload; index?: number }>()
  const { control, reset, getValues } = useFormContext<GeneralLedgerPayload>()

  const linesWatch = useWatch({ control, name: 'lines', defaultValue: [] })

  const tableOptions: any = useMemo(
    () => ({
      data: linesWatch,
      columns: tableColumns({
        control,
        edit: (row, idx) => {
          setDialogTransaction({ state: true, isEdit: true })
          setSelectedLine({ line: row, index: idx })
        },
        delete: index => {
          const newLines = linesWatch.filter((_, i) => i !== index)
          reset({
            ...getValues(),
            lines: newLines
          })
        }
      }),
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [linesWatch, getValues()]
  )

  const table = useReactTable(tableOptions)

  const handleSubmitLine = (line: LineType) => {
    if (!isNullOrUndefined(selectedLine.index)) {
      reset({
        ...getValues(),
        lines: [
          ...linesWatch.slice(0, selectedLine.index),
          {
            accountId: line.accountId,
            amount: line.amount,
            department: line.department,
            departmentId: line.departmentId,
            description: line.description,
            currencyId: line.currencyId,
            exchangeRate: line.exchangeRate,
            siteId: line.siteId,
            projectLabelId: line.projectLabelId,
            unitId: line.unitId,
            unit: line.unit,
            type: line.type,
            code: line.code,
            name: line.name
          },
          ...linesWatch.slice(selectedLine.index + 1)
        ]
      })
    } else {
      reset({
        ...getValues(),
        lines: [
          ...linesWatch,
          {
            accountId: line.accountId,
            amount: line.amount,
            department: line.department,
            departmentId: line.departmentId,
            description: line.description,
            currencyId: line.currencyId,
            exchangeRate: line.exchangeRate,
            siteId: line.siteId,
            projectLabelId: line.projectLabelId,
            unitId: line.unitId,
            unit: line.unit,
            type: line.type,
            code: line.code,
            name: line.name
          }
        ]
      })
    }
    setDialogTransaction({ state: false })
  }

  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-4'>
          <div className='flex justify-between items-center'>
            <Typography variant='h5'>Detil Transaksi</Typography>
            <Button
              size='small'
              onClick={() => {
                setDialogTransaction({ state: true, isEdit: false })
                setSelectedLine({ line: null })
              }}
              variant='outlined'
            >
              Tambah Transaksi
            </Button>
          </div>
          <div className='flex flex-col md:flex-row justify-between items-center gap-2'>
            <div className='rounded-[8px] is-full sm:is-auto bg-[#4C4E640D] p-4 py-3 flex flex-col flex-1'>
              <small>Total Debit</small>
              <Typography className='font-semibold'>
                {formatThousandSeparator(
                  linesWatch
                    ?.filter(line => line.type === 'DEBIT')
                    ?.reduce((sum, line) => sum + (Number(line.amount) || 0), 0)
                )}
              </Typography>
            </div>
            <div className='rounded-[8px] is-full sm:is-auto bg-[#4C4E640D] p-4 py-3 flex flex-col flex-1'>
              <small>Total Kredit</small>
              <Typography className='font-semibold'>
                {formatThousandSeparator(
                  linesWatch
                    ?.filter(line => line.type === 'CREDIT')
                    ?.reduce((sum, line) => sum + (Number(line.amount) || 0), 0)
                )}
              </Typography>
            </div>
          </div>
          <Controller
            control={control}
            name='lines'
            render={({ fieldState: { error } }) => (
              <>
                <div className='rounded-[8px] shadow-md'>
                  <Table
                    headerColor='green'
                    table={table}
                    emptyLabel={
                      <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
                        <Typography>Belum ada data transaksi</Typography>
                        <Typography className='text-sm text-gray-400'>
                          Tambahkan data transaksi dengan tombol di atas
                        </Typography>
                      </td>
                    }
                  />
                </div>
                {!!error && <FormHelperText error>Wajib diisi</FormHelperText>}
              </>
            )}
          />
        </CardContent>
      </Card>
      {dialogTransaction.state && (
        <DialogAddLinesTransaction
          fromCreate
          line={selectedLine.line}
          onSubmitLine={handleSubmitLine}
          open={dialogTransaction.state}
          isEdit={dialogTransaction.isEdit}
          setOpen={open => setDialogTransaction({ state: open })}
        />
      )}
    </>
  )
}

export default DetailListLedgerCard
