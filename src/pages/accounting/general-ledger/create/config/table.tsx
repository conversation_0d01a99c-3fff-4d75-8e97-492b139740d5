import { <PERSON><PERSON><PERSON> } from '@/components/numeric/CurrencyField'
import { GeneralLedgerLineTypePayload, GeneralLedgerPayload } from '@/types/payload'
import { formatThousandSeparator, parseLocalizedNumber, thousandSeparator } from '@/utils/helper'
import { IconButton, TextField, Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'
import { Control, Controller } from 'react-hook-form'

type GeneralLedgerTypeWithAction = GeneralLedgerLineTypePayload & {
  code: string
  name: string
}

const columnHelper = createColumnHelper<GeneralLedgerTypeWithAction>()

type RowActionType = {
  delete: (index: number) => void
  edit: (row: GeneralLedgerLineTypePayload, index: number) => void
  control: Control<GeneralLedgerPayload>
}

export const tableColumns = ({ control, ...rowAction }: RowActionType) => [
  columnHelper.accessor('code', {
    header: '<PERSON><PERSON>kun'
  }),
  columnHelper.accessor('name', {
    header: '<PERSON><PERSON>kun'
  }),
  columnHelper.accessor('type', {
    header: 'Debit',
    cell: ({ row }) =>
      row.original.type === 'DEBIT' ? (
        <Typography sx={{ mr: 3.5 }} textAlign={'right'}>
          {formatThousandSeparator(row.original.amount)}
        </Typography>
      ) : (
        <Typography sx={{ mr: 3.5 }} textAlign={'right'}>
          {formatThousandSeparator(0)}
        </Typography>
      )
  }),
  columnHelper.display({
    id: 'credit',
    header: 'Kredit',
    cell: ({ row }) =>
      row.original.type === 'CREDIT' ? (
        <Typography sx={{ mr: 3.5 }} textAlign={'right'}>
          {formatThousandSeparator(row.original.amount)}
        </Typography>
      ) : (
        <Typography sx={{ mr: 3.5 }} textAlign={'right'}>
          {formatThousandSeparator(0)}
        </Typography>
      )
  }),
  columnHelper.accessor('description', {
    header: 'Keterangan',
    cell: ({ row }) => row.original.description
  }),
  columnHelper.accessor('department', {
    header: 'Departemen',
    cell: ({ row }) => row.original.department?.name ?? '-'
  }),
  columnHelper.display({
    id: 'actions',
    header: 'Action',
    cell: ({ row }) => {
      return (
        <>
          <IconButton onClick={() => rowAction.edit(row.original, row.index)}>
            <i className='ri-pencil-line' />
          </IconButton>
          <IconButton onClick={() => rowAction.delete(row.index)}>
            <i className='ri-delete-bin-7-line' />
          </IconButton>
        </>
      )
    }
  })
]
