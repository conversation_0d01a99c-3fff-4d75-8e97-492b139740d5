import { GeneralLedgerTypeEnum } from '@/types/payload'

export const getGeneralLedgerType = (type: string) => {
  switch (type) {
    case GeneralLedgerTypeEnum.GENERAL:
      return 'Jurnal Umum'
      break
    case GeneralLedgerTypeEnum.SALES_TRANSACTION:
      return 'Transaksi Penjualan'
      break
    case GeneralLedgerTypeEnum.PURCHASE_TRANSACTION:
      return 'Transaksi Pembelian'
      break
    case GeneralLedgerTypeEnum.CASH_RECEIPT:
      return 'Transaksi Penerimaan'
      break
    case GeneralLedgerTypeEnum.CASH_PAYMENT:
      return 'Pembayaran Tunai'
      break
    case GeneralLedgerTypeEnum.ADJUSTING_ENTRIES:
      return 'Entri Penyesuaian'
      break
    case GeneralLedgerTypeEnum.OTHER_TRANSACTION:
      return 'Transaksi Lain'
      break
    default:
      return 'Transaksi Lain'
  }
}

export const generalLedgerOptions = [
  {
    value: GeneralLedgerTypeEnum.GENERAL,
    label: 'Jurnal Umum'
  },
  {
    value: GeneralLedgerTypeEnum.SALES_TRANSACTION,
    label: 'Transaksi Penjualan'
  },
  {
    value: GeneralLedgerTypeEnum.PURCHASE_TRANSACTION,
    label: 'Transaksi Pembelian'
  },
  {
    value: GeneralLedgerTypeEnum.CASH_RECEIPT,
    label: 'Transaksi Penerimaan'
  },
  {
    value: GeneralLedgerTypeEnum.CASH_PAYMENT,
    label: 'Transaksi Cetak'
  },
  {
    value: GeneralLedgerTypeEnum.ADJUSTING_ENTRIES,
    label: 'Entri Penyesuaian'
  },
  {
    value: GeneralLedgerTypeEnum.OTHER_TRANSACTION,
    label: 'Transaksi Lain'
  }
]
