import { GeneralLedgerType } from '@/types/accountTypes'
import { IconButton, Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'
import { formatDate } from 'date-fns'
import truncateString from '@/core/utils/truncate'
import { thousandSeparator } from '@/utils/helper'

const columnHelper = createColumnHelper<GeneralLedgerType>()

type RowActionType = {
  detail: (row: GeneralLedgerType) => void
  withoutActions?: boolean
}

export const tableColumns = ({ detail, withoutActions }: RowActionType) => [
  columnHelper.accessor('number', {
    header: 'No. Pencatatan',
    cell: ({ row }) => (
      <Typography onClick={() => detail(row.original)} color='primary' sx={{ cursor: 'pointer' }}>
        {row.original.number}
      </Typography>
    )
  }),
  columnHelper.accessor('description', {
    header: 'Keterangan',
    cell: ({ row }) => (!!row.original?.description ? truncateString(row.original.description, 20) : '-')
  }),
  columnHelper.accessor('totalDebit', {
    header: '<PERSON><PERSON>aksi',
    cell: ({ row }) =>
      row.original?.totalDebit < 0
        ? `(${thousandSeparator(Math.abs(row.original.totalDebit), !!row.original?.currencyId, row.original?.currency?.symbol, 2)})`
        : thousandSeparator(row.original.totalDebit, !!row.original?.currencyId, row?.original?.currency?.symbol, 2)
  }),
  columnHelper.accessor('transactionDate', {
    header: 'Tgl Transaksi',
    cell: ({ row }) => formatDate(row.original.transactionDate, 'dd/MM/yyyy')
  }),
  columnHelper.accessor('createdAt', {
    header: 'Tgl Pencatatan',
    cell: ({ row }) => formatDate(row.original.createdAt, 'dd/MM/yyyy')
  }),
  ...(!withoutActions
    ? [
        columnHelper.display({
          id: 'action',
          header: 'Action',
          cell: ({ row }) => {
            return (
              <IconButton onClick={() => detail(row.original)}>
                <i className='ri-eye-line text-textSecondary' />
              </IconButton>
            )
          }
        })
      ]
    : [])
]
