export const periodMonthOptions = [
  { value: 1, label: '<PERSON><PERSON><PERSON>' },
  { value: 2, label: '<PERSON><PERSON><PERSON>' },
  { value: 3, label: '<PERSON><PERSON>' },
  { value: 4, label: 'April' },
  { value: 5, label: '<PERSON>' },
  { value: 6, label: 'Jun<PERSON>' },
  { value: 7, label: 'Jul<PERSON>' },
  { value: 8, label: '<PERSON>gus<PERSON>' },
  { value: 9, label: 'September' },
  { value: 10, label: 'Oktober' },
  { value: 11, label: 'November' },
  { value: 12, label: 'Desember' }
]

export const getStatusConfigChip = (status: string) => {
  switch (status) {
    case 'PENDING':
      return { label: '<PERSON>unggu', color: 'warning' }
    case 'APPROVED':
      return { label: 'Disetujui', color: 'success' }
    case 'REJECTED':
      return { label: 'Dito<PERSON>', color: 'error' }
    case 'CANCELED':
      return { label: 'Ditutup', color: 'info' }
    case 'CANCEL_REQUESTED':
      return { label: '<PERSON><PERSON><PERSON>', color: 'error' }
    case 'CLOSED':
      return { label: 'Ditutup', color: 'info' }
    case 'COMPLETED':
      return { label: '<PERSON><PERSON><PERSON>', color: 'success' }
    default:
      return { label: status, color: 'default' }
  }
}

const OBFUSCATION_SALT = 'acc-end-periods'

export function encryptorId(value: string | number): string {
  const str = String(value) + OBFUSCATION_SALT
  return btoa(unescape(encodeURIComponent(str)))
}

export function decryptorId(obfuscated: string): string | number {
  try {
    const decoded = decodeURIComponent(escape(atob(obfuscated)))
    if (decoded.endsWith(OBFUSCATION_SALT)) {
      const original = decoded.slice(0, -OBFUSCATION_SALT.length)
      // Try to convert back to number if possible
      if (!isNaN(Number(original)) && original.trim() !== '') {
        return Number(original)
      }
      return original
    }
    return ''
  } catch {
    return ''
  }
}
