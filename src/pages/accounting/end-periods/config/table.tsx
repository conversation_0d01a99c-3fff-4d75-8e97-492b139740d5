import { EndPeriodType } from '@/types/accountTypes'
import { toCurrency } from '@/utils/helper'
import { Chip, IconButton, Typography } from '@mui/material'
import { createColumn, createColumnHelper } from '@tanstack/react-table'
import { format } from 'date-fns'
import { getStatusConfigChip, periodMonthOptions } from './utils'

const columnHelper = createColumnHelper<EndPeriodType>()

type RowAction = {
  detail: (row: EndPeriodType) => void
}

export const tableColumns = (rowAction: RowAction) => [
  columnHelper.accessor('number', {
    header: 'No. Dokumen',
    cell: ({ row }) => (
      <span className='text-primary cursor-pointer' role='button' onClick={() => rowAction.detail(row.original)}>
        {row.original.number}
      </span>
    )
  }),
  columnHelper.accessor('status', {
    header: 'Status',
    cell: ({ row }) => (
      <Chip
        label={getStatusConfigChip(row.original?.status).label}
        color={getStatusConfigChip(row.original?.status).color as any}
        variant='tonal'
        size='small'
      />
    )
  }),
  columnHelper.accessor('year', {
    header: 'Periode',
    cell: ({ row }) =>
      periodMonthOptions.find(option => option.value === row.original?.month)?.label + ' ' + row.original?.year
  }),
  columnHelper.display({
    id: 'base-currency',
    header: 'Mata Uang Acuan',
    cell: () => <Typography>Rupiah (Rp)</Typography>
  }),
  // columnHelper.accessor('exchangeRates', {
  //   header: 'Konversi',
  //   cell: ({ row }) => (
  //     <div className='flex flex-col gap-1'>
  //       {row.original?.exchangeRates?.map((rate, index) => (
  //         <div key={index} className='flex gap-1 items-center'>
  //           <Typography>{rate?.currencyId}</Typography>
  //           <Typography>{rate?.exchangeRate}</Typography>
  //         </div>
  //       ))}
  //     </div>
  //   )
  // }),
  // columnHelper.display({
  //   id: 'total-amount',
  //   header: 'Total Transaksi',
  //   cell: ({ row }) => (
  //     <Typography color='primary' align='right'>
  //       {toCurrency(100000)}
  //     </Typography>
  //   )
  // }),
  columnHelper.accessor('createdAt', {
    header: 'Tgl Dibuat',
    cell: ({ row }) => (row.original?.createdAt ? format(row.original.createdAt, 'dd/MM/yyyy') : '-')
  }),
  columnHelper.display({
    id: 'actions',
    header: 'Action',
    cell: ({ row }) => {
      return (
        <IconButton onClick={() => rowAction.detail(row.original)}>
          <i className='ri-eye-line' />
        </IconButton>
      )
    }
  })
]
