import { useMenu } from '@/components/menu/contexts/menuContext'
import { CreateEndPeriodPayload } from '@/types/payload'
import LoadingButton from '@mui/lab/LoadingButton'
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography
} from '@mui/material'
import { Controller, useFieldArray, useForm, useWatch } from 'react-hook-form'
import { periodMonthOptions } from '../config/utils'
import AppReactDatepicker from '@/components/libs/styles/AppReactDatepicker'
import { useQuery } from '@tanstack/react-query'
import CompanyQueryMethods, { CURRENCIES_LIST_QUERY_KEY } from '@/api/services/company/query'
import { format, formatISO, toDate } from 'date-fns'
import { useEffect } from 'react'
import CurrencyField from '@/components/numeric/CurrencyField'
import { useEndPeriod } from '../context/EndPeriodContext'
import { useCreateEndPeriod } from '@/api/services/account/mutation'
import { toast } from 'react-toastify'
import NumberField from '@/components/numeric/NumberField'
import { useAuth } from '@/contexts/AuthContext'

type AddEndPeriodsDialogProps = {
  open: boolean
  setOpen: (open: boolean) => void
}

const AddEndPeriodsDialog = ({ open, setOpen }: AddEndPeriodsDialogProps) => {
  const { fetchEndPeriodList } = useEndPeriod()
  const { currenciesList } = useAuth()
  const { handleSubmit, control, setValue } = useForm<CreateEndPeriodPayload>()
  const { setConfirmState } = useMenu()

  const { mutate: createEndPeriodMutate, isLoading: createEndPeriodLoading } = useCreateEndPeriod()

  const { fields } = useFieldArray({ control, name: 'exchangeRates' })
  const baseCurrencyId = useWatch({
    control,
    name: 'baseCurrencyId'
  })

  const handleClose = () => {
    setOpen(false)
  }

  const handleSubmitItem = (data: CreateEndPeriodPayload) => {
    setConfirmState({
      open: true,
      title: 'Buat Proses End Period',
      content:
        'Apakah kamu yakin akan membuat proses end period untuk periode ini? Proses end period akan secara otomatis membuat item Jurnal Umum berisi penyesuaian nilai Aset dan perubahan lain yang terkait dengan kurs mata uang. Lanjutkan?',
      confirmText: 'Proses End Period',
      onConfirm: () => {
        const exchangeRates = [
          ...data.exchangeRates.map(rate => ({
            currencyId: rate.currencyId,
            value: rate.value
          })),
          { currencyId: data.baseCurrencyId, value: 1 }
        ]
        delete data.baseCurrencyId
        createEndPeriodMutate(
          {
            ...data,
            year: +format(data.year, 'yyyy'),
            exchangeRates: exchangeRates
          },
          {
            onSuccess: () => {
              toast.success('Proses end period berhasil dibuat')
              fetchEndPeriodList()
              handleClose()
            }
          }
        )
      }
    })
  }

  useEffect(() => {
    if (currenciesList) {
      const defaultCurrencyId = currenciesList?.find(curr => curr.isDefault)?.id
      setValue('baseCurrencyId', defaultCurrencyId)
      setValue(
        'exchangeRates',
        currenciesList
          ?.filter(curr => curr.id !== defaultCurrencyId)
          .map(curr => ({
            currencyId: curr.id,
            value: null,
            code: curr.code,
            name: curr.name
          }))
      )
    }
  }, [currenciesList])

  return (
    <Dialog fullWidth maxWidth='sm' scroll='body' open={open} onClose={handleClose}>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-16'>
        Buat Proses End Period
        <Typography component='span' className='flex flex-col text-center'>
          Tambahkan perubahan nilai mata uang di akhir periode
        </Typography>
      </DialogTitle>
      <form onSubmit={e => e.preventDefault()}>
        <DialogContent className='overflow-visible pbs-0 sm:pbe-6 sm:px-16'>
          <IconButton onClick={handleClose} className='absolute block-start-4 inline-end-4'>
            <i className='ri-close-line text-textSecondary' />
          </IconButton>
          <Grid container spacing={4}>
            <Grid item xs={12} md={6}>
              <Controller
                control={control}
                name='month'
                rules={{ required: true }}
                render={({ field, fieldState: { error } }) => (
                  <FormControl error={!!error} fullWidth>
                    <InputLabel id='period-month'>Bulan</InputLabel>
                    <Select
                      error={!!error}
                      {...field}
                      label='Bulan'
                      labelId='period-month'
                      id='period-month'
                      className='bg-white'
                    >
                      {periodMonthOptions.map(option => (
                        <MenuItem key={option.value} value={option.value}>
                          {option.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                )}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Controller
                control={control}
                name='year'
                rules={{ required: true }}
                render={({ field: { onChange, value }, fieldState: { error } }) => (
                  <AppReactDatepicker
                    boxProps={{ className: 'is-full' }}
                    selected={value ? toDate(value) : undefined}
                    onChange={(date: Date) => onChange(formatISO(date))}
                    dateFormat='YYYY'
                    maxDate={new Date()}
                    showYearPicker
                    customInput={<TextField fullWidth label='Tahun' placeholder='Pilih Tahun' error={!!error} />}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12}>
              <Typography className='font-medium'>Nilai Tukar</Typography>
            </Grid>
            <Grid item xs={12}>
              <Controller
                control={control}
                name='baseCurrencyId'
                render={({ field: { value } }) => (
                  <FormControl fullWidth>
                    <InputLabel id='base-currency'>Mata Uang Acuan</InputLabel>
                    <Select
                      key={value}
                      value={value}
                      label='Mata Uang Acuan'
                      labelId='base-currency'
                      id='base-currency'
                      className='bg-white'
                      readOnly
                    >
                      {currenciesList?.map(option => (
                        <MenuItem key={option.code} value={option.id}>
                          {option.name}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                )}
              />
            </Grid>
            {fields?.map((exchange, index) => (
              <>
                <Grid item xs={12} md={6}>
                  <Controller
                    control={control}
                    name={`exchangeRates.${index}.code`}
                    rules={{ required: true }}
                    render={({ field: { onChange, value }, fieldState: { error } }) => (
                      <TextField
                        fullWidth
                        value={`[${value}] ${fields?.[index]?.name}`}
                        label='Mata Uang'
                        error={!!error}
                      />
                    )}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <Controller
                    control={control}
                    name={`exchangeRates.${index}.value`}
                    rules={{ required: true }}
                    render={({ field: { onChange, value }, fieldState: { error } }) => (
                      <TextField
                        fullWidth
                        onChange={onChange}
                        value={value}
                        label='Nilai Tukar'
                        error={!!error}
                        InputProps={{
                          inputComponent: NumberField as any,
                          startAdornment: currenciesList?.find(curr => curr.id === baseCurrencyId)?.symbol ?? 'Rp',
                          inputProps: {
                            className: 'ml-1'
                          }
                        }}
                      />
                    )}
                  />
                </Grid>
              </>
            ))}
            <Grid item xs={12}>
              <div className='bg-[#4C4E640D] p-4 flex items-center gap-4 rounded-[8px]'>
                <i className='triangle-pentung-icon text-error size-16' />
                <Typography>
                  Proses end period akan secara otomatis membuat item Jurnal Umum berisi penyesuaian nilai Aset dan
                  perubahan lain yang terkait dengan kurs mata uang
                </Typography>
              </div>
            </Grid>
          </Grid>
        </DialogContent>
      </form>
      <DialogActions className='gap-2 sm:gap-6 max-sm:flex-col max-sm:items-center justify-center pbs-0 sm:pbe-10 sm:px-16'>
        <Button
          onClick={() => {
            setOpen(false)
          }}
          disabled={createEndPeriodLoading}
          variant='outlined'
          className='is-full sm:is-auto'
        >
          BATALKAN
        </Button>
        <LoadingButton
          loading={createEndPeriodLoading}
          startIcon={<></>}
          loadingPosition='start'
          variant='contained'
          onClick={handleSubmit(handleSubmitItem)}
          className='px-8 is-full !ml-0 sm:is-auto'
        >
          PROSES END PERIOD
        </LoadingButton>
      </DialogActions>
    </Dialog>
  )
}

export default AddEndPeriodsDialog
