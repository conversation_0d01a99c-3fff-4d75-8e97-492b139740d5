import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import { tableColumns } from '../config/table'
import { useMemo } from 'react'
import { Grid, Button, Card, Typography } from '@mui/material'
import DebouncedInput from '@/components/DebounceInput'
import { useEndPeriod } from '../context/EndPeriodContext'
import Table from '@/components/table'
import { useRouter } from '@/routes/hooks'
import { useLocation } from 'react-router-dom'
import { encryptorId } from '../config/utils'
import Permission from '@/core/components/Permission'

const EndperiodList = () => {
  const router = useRouter()
  const { pathname } = useLocation()
  const {
    endPeriodListResponse: { items, totalItems },
    endperiodParams: { search, limit, page },
    setPartialEndperiodParams,
    setEndperiodParams,
    setAddEndPeriodDialogOpen
  } = useEndPeriod()

  const tableOptions: any = useMemo(
    () => ({
      data: items ?? [],
      columns: tableColumns({
        detail: row => {
          router.push(`${pathname}/${encryptorId(row.id)}`)
        }
      }),
      initialState: {
        pagination: {
          pageSize: limit,
          pageIndex: page - 1
        }
      },
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [items, limit, page]
  )
  const table = useReactTable(tableOptions)

  return (
    <Card>
      <div className='flex justify-between gap-4 p-5 flex-col items-start sm:flex-row items-center'>
        <div className='flex gap-4 items-center flex-col sm:flex-row is-full sm:is-auto'>
          <DebouncedInput
            value={search}
            onChange={value => setEndperiodParams(prev => ({ ...prev, page: 1, search: value as string }))}
            placeholder='Cari..'
            className='is-full sm:w-[240px]'
          />
        </div>
        <div className='flex items-center gap-x-4 max-sm:gap-y-4 is-full flex-col sm:is-auto sm:flex-row'>
          {/* <Button
            color='secondary'
            variant='outlined'
            startIcon={<i className='ri-upload-2-line' />}
            className='is-full sm:is-auto'
          >
            Ekspor
          </Button> */}
          <Permission permission='period.create'>
            <Button
              variant='contained'
              onClick={() => setAddEndPeriodDialogOpen(true)}
              startIcon={<i className='ic-baseline-add' />}
              className='is-full sm:is-auto'
            >
              Buat
            </Button>
          </Permission>
        </div>
      </div>
      <Table
        table={table}
        emptyLabel={
          <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
            <Typography>Belum ada Data</Typography>
            <Typography className='text-sm text-gray-400'>
              Semua periode nilai mata uang yang telah dibuat akan ditampilkan di sini
            </Typography>
          </td>
        }
        onRowsPerPageChange={pageSize => {
          if (pageSize > totalItems) {
            setEndperiodParams(prev => ({ ...prev, limit: totalItems, page: 1 }))
          } else {
            setPartialEndperiodParams('limit', pageSize)

            const maxPage = Math.ceil(totalItems / pageSize)
            if (page > maxPage) {
              setEndperiodParams(prev => ({ ...prev, page: maxPage }))
            }
          }
        }}
        onPageChange={pageIndex => setPartialEndperiodParams('page', pageIndex)}
      />
    </Card>
  )
}

export default EndperiodList
