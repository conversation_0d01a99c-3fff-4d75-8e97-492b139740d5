import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Grid, Typography } from '@mui/material'
import { Link } from 'react-router-dom'
import EndPeriodInfoCard from './components/EndPeriodInfoCard'
import { useEndPeriod } from '../context/EndPeriodContext'
import { getStatusConfigChip, periodMonthOptions } from '../config/utils'
import { format } from 'date-fns'
import ExchangeRatesCard from './components/ExchangeRatesCard'
import ActivityLogCard from './components/ActivityLogCard'
import { id } from 'date-fns/locale'
import JournalDataCard from './components/JournalDataCard'
import { toast } from 'react-toastify'

const EndPeriodDetailPage = () => {
  const { endPeriodData, endPeriodLogs } = useEndPeriod()

  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Breadcrumbs>
          <Link to='#' replace>
            <Typography color='var(--mui-palette-text-disabled)'>End Period</Typography>
          </Link>
          <Link to='/accounting/end-periods' replace>
            <Typography color='var(--mui-palette-text-disabled)'>List Periode</Typography>
          </Link>
          <Typography>Detail Periode</Typography>
        </Breadcrumbs>
      </Grid>
      <Grid item xs={12}>
        <div className='flex justify-between items-end'>
          <div className='flex flex-col'>
            <div className='flex gap-2 items-center'>
              <Typography variant='h4'>
                Periode {periodMonthOptions.find(option => option.value === endPeriodData?.month)?.label}{' '}
              </Typography>
              <Chip
                label={getStatusConfigChip(endPeriodData?.status).label}
                color={getStatusConfigChip(endPeriodData?.status).color as any}
                variant='tonal'
                size='small'
              />
            </div>
            <Typography>
              {endPeriodData?.createdAt ? format(endPeriodData?.createdAt, 'eeee, dd/MM/yyyy', { locale: id }) : '-'}
            </Typography>
          </div>
        </div>
      </Grid>
      <Grid item xs={12} md={6}>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <EndPeriodInfoCard />
          </Grid>
          <Grid item xs={12}>
            <ExchangeRatesCard />
          </Grid>
        </Grid>
      </Grid>
      <Grid item xs={12} md={6}>
        <ActivityLogCard logList={endPeriodLogs?.items ?? []} />
      </Grid>
      <Grid item xs={12}>
        <JournalDataCard />
      </Grid>
    </Grid>
  )
}

export default EndPeriodDetailPage
