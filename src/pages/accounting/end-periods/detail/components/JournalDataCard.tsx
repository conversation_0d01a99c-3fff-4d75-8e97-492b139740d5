import { Card, CardContent, Grid, Typography } from '@mui/material'
import { useEndPeriod } from '../../context/EndPeriodContext'
import {
  useReactTable,
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel
} from '@tanstack/react-table'
import { tableColumns } from '@/pages/accounting/general-ledger/config/table'
import { useEffect, useMemo, useState } from 'react'
import Table from '@/components/table'
import { useForm, useWatch } from 'react-hook-form'
import { GeneralLedgerLineType } from '@/types/accountTypes'
import { toast } from 'react-toastify'
import { useMenu } from '@/components/menu/contexts/menuContext'
import { useUpdateGeneralLedger } from '@/api/services/account/mutation'
import { GeneralLedgerTypeEnum } from '@/types/payload'
import { useRouter } from '@/routes/hooks'

const JournalDataCard = () => {
  const router = useRouter()
  const { setConfirmState } = useMenu()
  const { journalData, fetchEndPeriodLogs, fetchJournalData, fetchEndPeriodData, generalLedgerList } = useEndPeriod()
  const { reset, control, watch } = useForm<{ lines: GeneralLedgerLineType[] }>()

  const [isEdit, setIsEdit] = useState(false)

  const lines = useWatch({ control, name: 'lines' })

  const { mutate: updateGeneralLedger, isLoading: loadingMutate } = useUpdateGeneralLedger()

  const toggleEdit = () => {
    if (isEdit) {
      setConfirmState({
        open: true,
        title: 'Simpan Perubahan',
        content: 'Apakah kamu yakin akan menyimpan perubahan jurnal, action ini tidak dapat diubah',
        confirmText: 'Simpan',
        onConfirm: () => {
          updateGeneralLedger(
            {
              id: journalData?.id,
              ...journalData,
              type: journalData?.type as GeneralLedgerTypeEnum,
              lines: lines.map(line => ({
                accountId: line.accountId,
                amount: line.credit || line.debit,
                description: line.description,
                type: !!line.credit ? 'CREDIT' : 'DEBIT',
                id: line?.id
              }))
            },
            {
              onSuccess: () => {
                toast.success('Perubahan berhasil disimpan')
                fetchJournalData()
                fetchEndPeriodData()
                fetchEndPeriodLogs()
                setIsEdit(!isEdit)
              }
            }
          )
        }
      })
      return
    }
    setIsEdit(!isEdit)
  }

  const tableOptions: any = useMemo(
    () => ({
      data: generalLedgerList ?? [],
      columns: tableColumns({
        detail: row => router.push(`/accounting/general-ledger/list/${row.id}`)
      }),
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [generalLedgerList, control, isEdit]
  )

  const table = useReactTable(tableOptions)

  useEffect(() => {
    if (journalData?.lines?.length > 0) {
      reset({ lines: journalData?.lines?.map(line => ({ ...line, accountId: line?.account?.id })) })
    }
  }, [journalData])

  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Jurnal Terbentuk</Typography>
          {/* <Permission permission={['journal.update']}>
            <LoadingButton loading={loadingMutate} variant={!isEdit ? 'outlined' : 'contained'} onClick={toggleEdit}>
              {!isEdit ? 'Edit Transaksi' : 'Selesai'}
            </LoadingButton>
          </Permission> */}
        </div>
        <Grid container spacing={4}>
          {/* <Grid item xs={12}>
            <div className='flex flex-col md:flex-row justify-between items-center gap-4'>
              <div className='flex-1 is-full sm:is-auto bg-[#4C4E640D] flex flex-col gap-1 p-3'>
                <small>Total Debit</small>
                <Typography className='font-medium'>
                  {thousandSeparator(lines?.reduce((acc, curr) => acc + curr.debit, 0) ?? 0)}
                </Typography>
              </div>
              <div className='flex-1 is-full sm:is-auto bg-[#4C4E640D] flex flex-col gap-1 p-3'>
                <small>Total Kredit</small>
                <Typography className='font-medium'>
                  {thousandSeparator(lines?.reduce((acc, curr) => acc + curr.credit, 0) ?? 0)}
                </Typography>
              </div>
            </div>
          </Grid> */}
          <Grid item xs={12}>
            <div className='rounded-[8px] shadow-md'>
              <Table
                headerColor='green'
                table={table}
                emptyLabel={
                  <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
                    <Typography>Belum ada Data</Typography>
                    <Typography className='text-sm text-gray-400'>
                      Semua transaksi nilai mata uang yang telah dibuat akan ditampilkan di sini
                    </Typography>
                  </td>
                }
              />
            </div>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  )
}

export default JournalDataCard
