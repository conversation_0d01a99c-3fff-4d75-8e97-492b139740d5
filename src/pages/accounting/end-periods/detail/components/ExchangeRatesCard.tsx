import { Card, CardContent, Grid, Typography } from '@mui/material'
import { useEndPeriod } from '../../context/EndPeriodContext'
import { toCurrency } from '@/utils/helper'
import { useMemo } from 'react'
import { useAuth } from '@/contexts/AuthContext'

const ExchangeRatesCard = () => {
  const { endPeriodData } = useEndPeriod()
  const { currenciesList, defaultCurrency } = useAuth()

  const baseCurrency = useMemo(() => {
    const findCurrency = endPeriodData?.exchangeRates?.find(rate => rate.value === 1)
    return findCurrency?.currency
  }, [endPeriodData])

  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'><PERSON><PERSON></Typography>
        </div>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <div className='flex flex-col gap-1'>
              <small>Mata <PERSON></small>
              <Typography>
                {baseCurrency?.name} ({baseCurrency?.symbol})
              </Typography>
            </div>
          </Grid>
          <Grid item xs={12}>
            <Typography className='font-medium'>Konversi</Typography>
          </Grid>
          <Grid item xs={12}>
            <div className='grid grid-cols-2 gap-4'>
              {endPeriodData?.exchangeRates
                ?.filter(exchange => exchange.value !== 1)
                ?.map(exchange => (
                  <div key={exchange.currencyId} className='flex flex-col gap-1'>
                    <small>Mata Uang {exchange?.currency?.code}</small>
                    <Typography>
                      1 {exchange?.currency?.code} = {toCurrency(exchange?.value)}
                    </Typography>
                  </div>
                ))}
            </div>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  )
}

export default ExchangeRatesCard
