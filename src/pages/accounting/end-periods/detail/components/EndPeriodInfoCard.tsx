import { Card, CardContent, Typography } from '@mui/material'
import { useEndPeriod } from '../../context/EndPeriodContext'
import { format } from 'date-fns'
import { periodMonthOptions } from '../../config/utils'

const EndPeriodInfoCard = () => {
  const { endPeriodData } = useEndPeriod()
  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Detil Periode</Typography>
        </div>
        <div className='grid grid-cols-2 gap-4'>
          <div className='flex flex-col gap-1'>
            <small>Bulan</small>
            <Typography>{periodMonthOptions.find(option => option.value === endPeriodData?.month)?.label}</Typography>
          </div>
          <div className='flex flex-col gap-1'>
            <small><PERSON>hun</small>
            <Typography>{endPeriodData?.year}</Typography>
          </div>
          <div className='flex flex-col gap-1'>
            <small>Tanggal Dibuat</small>
            <Typography>
              {endPeriodData?.createdAt ? format(endPeriodData?.createdAt, 'dd/MM/yyyy, HH:mm') : '-'}
            </Typography>
          </div>
          <div className='flex flex-col gap-1'>
            <small>Tanggal Diperbarui</small>
            <Typography>-</Typography>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default EndPeriodInfoCard
