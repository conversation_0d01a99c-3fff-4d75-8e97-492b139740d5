import { ThousandField } from '@/components/numeric/CurrencyField'
import { GeneralLedgerLineType } from '@/types/accountTypes'
import { formatThousandSeparator, parseLocalizedNumber, thousandSeparator } from '@/utils/helper'
import { TextField } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'
import { Control, Controller } from 'react-hook-form'

const columnHelper = createColumnHelper<GeneralLedgerLineType>()

type RowAction = {
  isEdit?: boolean
  control: Control<{ lines: GeneralLedgerLineType[] }, any>
}

export const tableColumns = ({ isEdit, ...rowAction }: RowAction) => [
  columnHelper.accessor('account.accountType.name', {
    header: 'Kode Akun',
    size: 40,
    cell: ({ row }) => row.original?.account?.code
  }),
  columnHelper.accessor('account.name', {
    header: '<PERSON><PERSON>kun',
    cell: ({ row }) => row.original?.account?.name
  }),
  columnHelper.accessor('debit', {
    header: 'Debit',
    size: 200,
    cell: ({ row }) =>
      !isEdit ? (
        formatThousandSeparator(row.original?.debit)
      ) : (
        <Controller
          control={rowAction.control}
          name={`lines.${row.index}.debit`}
          render={({ field: { onChange, value } }) => (
            <TextField
              fullWidth
              size='small'
              defaultValue={value}
              onBlur={e => onChange(parseLocalizedNumber(e.target.value))}
              variant='outlined'
              placeholder='Debit'
              InputProps={{ inputComponent: ThousandField as any }}
            />
          )}
        />
      )
  }),
  columnHelper.accessor('credit', {
    header: 'Kredit',
    size: 200,
    cell: ({ row }) =>
      !isEdit ? (
        formatThousandSeparator(row.original?.credit)
      ) : (
        <Controller
          control={rowAction.control}
          name={`lines.${row.index}.credit`}
          render={({ field: { onChange, value } }) => (
            <TextField
              fullWidth
              size='small'
              defaultValue={value}
              onBlur={e => onChange(parseLocalizedNumber(e.target.value))}
              variant='outlined'
              placeholder='Kredit'
              InputProps={{ inputComponent: ThousandField as any }}
            />
          )}
        />
      )
  })
]
