import { Card, CardActions, CardContent, CardHeader, Divider, Typography } from '@mui/material'
import { toCurrency } from '@/utils/helper'
import { useOwnerEquityContext } from '../../context/OwnerEquityContext'
import { format } from 'date-fns'
import { id } from 'date-fns/locale'

const OwnerEquityList = () => {
  const { site, filter } = useOwnerEquityContext()

  const { startDate, endDate } = filter

  type RowVariant = 'section' | 'group' | 'item' | 'subtotal' | 'total' | 'group-total' | 'summary'
  type PLRow = {
    variant: RowVariant
    label: string
    amount?: number | null
    indent?: number
    color?: 'default' | 'error'
  }

  // Template rows; replace amounts/descriptions with BE data
  const rows: PLRow[] = [
    { variant: 'item', label: 'Ekuitas Pemilik Awal Periode', amount: 100000000 },

    { variant: 'section', label: 'Penambahan Ekuitas' },
    { variant: 'item', label: 'Pendapatan Be<PERSON>ih', amount: 0, indent: 1 },
    { variant: 'item', label: 'Investasi Kurun Periode', amount: 0, indent: 1 },
    { variant: 'item', label: 'Penarikan', amount: 0, indent: 1 },
    { variant: 'subtotal', label: 'Total Penambahan Ekuitas', amount: 0 }
  ]

  // Calculate total equity at end of period
  const totalEquity = 100000000 + 0 // Ekuitas awal + total penambahan

  const renderRow = (row: PLRow, idx: number) => {
    const amountText = row.amount !== null && row.amount !== undefined ? toCurrency(row.amount) : '-'
    const ml = row.indent ? row.indent * 16 : 0

    switch (row.variant) {
      case 'section':
        return (
          <div key={idx} className='col-span-2 mt-2'>
            <Typography className='font-semibold'>{row.label}</Typography>
          </div>
        )
      case 'group':
        return (
          <div key={idx} className='col-span-2'>
            <Typography className='font-semibold' style={{ marginLeft: ml }}>
              {row.label}
            </Typography>
          </div>
        )
      case 'item':
        return (
          <>
            <Typography key={`l-${idx}`} style={{ marginLeft: ml }}>
              {row.label}
            </Typography>
            <Typography key={`r-${idx}`} align='right' className={row.amount && row.amount < 0 ? 'text-error' : ''}>
              {amountText}
            </Typography>
          </>
        )
      case 'group-total':
        return (
          <>
            <Typography key={`lgt-${idx}`} className='font-semibold' style={{ marginLeft: ml }}>
              {row.label}
            </Typography>
            <Typography
              key={`rgt-${idx}`}
              align='right'
              className={`font-semibold ${row.amount && row.amount < 0 ? 'text-error' : ''}`}
            >
              {amountText}
            </Typography>
          </>
        )
      case 'summary':
        return (
          <>
            <div key={`d-${idx}`} className='col-span-2'>
              <Divider variant='inset' />
            </div>
            <Typography key={`ls-${idx}`}>{row.label}</Typography>
            <Typography key={`rs-${idx}`} align='right' className={row.amount && row.amount < 0 ? 'text-error' : ''}>
              {amountText}
            </Typography>
          </>
        )
      case 'total':
        return (
          <>
            <div key={`d2-${idx}`} className='col-span-2'>
              <Divider variant='inset' />
            </div>
            <Typography key={`lt-${idx}`} className='font-semibold'>
              {row.label}
            </Typography>
            <Typography
              key={`rt-${idx}`}
              align='right'
              className={`font-semibold ${row.amount && row.amount < 0 ? 'text-error' : ''}`}
            >
              {amountText}
            </Typography>
          </>
        )
      default:
        return null
    }
  }

  return (
    <Card>
      <CardContent className='flex flex-col gap-5 py-8'>
        <div className='flex flex-col items-center justify-center'>
          <Typography variant='h5' color='primary'>
            PT Equalindo Makmur Alam Sejahtera
          </Typography>
          <Typography variant='h4'>Laporan Perubahan Ekuitas Pemilik</Typography>
          <Typography variant='caption' className='font-semibold text-textPrimary'>
            {format(startDate, 'dd/MM/yyyy', { locale: id })} - {format(endDate, 'dd/MM/yyyy', { locale: id })}
          </Typography>
          <Typography className='font-semibold'>Lokasi: {site?.name}</Typography>
        </div>
        <Card className='mx-20'>
          <CardHeader
            action={
              <Typography align='right' className='font-normal text-textPrimary text-sm'>
                Nilai
              </Typography>
            }
            title={<Typography className='font-normal text-textPrimary text-base'>Deskripsi</Typography>}
            className='bg-[#DBF7E8]'
          />
          <CardContent className='grid grid-cols-2 gap-2 p-4 mt-4'>
            {rows.map((row, idx) => renderRow(row, idx))}
          </CardContent>
          <CardActions className='p-0'>
            <div className='flex justify-between w-full h-full p-4 bg-[#DBF7E8]'>
              <Typography variant='h6'>Ekuitas Pemilik Akhir Periode</Typography>
              <Typography variant='h6' color='primary'>
                {toCurrency(totalEquity)}
              </Typography>
            </div>
          </CardActions>
        </Card>
      </CardContent>
    </Card>
  )
}

export default OwnerEquityList
