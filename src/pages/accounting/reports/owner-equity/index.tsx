import { Breadcrumbs, <PERSON>ton, Grid, Typography } from '@mui/material'
import { Link } from 'react-router-dom'
import OwnerEquityList from './components/OwnetEquityList'
import { useProfitLoss } from '../context/ProfitLossContext'
import { format } from 'date-fns'
import { id } from 'date-fns/locale'
import { useCashFlowContext } from '../context/CashFlowContext'
import { useOwnerEquityContext } from '../context/OwnerEquityContext'

const OwnerEquityReport = () => {
  const { filter } = useOwnerEquityContext()

  const { startDate, endDate } = filter

  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Breadcrumbs>
          <Link to='#' replace>
            <Typography color='var(--color-text-disabled)'>Laporan</Typography>
          </Link>
          <Link to='/report/finance' replace>
            <Typography color='var(--color-text-disabled)'><PERSON>uangan</Typography>
          </Link>
          <Typography>Detil Laporan</Typography>
        </Breadcrumbs>
      </Grid>
      <Grid item xs={12}>
        <div className='flex justify-between items-end'>
          <div className='flex flex-col'>
            <Typography variant='h4'>Detil Laporan Perubahan Ekuitas Pemilik</Typography>
            <Typography>
              {format(startDate, 'dd/MM/yyyy', { locale: id })} - {format(endDate, 'dd/MM/yyyy', { locale: id })}
            </Typography>
          </div>
          <div className='flex gap-2'>
            <Button
              color='secondary'
              variant='outlined'
              startIcon={<i className='ri-upload-2-line' />}
              className='is-full sm:is-auto'
            >
              Ekspor
            </Button>
            <Button
              variant='contained'
              startIcon={<i className='ic-outline-local-printshop size-5' />}
              className='is-full sm:is-auto'
            >
              Cetak
            </Button>
          </div>
        </div>
      </Grid>
      <Grid item xs={12}>
        <OwnerEquityList />
      </Grid>
    </Grid>
  )
}

export default OwnerEquityReport
