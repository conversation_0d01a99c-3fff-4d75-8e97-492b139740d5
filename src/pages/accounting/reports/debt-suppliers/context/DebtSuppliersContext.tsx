import React, { useState } from 'react'
import { DebtReportType } from '../config/types'
import { ChildrenType } from '@/core/types'
import OutstandingDebtDialog from '../components/dialogs/outstanding-debts-dialog'
import SubsidiaryLedgerDialog from '../components/dialogs/subsidiary-ledger-dialog'
import VendorDebtHistoryDialog from '../components/dialogs/vendor-debt-history-dialog'
import InvoicePaymentDialog from '../components/dialogs/invoice-details-dialog'
import DebtAgeSummaryDialog from '../components/dialogs/debt-age-summary-dialog'
import DebtAgeDetailsDialog from '../components/dialogs/debt-age-details-dialog'
import DebtAgeGraphDialog from '../components/dialogs/debt-age-graph-dialog'

type DebtSuppliersContextType = {
  handleReportClick: (type: DebtReportType) => void
}

const DebtSuppliersCustomerContext = React.createContext<DebtSuppliersContextType>({} as DebtSuppliersContextType)

export const useDebtSuppliersReports = () => {
  const context = React.useContext(DebtSuppliersCustomerContext)
  if (context === undefined) {
    throw new Error('useFinanceReport must be used within a FinanceReportContext')
  }
  return context
}

export const DebtSuppliersReportProvider = ({ children }: ChildrenType) => {
  const [
    {
      OUTSTANDING_DEBT,
      SUBSIDIARY_LEDGER,
      VENDOR_DEBT_HISTORY,
      INVOICE_PAYMENT_DETAILS,
      DEBT_AGE_SUMMARY,
      DEBT_AGE_DETAILS,
      DEBT_AGE_GRAPH
    },
    setDialog
  ] = useState<{
    [DebtReportType.OutstandingDebt]?: boolean
    [DebtReportType.SubsidiaryLedger]?: boolean
    [DebtReportType.VendorDebtHistory]?: boolean
    [DebtReportType.InvoicePaymentDetails]?: boolean
    [DebtReportType.DebtAgeSummary]?: boolean
    [DebtReportType.DebtAgeDetails]?: boolean
    [DebtReportType.DebtAgeGraph]?: boolean
  }>({})

  const handleReportClick = (type: DebtReportType) => {
    setDialog(current => ({ ...current, [type]: !current[type] }))
  }

  const value = {
    handleReportClick
  }
  return (
    <DebtSuppliersCustomerContext.Provider value={value}>
      {[
        {
          key: 'OUTSTANDING_DEBT',
          Dialog: OutstandingDebtDialog
        },
        {
          key: 'SUBSIDIARY_LEDGER',
          Dialog: SubsidiaryLedgerDialog
        },
        {
          key: 'VENDOR_DEBT_HISTORY',
          Dialog: VendorDebtHistoryDialog
        },
        {
          key: 'INVOICE_PAYMENT_DETAILS',
          Dialog: InvoicePaymentDialog
        },
        {
          key: 'DEBT_AGE_SUMMARY',
          Dialog: DebtAgeSummaryDialog
        },
        {
          key: 'DEBT_AGE_DETAILS',
          Dialog: DebtAgeDetailsDialog
        },
        {
          key: 'DEBT_AGE_GRAPH',
          Dialog: DebtAgeGraphDialog
        }
      ].map(
        ({ key, Dialog }) =>
          !!eval(key) && (
            <Dialog open={eval(key)} onClose={() => setDialog(current => ({ ...current, [key]: false }))} key={key} />
          )
      )}
      {children}
    </DebtSuppliersCustomerContext.Provider>
  )
}
