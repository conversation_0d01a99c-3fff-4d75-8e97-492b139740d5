import ChevronRight from '@/components/menu/svg/ChevronRight'
import { Card, CardContent, Grid, IconButton, Typography } from '@mui/material'
import { receivablesReportOptions } from '../config/utils'
import { useDebtSuppliersReports } from '../context/DebtSuppliersContext'

const ReceivablesCustomerList = () => {
  const { handleReportClick } = useDebtSuppliersReports()
  return (
    <Grid container spacing={4}>
      {receivablesReportOptions.map(report => (
        <Grid key={report.type} item xs={12} md={6}>
          <Card className='h-[100px]'>
            <CardContent className='p-4 h-full flex justify-between'>
              <div className='flex flex-col h-full justify-between'>
                <Typography variant='h5'>{report.label}</Typography>
                <Typography>{report.description}</Typography>
              </div>
              <IconButton onClick={() => handleReportClick(report.type)}>
                <ChevronRight />
              </IconButton>
            </CardContent>
          </Card>
        </Grid>
      ))}
    </Grid>
  )
}

export default ReceivablesCustomerList
