import AppReactDatepicker from '@/components/libs/styles/AppReactDatepicker'
import { DialogProps } from '../../../finance/config/types'
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography
} from '@mui/material'
import { Controller, useForm } from 'react-hook-form'
import { useAuth } from '@/contexts/AuthContext'
import { useRouter } from '@/routes/hooks'
import { format, formatISO, toDate } from 'date-fns'
import LoadingButton from '@mui/lab/LoadingButton'
import { useLocation } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'
import CompanyQueryMethods, { PROJECT_LIST_QUERY_KEY } from '@/api/services/company/query'
import { defaultListData } from '@/api/queryClient'
import { ListResponse } from '@/types/api'
import { ProjectType } from '@/types/projectTypes'

const DebtAgeSummaryDialog = ({ open, onClose }: DialogProps) => {
  const { ownSiteList } = useAuth()
  const { pathname } = useLocation()
  const { control, handleSubmit, reset, getValues } = useForm()
  const router = useRouter()

  const handleClose = () => {
    onClose(false)
  }

  const { data: projectListResponse } = useQuery({
    queryKey: [PROJECT_LIST_QUERY_KEY],
    queryFn: () => CompanyQueryMethods.getProjectList({ limit: Number.MAX_SAFE_INTEGER }),
    placeholderData: defaultListData as ListResponse<ProjectType>
  })

  const onSubmitFilter = inputValues => {
    if (inputValues.date) {
      inputValues.date = format(inputValues.date, 'MM-dd-yyyy')
    } else {
      delete inputValues.date
    }

    router.push(`${pathname}/debt-age-summary?${new URLSearchParams(inputValues).toString()}`)
    handleClose()
  }

  return (
    <Dialog fullWidth maxWidth='sm' scroll='body' open={open} onClose={handleClose}>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-16'>
        Laporan Ringkasan Umur Hutang
        <Typography component='span' className='flex flex-col text-center'>
          Pilih parameter yang akan ditampilkan di laporan ini
        </Typography>
      </DialogTitle>
      <form onSubmit={e => e.preventDefault()}>
        <DialogContent className='overflow-visible pbs-0 sm:pbe-6 sm:px-16'>
          <IconButton onClick={handleClose} className='absolute block-start-4 inline-end-4'>
            <i className='ri-close-line text-textSecondary' />
          </IconButton>
          <Grid container spacing={4}>
            <Grid item xs={12}>
              <Controller
                control={control}
                name='date'
                rules={{ required: true }}
                render={({ field: { value, onChange }, fieldState: { error } }) => (
                  <AppReactDatepicker
                    boxProps={{ className: 'is-full' }}
                    selected={value ? toDate(value) : undefined}
                    onChange={(date: Date) => onChange(formatISO(date))}
                    dateFormat='dd/MM/yyyy'
                    customInput={
                      <TextField fullWidth label='Per Tanggal' placeholder='Pilih tanggal' error={!!error} />
                    }
                  />
                )}
              />
            </Grid>
            <Grid item xs={12}>
              <Controller
                control={control}
                name='projectId'
                rules={{ required: true }}
                render={({ field: { value, onChange }, fieldState: { error } }) => (
                  <FormControl fullWidth>
                    <InputLabel id='site-select'>Proyek</InputLabel>
                    <Select
                      value={value}
                      onChange={onChange}
                      labelId='site-select'
                      id='site-select'
                      label='Proyek'
                      placeholder='Pilih Proyek'
                      error={!!error}
                    >
                      {projectListResponse?.items?.map(project => (
                        <MenuItem key={project.id} value={project.id}>
                          {project.name}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                )}
              />
            </Grid>
          </Grid>
        </DialogContent>
      </form>
      <DialogActions className='justify-center pbs-0 sm:pbe-10 sm:px-16'>
        <Button variant='outlined' onClick={handleClose}>
          Batalkan
        </Button>
        <LoadingButton variant='contained' color='primary' onClick={handleSubmit(onSubmitFilter)}>
          Tampilkan
        </LoadingButton>
      </DialogActions>
    </Dialog>
  )
}

export default DebtAgeSummaryDialog
