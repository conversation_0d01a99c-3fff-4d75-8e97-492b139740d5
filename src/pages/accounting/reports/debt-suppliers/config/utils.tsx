import { DebtSuppliersOptions, DebtReportType } from './types'

export const receivablesReportOptions: DebtSuppliersOptions[] = [
  {
    label: 'Hutang Beredar',
    description: 'Lihat laporan hutang yang belum dibayarkan',
    href: '/report/piutang/faktur-belum-lunas',
    type: DebtReportType.OutstandingDebt
  },
  {
    label: 'Buku Besar Pembantu Hutang',
    description: 'Lihat laporan nilai faktur, penerimaan, dan retur',
    href: '/report/piutang/buku-besar-pembantu',
    type: DebtReportType.SubsidiaryLedger
  },
  {
    label: 'Histori Hutang Vendor',
    description: 'Lihat laporan riwayat hutang ke vendor',
    href: '/report/piutang/histori',
    type: DebtReportType.VendorDebtHistory
  },
  {
    label: 'Rincian Pembayaran Faktur',
    description: 'Lihat laporan rincian pembayaran faktur',
    href: '/report/piutang/rincian-pembayaran',
    type: DebtReportType.InvoicePaymentDetails
  },
  {
    label: 'Ringkasan Umur Hutang',
    description: '<PERSON>hat ringkasan umur hutang dari faktur yang belum lunas',
    href: '/report/piutang/ringkasan-umur',
    type: DebtReportType.DebtAgeSummary
  },
  {
    label: 'Rincian Umur Hutang',
    description: 'Lihat laporan terperinci umur piutang dari faktur yang belum lunas',
    href: '/report/piutang/rincian-umur',
    type: DebtReportType.DebtAgeDetails
  },
  {
    label: 'Grafik Umur Hutang',
    description: 'Lihat grafik umur piutang dari faktur yang belum lunas',
    href: '/report/piutang/grafik-umur',
    type: DebtReportType.DebtAgeGraph
  }
]
