export enum DebtReportType {
  OutstandingDebt = 'OUTSTANDING_DEBT',
  SubsidiaryLedger = 'SUBSIDIARY_LEDGER',
  VendorDebtHistory = 'VENDOR_DEBT_HISTORY',
  InvoicePaymentDetails = 'INVOICE_PAYMENT_DETAILS',
  DebtAgeSummary = 'DEBT_AGE_SUMMARY',
  DebtAgeDetails = 'DEBT_AGE_DETAILS',
  DebtAgeGraph = 'DEBT_AGE_GRAPH'
}

export type DebtSuppliersOptions = {
  label: string
  description: string
  href: string
  type: DebtReportType
}
