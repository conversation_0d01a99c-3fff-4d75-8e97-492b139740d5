export const monthFormatter = (month: string) => {
  if (!month) return ''
  const numericMonth = +month
  const months = [
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON>',
    'April',
    '<PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    'September',
    '<PERSON><PERSON><PERSON>',
    'November',
    '<PERSON><PERSON>ber'
  ]
  return months[numericMonth - 1]
}
