import { LedgerReportOption, LedgerReportType } from './types'

export const ledgerReportOptions: LedgerReportOption[] = [
  {
    label: 'Bukti Jurnal Umum',
    description: 'Lihat laporan semua jurnal dan transaksi',
    href: '/laporan/ledger/journal',
    type: LedgerReportType.GENERAL_JOURNAL
  },
  {
    label: 'Daftar Akun',
    description: 'Lihat laporan daftar akun perkiraan',
    href: '/laporan/ledger/account-list',
    type: LedgerReportType.ACCOUNT_LIST
  },
  {
    label: 'Ringkasan Buku Besar',
    description: 'Lihat laporan ringkasan buku besar',
    href: '/laporan/ledger/ledger-summary',
    type: LedgerReportType.LEDGER_SUMMARY
  },
  {
    label: 'Buku Besar - Rincian',
    description: 'Lihat laporan terperinci buku besar',
    href: '/laporan/ledger/ledger-detail',
    type: LedgerReportType.LEDGER_DETAIL
  },
  {
    label: 'Laba/Rugi Terealisir',
    description:
      'Lihat laporan daftar Laba atau rugi yang disebabkan oleh perbedaan nilai tukar antara faktur dengan pembayarannya',
    href: '/laporan/ledger/realized-profit-loss',
    type: LedgerReportType.REALIZED_PROFIT_LOSS
  },
  {
    label: 'Laba/Rugi Tidak Terealisir',
    description:
      'Lihat laporan daftar Laba atau rugi tidak terealisasi dimana terjadi perbedaan nilai tukar setelah proses akhir bulan',
    href: '/laporan/ledger/unrealized-profit_loss',
    type: LedgerReportType.UNREALIZED_PROFIT_LOSS
  },
  {
    label: 'Neraca Saldo',
    description: 'Lihat laporan nilai perubahan akun perkiraan',
    href: '/laporan/ledger/trial-balance',
    type: LedgerReportType.TRIAL_BALANCE
  }
]
