import { ChildrenType } from '@/core/types'
import React, { createContext, useState } from 'react'
import { LedgerReportType } from '../config/types'
import GeneralLedgerDialog from '../components/dialogs/general-ledger-dialog'
import AccountListDialog from '../components/dialogs/account-list-dialog'
import SummaryLedgerDialog from '../components/dialogs/ledger-summary-dialog'
import SummaryLedgerDetailDialog from '../components/dialogs/ledger-summary-detailed-dialog'
import RealizedPNLDialog from '../components/dialogs/realized-profit-loss-dialog'
import UnrealizedPNLDialog from '../components/dialogs/unrealized-profit-loss-dialog'
import BalanceSheetDialog from '../components/dialogs/balance-sheet-dialog'

type LedgerContextProps = {
  handleReportClick: (type: LedgerReportType) => void
}

const LedgerReportContext = createContext<LedgerContextProps>({} as LedgerContextProps)

export const useLedgerReport = () => {
  const context = React.useContext(LedgerReportContext)
  if (context === undefined) {
    throw new Error('useFinanceReport must be used within a FinanceReportContext')
  }
  return context
}

export const LedgerReportProvider = ({ children }: ChildrenType) => {
  const [
    {
      GENERAL_JOURNAL,
      ACCOUNT_LIST,
      LEDGER_SUMMARY,
      LEDGER_DETAIL,
      REALIZED_PROFIT_LOSS,
      UNREALIZED_PROFIT_LOSS,
      TRIAL_BALANCE
    },
    setDialog
  ] = useState<{
    [LedgerReportType.GENERAL_JOURNAL]?: boolean
    [LedgerReportType.ACCOUNT_LIST]?: boolean
    [LedgerReportType.LEDGER_SUMMARY]?: boolean
    [LedgerReportType.LEDGER_DETAIL]?: boolean
    [LedgerReportType.REALIZED_PROFIT_LOSS]?: boolean
    [LedgerReportType.UNREALIZED_PROFIT_LOSS]?: boolean
    [LedgerReportType.TRIAL_BALANCE]?: boolean
  }>({})

  const handleReportClick = (type: LedgerReportType) => {
    setDialog(current => ({ ...current, [type]: !current[type] }))
  }

  const value = {
    handleReportClick
  }
  return (
    <LedgerReportContext.Provider value={value}>
      {[
        {
          key: 'GENERAL_JOURNAL',
          Dialog: GeneralLedgerDialog
        },
        {
          key: 'ACCOUNT_LIST',
          Dialog: AccountListDialog
        },
        {
          key: 'LEDGER_SUMMARY',
          Dialog: SummaryLedgerDialog
        },
        {
          key: 'LEDGER_DETAIL',
          Dialog: SummaryLedgerDetailDialog
        },
        {
          key: 'REALIZED_PROFIT_LOSS',
          Dialog: RealizedPNLDialog
        },
        {
          key: 'UNREALIZED_PROFIT_LOSS',
          Dialog: UnrealizedPNLDialog
        },
        {
          key: 'TRIAL_BALANCE',
          Dialog: BalanceSheetDialog
        }
      ].map(
        ({ key, Dialog }) =>
          !!eval(key) && (
            <Dialog open={eval(key)} onClose={() => setDialog(current => ({ ...current, [key]: false }))} key={key} />
          )
      )}
      {children}
    </LedgerReportContext.Provider>
  )
}
