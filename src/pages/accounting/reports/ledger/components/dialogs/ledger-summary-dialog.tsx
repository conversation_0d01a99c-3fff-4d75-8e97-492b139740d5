import AppReactDatepicker from '@/components/libs/styles/AppReactDatepicker'
import { DialogProps } from '../../../finance/config/types'
import {
  Button,
  Checkbox,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  FormControlLabel,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography
} from '@mui/material'
import { Controller, useForm, useWatch } from 'react-hook-form'
import { useAuth } from '@/contexts/AuthContext'
import { useRouter } from '@/routes/hooks'
import { format, formatISO, toDate } from 'date-fns'
import LoadingButton from '@mui/lab/LoadingButton'
import { useLocation } from 'react-router-dom'

const SummaryLedgerDialog = ({ open, onClose }: DialogProps) => {
  const { ownSiteList } = useAuth()
  const { pathname } = useLocation()
  const { control, handleSubmit, reset, getValues } = useForm()
  const router = useRouter()

  const startDateWatch = useWatch({
    control,
    name: 'startDate'
  })

  const handleClose = () => {
    onClose(false)
  }

  const onSubmitFilter = inputValues => {
    if (inputValues.startDate) {
      inputValues.startDate = format(inputValues.startDate, 'MM-dd-yyyy')
    } else {
      delete inputValues.startDate
    }
    if (inputValues.endDate) {
      inputValues.endDate = format(inputValues.endDate, 'MM-dd-yyyy')
    } else {
      delete inputValues.endDate
    }
    inputValues.showZeroBalance = !!inputValues.showZeroBalance

    router.push(`${pathname}/ledger-summary?${new URLSearchParams(inputValues).toString()}`)
    handleClose()
  }

  return (
    <Dialog fullWidth maxWidth='sm' scroll='body' open={open} onClose={handleClose}>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-16'>
        Laporan Ringkasan Buku Besar
        <Typography component='span' className='flex flex-col text-center'>
          Pilih parameter yang akan ditampilkan di laporan ini
        </Typography>
      </DialogTitle>
      <form onSubmit={e => e.preventDefault()}>
        <DialogContent className='overflow-visible pbs-0 sm:pbe-6 sm:px-16'>
          <IconButton onClick={handleClose} className='absolute block-start-4 inline-end-4'>
            <i className='ri-close-line text-textSecondary' />
          </IconButton>
          <Grid container spacing={4}>
            <Grid item xs={12} md={6}>
              <Controller
                control={control}
                name='startDate'
                rules={{ required: true }}
                render={({ field: { value, onChange }, fieldState: { error } }) => (
                  <AppReactDatepicker
                    boxProps={{ className: 'is-full' }}
                    selected={value ? toDate(value) : undefined}
                    onChange={(date: Date) => onChange(formatISO(date))}
                    dateFormat='dd/MM/yyyy'
                    customInput={
                      <TextField fullWidth label='Dari Tanggal' placeholder='Pilih tanggal' error={!!error} />
                    }
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Controller
                control={control}
                name='endDate'
                rules={{ required: true }}
                render={({ field: { value, onChange }, fieldState: { error } }) => (
                  <AppReactDatepicker
                    boxProps={{ className: 'is-full' }}
                    selected={value ? toDate(value) : undefined}
                    onChange={(date: Date) => onChange(formatISO(date))}
                    dateFormat='dd/MM/yyyy'
                    minDate={startDateWatch ? toDate(startDateWatch) : undefined}
                    customInput={
                      <TextField fullWidth label='Sampai Tanggal' placeholder='Pilih tanggal' error={!!error} />
                    }
                  />
                )}
              />
            </Grid>
            <Grid item xs={12}>
              <Controller
                control={control}
                name='siteId'
                rules={{ required: true }}
                render={({ field: { value, onChange }, fieldState: { error } }) => (
                  <FormControl fullWidth>
                    <InputLabel id='site-select'>Lokasi</InputLabel>
                    <Select
                      value={value}
                      onChange={onChange}
                      labelId='site-select'
                      id='site-select'
                      label='Lokasi'
                      placeholder='Pilih Lokasi'
                      error={!!error}
                    >
                      {ownSiteList?.map(site => (
                        <MenuItem key={site.id} value={site.id}>
                          {site.name}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                )}
              />
            </Grid>
            <Grid item xs={12}>
              <Controller
                control={control}
                name='showZeroBalance'
                render={({ field: { value, onChange } }) => (
                  <FormControlLabel
                    control={<Checkbox onChange={onChange} checked={!!value} />}
                    label='Tampilkan data dengan saldo nol'
                  />
                )}
              />
            </Grid>
          </Grid>
        </DialogContent>
      </form>
      <DialogActions className='justify-center pbs-0 sm:pbe-10 sm:px-16'>
        <Button variant='outlined' onClick={handleClose}>
          Batalkan
        </Button>
        <LoadingButton variant='contained' color='primary' onClick={handleSubmit(onSubmitFilter)}>
          Tampilkan
        </LoadingButton>
      </DialogActions>
    </Dialog>
  )
}

export default SummaryLedgerDialog
