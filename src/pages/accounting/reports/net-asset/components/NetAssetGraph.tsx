import { Card, CardActions, CardContent, CardHeader, Divider, Typography } from '@mui/material'
import { ResponsiveBar } from '@nivo/bar'
import { format } from 'date-fns'
import { id } from 'date-fns/locale'
import { toCurrency } from '@/utils/helper'

import { useNetAsset } from '../../context/NetAssetContext'

const NetAssetGraph = () => {
  const { site, filter } = useNetAsset()

  const { startMonth, startYear, endMonth, endYear } = filter

  // Chart data based on the image - Total Aset, Total Kewajiban, and Harta Bersih for 6 months
  const chartData = [
    {
      month: 'Jan 2025',
      'Total Aset': 100000000,
      'Total Kewajiban': 40000000,
      'Harta Bersih': 55000000
    },
    {
      month: 'Feb 2025',
      'Total Aset': 110000000,
      'Total Kewajiban': 60000000,
      'Harta Bersih': 50000000
    },
    {
      month: 'Mar 2025',
      'Total Aset': 88000000,
      'Total Kewajiban': 55000000,
      'Harta Bersih': 30000000
    },
    {
      month: 'Apr 2025',
      'Total Aset': 100000000,
      'Total Kewajiban': 50000000,
      'Harta Bersih': 50000000
    },
    {
      month: 'Mei 2025',
      'Total Aset': 125000000,
      'Total Kewajiban': 50000000,
      'Harta Bersih': 75000000
    },
    {
      month: 'Jun 2025',
      'Total Aset': 120000000,
      'Total Kewajiban': 30000000,
      'Harta Bersih': 88000000
    }
  ]

  return (
    <Card>
      <CardContent className='flex flex-col gap-5 py-8'>
        <div className='flex flex-col items-center justify-center'>
          <Typography variant='h5' color='primary'>
            PT Equalindo Makmur Alam Sejahtera
          </Typography>
          <Typography variant='h4'>Grafik Harta Bersih</Typography>
          <Typography variant='caption' className='font-semibold text-textPrimary'>
            {startMonth}/{startYear} - {endMonth}/{endYear}
          </Typography>
          <Typography className='font-semibold'>Lokasi: {site?.name}</Typography>
        </div>
        <Card className='mx-20'>
          {/* <CardHeader
            action={
              <Typography align='right' className='font-normal text-textPrimary text-sm'>
                {startMonth}/{startYear} - {endMonth}/{endYear}
              </Typography>
            }
            title={<Typography className='font-normal text-textPrimary text-base'>Deskripsi</Typography>}
            className='bg-[#DBF7E8]'
          /> */}
          <CardContent className='p-4 mt-4'>
            <div style={{ height: '400px' }}>
              <ResponsiveBar
                data={chartData}
                keys={['Total Aset', 'Total Kewajiban', 'Harta Bersih']}
                indexBy='month'
                margin={{ top: 50, right: 60, bottom: 80, left: 60 }}
                padding={0.3}
                groupMode='grouped'
                valueScale={{ type: 'linear' }}
                indexScale={{ type: 'band', round: true }}
                colors={['#8884d8', '#ff6b6b', '#87ceeb']}
                axisTop={null}
                axisRight={null}
                axisBottom={{
                  tickSize: 5,
                  tickPadding: 5,
                  tickRotation: 0,
                  legend: 'Bulan',
                  legendPosition: 'middle',
                  legendOffset: 32
                }}
                axisLeft={{
                  tickSize: 5,
                  tickPadding: 5,
                  tickRotation: 0,
                  legend: 'Nilai (Rp)',
                  legendPosition: 'middle',
                  legendOffset: -40,
                  format: (value: number) => toCurrency(value)
                }}
                labelSkipWidth={12}
                labelSkipHeight={12}
                legends={[
                  {
                    dataFrom: 'keys',
                    anchor: 'bottom',
                    direction: 'row',
                    justify: false,
                    translateX: 0,
                    translateY: 50,
                    itemsSpacing: 20,
                    itemWidth: 120,
                    itemHeight: 20,
                    itemDirection: 'left-to-right',
                    itemOpacity: 0.85,
                    symbolSize: 20,
                    effects: [
                      {
                        on: 'hover',
                        style: {
                          itemOpacity: 1
                        }
                      }
                    ]
                  }
                ]}
                animate={true}
                tooltip={({ id, value }: any) => (
                  <div
                    style={{
                      padding: 12,
                      color: 'white',
                      background: '#333',
                      borderRadius: '4px'
                    }}
                  >
                    <strong>{id}:</strong> {toCurrency(value)}
                  </div>
                )}
              />
            </div>
          </CardContent>
        </Card>
      </CardContent>
    </Card>
  )
}

export default NetAssetGraph
