import { Breadcrumbs, <PERSON>ton, Grid, Typography } from '@mui/material'
import { Link } from 'react-router-dom'
import NetAssetGraph from './components/NetAssetGraph'
import { useProfitLoss } from '../context/ProfitLossContext'
import { format } from 'date-fns'
import { id } from 'date-fns/locale'
import { useCashFlowContext } from '../context/CashFlowContext'
import { useRevenueExpenseContext } from '../context/RevenueExpenseContext'
import { useNetAsset } from '../context/NetAssetContext'

const NetAssetReport = () => {
  const { filter } = useNetAsset()

  const { startMonth, startYear, endMonth, endYear } = filter

  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Breadcrumbs>
          <Link to='#' replace>
            <Typography color='var(--color-text-disabled)'><PERSON><PERSON><PERSON></Typography>
          </Link>
          <Link to='/report/finance' replace>
            <Typography color='var(--color-text-disabled)'><PERSON><PERSON>an</Typography>
          </Link>
          <Typography>Detil Laporan</Typography>
        </Breadcrumbs>
      </Grid>
      <Grid item xs={12}>
        <div className='flex justify-between items-end'>
          <div className='flex flex-col'>
            <Typography variant='h4'>Detil Grafik Harta Bersih</Typography>
            <Typography>
              {startMonth}/{startYear} - {endMonth}/{endYear}
            </Typography>
          </div>
          <div className='flex gap-2'>
            <Button
              color='secondary'
              variant='outlined'
              startIcon={<i className='ri-upload-2-line' />}
              className='is-full sm:is-auto'
            >
              Ekspor
            </Button>
            <Button
              variant='contained'
              startIcon={<i className='ic-outline-local-printshop size-5' />}
              className='is-full sm:is-auto'
            >
              Cetak
            </Button>
          </div>
        </div>
      </Grid>
      <Grid item xs={12}>
        <NetAssetGraph />
      </Grid>
    </Grid>
  )
}

export default NetAssetReport
