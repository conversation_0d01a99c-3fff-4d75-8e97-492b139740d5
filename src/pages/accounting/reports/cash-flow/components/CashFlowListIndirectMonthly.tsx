import { Box, Card, CardActions, CardContent, CardHeader, Divider, Typography } from '@mui/material'
import { useProfitLoss } from '../../context/ProfitLossContext'
import { cashFlowReportOptions, profitAndLossReportOptions } from '../../finance/components/dialogs/utils'
import { format } from 'date-fns'
import { id } from 'date-fns/locale'
import { toCurrency } from '@/utils/helper'
import { useCashFlowContext } from '../../context/CashFlowContext'
import React from 'react'

const CashFlowIndirectMonthlyList = () => {
  const { site, reportType, allSearchParams } = useCashFlowContext()

  type PlRow = {
    variant: 'section' | 'label' | 'item' | 'subtotal' | 'total'
    noAkun?: string
    label: string
    amounts?: (number | null)[]
    indent?: number
  }

  // Data laporan arus kas dengan array amounts
  const cashFlowData: PlRow[] = [
    { variant: 'section', label: 'Aktivitas Operasi' },
    {
      variant: 'item',
      noAkun: 'Laba Bersih',
      label: '(<PERSON>i Laporan Laba Rugi)',
      indent: 0,
      amounts: [10000000000, 10000000000]
    },
    { variant: 'section', label: 'Tambah', indent: 0 },
    { variant: 'section', label: 'Akumulasi Penyusutan', indent: 1 },
    {
      variant: 'item',
      noAkun: '1720.1',
      label: 'Akumulasi Penyusutan Kendaraan Proyek',
      amounts: [1000000000, 1000000000],
      indent: 2
    },
    {
      variant: 'item',
      noAkun: '1720.2',
      label: 'Akumulasi Penyusutan Alat-Alat Berat',
      amounts: [1000000000, 1000000000],
      indent: 2
    },
    {
      variant: 'item',
      noAkun: '1720.3',
      label: 'Akumulasi Penyusutan Mesin & Peralatan',
      amounts: [1000000000, 1000000000],
      indent: 2
    },
    {
      variant: 'item',
      noAkun: '1720.4',
      label: 'Akumulasi Penyusutan Inventaris Proyek',
      amounts: [1000000000, 1000000000],
      indent: 2
    },
    {
      variant: 'item',
      noAkun: '1720.5',
      label: 'Akumulasi Penyusutan Inventaris Kantor',
      amounts: [1000000000, 1000000000],
      indent: 2
    },
    { variant: 'subtotal', label: 'Total Akumulasi Penyusutan', amounts: [5000000000, 5000000000] },
    {
      variant: 'total',
      label: 'Total dari Arus Kas Bersih yang digunakan (dipakai) di periode ini',
      amounts: [5000000000, 5000000000]
    },
    { variant: 'total', label: 'Kas & Setara Kas pada Awal periode', amounts: [1000000000, 1000000000] }
  ]

  const renderRow = (row: PlRow, idx: number) => {
    const ml = row.indent ? row.indent * 16 : 0

    // Render kolom amounts secara dinamis
    const renderAmounts = () =>
      row.amounts?.map((amount, amountIdx) => (
        <Typography key={`amount-${idx}-${amountIdx}`} align='right'>
          {amount !== null && amount !== undefined ? toCurrency(amount) : '-'}
        </Typography>
      ))

    switch (row.variant) {
      case 'section':
        return (
          <div key={idx} className={`col-span-4 mt-2`}>
            <Typography className='font-semibold'>{row.label}</Typography>
          </div>
        )
      case 'label':
        return (
          <React.Fragment key={idx}>
            <div />
            <Typography className='font-semibold' style={{ marginLeft: ml }}>
              {row.label}
            </Typography>
            {renderAmounts()}
          </React.Fragment>
        )
      case 'item':
        return (
          <React.Fragment key={idx}>
            <Typography style={{ marginLeft: ml }}>{row.noAkun}</Typography>
            <Typography>{row.label}</Typography>
            {renderAmounts()}
          </React.Fragment>
        )
      case 'subtotal':
        return (
          <React.Fragment key={idx}>
            <div className={`col-start-3 col-span-2`}>
              <Divider variant='inset' />
            </div>
            <div className='col-span-2'>
              <Typography className='font-semibold'>{row.label}</Typography>
            </div>
            {row.amounts?.map((amount, amountIdx) => (
              <Typography key={`subtotal-amount-${idx}-${amountIdx}`} align='right' className='font-semibold'>
                {amount !== null && amount !== undefined ? toCurrency(amount) : '-'}
              </Typography>
            ))}
          </React.Fragment>
        )
      case 'total':
        return (
          <React.Fragment key={idx}>
            <div className={`col-start-3 col-span-2`}>
              <Divider variant='inset' />
            </div>
            <div className='col-span-2'>
              <Typography className='font-semibold'>{row.label}</Typography>
            </div>
            {renderAmounts()}
          </React.Fragment>
        )
      default:
        return null
    }
  }

  return (
    <Card>
      <CardContent className='flex flex-col gap-5 py-8'>
        <div className='flex flex-col items-center justify-center'>
          <Typography variant='h5' color='primary'>
            PT Equalindo Makmur Alam Sejahtera
          </Typography>
          <Typography variant='h4'>
            Laporan Arus Kas ({cashFlowReportOptions?.find(option => option.value === reportType)?.label})
          </Typography>
          <Typography variant='caption' className='font-semibold text-textPrimary'>
            {allSearchParams?.startMonth}/{allSearchParams?.startYear} - {allSearchParams?.endMonth}/
            {allSearchParams?.endYear}
          </Typography>
          <Typography className='font-semibold'>Lokasi: {site?.name}</Typography>
        </div>
        <Card className='mx-10'>
          <CardHeader
            action={
              <Typography align='right' className='font-normal text-textPrimary text-sm'>
                {/* {format(startDate, 'dd/MM/yyyy', { locale: id })} - {format(endDate, 'dd/MM/yyyy', { locale: id })} */}
              </Typography>
            }
            title={
              <Box className='grid grid-cols-4'>
                <Typography className='font-normal text-textPrimary text-base'>Deskripsi</Typography>
                <Typography align='left' className='font-normal text-textPrimary text-sm'>
                  Tipe Akun
                </Typography>
                {[
                  `${allSearchParams?.startMonth}/${allSearchParams?.startYear}`,
                  `${allSearchParams?.endMonth}/${allSearchParams?.endYear}`
                ].map((period, idx) => (
                  <Typography key={idx} align='right' className='font-normal text-textPrimary text-sm'>
                    {period}
                  </Typography>
                ))}
              </Box>
            }
            className='bg-[#DBF7E8]'
          />
          <CardContent className='grid grid-cols-4 gap-2 p-4 mt-4'>
            {cashFlowData.map((row, idx) => renderRow(row, idx))}
          </CardContent>
          <CardActions className='p-0'>
            <div className='grid grid-cols-4 w-full h-full p-4 bg-[#DBF7E8]'>
              <Typography variant='h6' className='col-span-2'>
                Kas & Setara Kas pada Akhir Periode
              </Typography>
              <Typography variant='h6' align='right' color='primary'>
                {toCurrency(5000000000)}
              </Typography>
              <Typography variant='h6' align='right' color='primary'>
                {toCurrency(5000000000)}
              </Typography>
            </div>
          </CardActions>
        </Card>
      </CardContent>
    </Card>
  )
}

export default CashFlowIndirectMonthlyList
