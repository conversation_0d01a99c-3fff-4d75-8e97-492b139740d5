import { Box, Card, CardActions, CardContent, CardHeader, Divider, Typography } from '@mui/material'
import { useProfitLoss } from '../../context/ProfitLossContext'
import { cashFlowReportOptions, profitAndLossReportOptions } from '../../finance/components/dialogs/utils'
import { format } from 'date-fns'
import { id } from 'date-fns/locale'
import { isNullOrUndefined, toCurrency } from '@/utils/helper'
import { useCashFlowContext } from '../../context/CashFlowContext'

const CashFlowListMonthly = () => {
  const { site, reportType, allSearchParams } = useCashFlowContext()

  type RowVariant = 'section' | 'group' | 'item' | 'subtotal' | 'total' | 'group-total' | 'summary'
  type PLRow = {
    variant: RowVariant
    label: string
    amount?: number[]
    indent?: number
    color?: 'default' | 'error'
  }

  // Template rows; replace amounts/descriptions with BE data
  const rows: PLRow[] = [
    { variant: 'section', label: 'Arus Kas dari Aktivitas Operasi' },

    { variant: 'item', label: 'Kas da<PERSON>', amount: [1000000000, 1000000000], indent: 1 },

    { variant: 'group', label: 'Pendapatan Diluar Usaha', indent: 1 },
    { variant: 'item', label: 'Pendapatan Jasa Giro', amount: [2500000000, 2500000000], indent: 2 },
    { variant: 'item', label: 'Pendapatan Bunga Deposito', amount: [2500000000, 2500000000], indent: 2 },
    { variant: 'item', label: 'Laba Selisih Kurs', amount: [2500000000, 2500000000], indent: 2 },
    { variant: 'item', label: 'Pendapatan Lain-Lain', amount: [2500000000, 2500000000], indent: 2 },
    { variant: 'group-total', label: 'Pendapatan Diluar Usaha', amount: [10000000000, 10000000000], indent: 1 },

    { variant: 'item', label: 'Kas dari Pembelian', amount: [-6000000000, 6000000000], indent: 1 },

    { variant: 'group', label: 'Biaya Pemakaian Bahan', indent: 1 },
    { variant: 'item', label: 'Biaya Pemakaian Suku Cadang', amount: [-250000000, 250000000], indent: 2 },
    { variant: 'item', label: 'Biaya Pemakaian BBM/Pelumas', amount: [-250000000, 250000000], indent: 2 },
    { variant: 'item', label: 'Biaya Pemakaian Material', amount: [-250000000, 250000000], indent: 2 },
    { variant: 'item', label: 'Biaya Pemakaian Lainnya', amount: [-250000000, 250000000], indent: 2 },
    { variant: 'group-total', label: 'Biaya Pemakaian Bahan', amount: [-1000000000, 1000000000], indent: 1 },

    {
      variant: 'summary',
      label: 'Kas bersih dihasilkan oleh/(dipakai) di Period ini',
      amount: [4000000000, 4000000000]
    },
    { variant: 'summary', label: 'Kas & Setara Kas pada Awal Periode', amount: [1000000000, 1000000000] }
  ]

  const renderRow = (row: PLRow, idx: number) => {
    const ml = row.indent ? row.indent * 16 : 0

    switch (row.variant) {
      case 'section':
        return (
          <div key={idx} className='col-span-3 mt-2'>
            <Typography className='font-semibold'>{row.label}</Typography>
          </div>
        )
      case 'group':
        return (
          <div key={idx} className='col-span-3'>
            <Typography className='font-semibold' style={{ marginLeft: ml }}>
              {row.label}
            </Typography>
          </div>
        )
      case 'item':
        return (
          <>
            <Typography key={`l-${idx}`} style={{ marginLeft: ml }}>
              {row.label}
            </Typography>
            {row.amount.map(amount => (
              <Typography key={`r-${idx}`} align='right' className={amount && amount < 0 ? 'text-error' : ''}>
                {!isNullOrUndefined(amount) ? toCurrency(amount) : '-'}
              </Typography>
            ))}
          </>
        )
      case 'group-total':
        return (
          <>
            <Typography key={`lgt-${idx}`} className='font-semibold' style={{ marginLeft: ml }}>
              {row.label}
            </Typography>
            {row.amount.map(amount => (
              <Typography
                key={`rgt-${idx}`}
                align='right'
                className={`font-semibold ${amount && amount < 0 ? 'text-error' : ''}`}
              >
                {!isNullOrUndefined(amount) ? toCurrency(amount) : '-'}
              </Typography>
            ))}
          </>
        )
      case 'summary':
        return (
          <>
            <div key={`d-${idx}`} className='col-span-3'>
              <Divider variant='inset' />
            </div>
            <Typography key={`ls-${idx}`}>{row.label}</Typography>
            {row.amount.map(amount => (
              <Typography key={`rs-${idx}`} align='right' className={amount && amount < 0 ? 'text-error' : ''}>
                {!isNullOrUndefined(amount) ? toCurrency(amount) : '-'}
              </Typography>
            ))}
          </>
        )
      case 'total':
        return (
          <>
            <div key={`d2-${idx}`} className='col-span-3'>
              <Divider variant='inset' />
            </div>
            <Typography key={`lt-${idx}`} className='font-semibold'>
              {row.label}
            </Typography>
            {row.amount.map(amount => (
              <Typography
                key={`rt-${idx}`}
                align='right'
                className={`font-semibold ${amount && amount < 0 ? 'text-error' : ''}`}
              >
                {!isNullOrUndefined(amount) ? toCurrency(amount) : '-'}
              </Typography>
            ))}
          </>
        )
      default:
        return null
    }
  }

  const monthsHeader = [
    `${allSearchParams?.startMonth}/${allSearchParams?.startYear}`,
    `${allSearchParams?.endMonth}/${allSearchParams?.endYear}`
  ]

  return (
    <Card>
      <CardContent className='flex flex-col gap-5 py-8'>
        <div className='flex flex-col items-center justify-center'>
          <Typography variant='h5' color='primary'>
            PT Equalindo Makmur Alam Sejahtera
          </Typography>
          <Typography variant='h4'>
            Laporan Arus Kas ({cashFlowReportOptions?.find(option => option.value === reportType)?.label})
          </Typography>
          <Typography variant='caption' className='font-semibold text-textPrimary'>
            {allSearchParams?.startMonth}/{allSearchParams?.startYear} - {allSearchParams?.endMonth}/
            {allSearchParams?.endYear}
          </Typography>
          <Typography className='font-semibold'>Lokasi: {site?.name}</Typography>
        </div>
        <Card className='mx-10'>
          <CardHeader
            action={
              <Typography align='right' className='font-normal text-textPrimary text-sm'>
                {/* {format(startDate, 'dd/MM/yyyy', { locale: id })} - {format(endDate, 'dd/MM/yyyy', { locale: id })} */}
              </Typography>
            }
            title={
              <Box className='grid grid-cols-3'>
                <Typography className='font-normal text-textPrimary text-base'>Deskripsi</Typography>
                {monthsHeader.map((month, idx) => (
                  <Typography align='right' className='font-normal text-textPrimary text-sm' key={idx}>
                    {month}
                  </Typography>
                ))}
              </Box>
            }
            className='bg-[#DBF7E8]'
          />
          <CardContent className='grid grid-cols-3 gap-2 p-4 mt-4'>
            {rows.map((row, idx) => renderRow(row, idx))}
          </CardContent>
          <CardActions className='p-0'>
            <div className='grid grid-cols-3 w-full h-full p-4 bg-[#DBF7E8]'>
              <Typography variant='h6'>Kas & Setara Kas pada Akhir Periode</Typography>
              <Typography variant='h6' align='right' color='primary'>
                {toCurrency(5000000000)}
              </Typography>
              <Typography variant='h6' align='right' color='primary'>
                {toCurrency(5000000000)}
              </Typography>
            </div>
          </CardActions>
        </Card>
      </CardContent>
    </Card>
  )
}

export default CashFlowListMonthly
