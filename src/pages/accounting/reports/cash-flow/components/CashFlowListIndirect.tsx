import { Box, Card, CardActions, CardContent, CardHeader, Divider, Typography } from '@mui/material'
import { useProfitLoss } from '../../context/ProfitLossContext'
import { cashFlowReportOptions, profitAndLossReportOptions } from '../../finance/components/dialogs/utils'
import { format } from 'date-fns'
import { id } from 'date-fns/locale'
import { toCurrency } from '@/utils/helper'
import { useCashFlowContext } from '../../context/CashFlowContext'
import React from 'react'

const CashFlowIndirectList = () => {
  const { site, reportType, allSearchParams } = useCashFlowContext()

  type RowVariant = 'section' | 'group' | 'item' | 'subtotal' | 'total' | 'group-total' | 'summary'
  type PLRow = {
    variant: 'section' | 'item' | 'summary' | 'total'
    label: string
    status?: string
    amount?: number | null
    indent?: number
  }

  // Template rows; replace amounts/descriptions with BE data
  const cashFlowData: PLRow[] = [
    { variant: 'section', label: 'Aktivitas Operasi' },
    { variant: 'item', status: 'Tambah', label: 'Laba Bersih', amount: 10000000000, indent: 0 },
    { variant: 'item', status: 'Tambah', label: 'Akun Piutang', amount: 2000000000, indent: 0 },
    { variant: 'item', status: 'Tambah', label: 'Persediaan', amount: 2000000000, indent: 0 },
    { variant: 'item', status: 'Tambah', label: 'Aktiva Lancar Lainnya', amount: 2000000000, indent: 0 },
    { variant: 'item', status: 'Tambah', label: 'Akumulasi Penyusutan', amount: 2000000000, indent: 0 },
    { variant: 'item', status: 'Tambah', label: 'Akun Hutang', amount: 0, indent: 0 },
    { variant: 'item', status: 'Tambah', label: 'Hutang Lancar Lainnya', amount: 2000000000, indent: 0 },
    { variant: 'item', status: 'Kurang', label: 'Akun Piutang', amount: -250000000, indent: 0 },
    { variant: 'item', status: 'Kurang', label: 'Aktiva Lancar Lainnya', amount: -250000000, indent: 0 },
    { variant: 'item', status: 'Kurang', label: 'Akumulasi Penyusutan', amount: 0, indent: 0 },
    { variant: 'item', status: 'Kurang', label: 'Akun Hutang', amount: -250000000, indent: 0 },
    { variant: 'item', status: 'Kurang', label: 'Hutang Lancar Lainnya', amount: -250000000, indent: 0 },

    // Baris ringkasan
    { variant: 'summary', label: 'Total dari Aktivitas Operasi', amount: 19000000000 },
    {
      variant: 'summary',
      label: 'Total dari Arus Kas Bersih yang digunakan (dipakai) di periode ini',
      amount: 5000000000
    },
    { variant: 'summary', label: 'Kas & Setara Kas pada Awal periode', amount: 1000000000 }
  ]

  const finalTotal = {
    label: 'Kas & Setara Kas pada Akhir Periode',
    amount: 6000000000
  }

  const renderRow = (row: PLRow, idx: number) => {
    const amountText = row.amount !== null && row.amount !== undefined ? toCurrency(row.amount) : '-'
    const amountColorClass = row.amount && row.amount < 0 ? 'text-red-600' : ''

    switch (row.variant) {
      case 'section':
        return (
          <div key={idx} className='col-span-3 mt-2'>
            <Typography className='font-semibold'>{row.label}</Typography>
          </div>
        )
      case 'item':
        return (
          <React.Fragment key={idx}>
            <Typography>{row.status}</Typography>
            <Typography>{row.label}</Typography>
            <Typography align='right' className={amountColorClass}>
              {amountText}
            </Typography>
          </React.Fragment>
        )
      case 'summary':
        return (
          <React.Fragment key={idx}>
            <div className='col-start-3'>
              <Divider variant='inset' />
            </div>
            <div className='col-span-2'>
              <Typography>{row.label}</Typography>
            </div>
            <Typography align='right'>{amountText}</Typography>
          </React.Fragment>
        )
      default:
        return null
    }
  }

  return (
    <Card>
      <CardContent className='flex flex-col gap-5 py-8'>
        <div className='flex flex-col items-center justify-center'>
          <Typography variant='h5' color='primary'>
            PT Equalindo Makmur Alam Sejahtera
          </Typography>
          <Typography variant='h4'>
            Laporan Arus Kas ({cashFlowReportOptions?.find(option => option.value === reportType)?.label})
          </Typography>
          <Typography variant='caption' className='font-semibold text-textPrimary'>
            {allSearchParams?.startMonth}/{allSearchParams?.startYear} - {allSearchParams?.endMonth}/
            {allSearchParams?.endYear}
          </Typography>
          <Typography className='font-semibold'>Lokasi: {site?.name}</Typography>
        </div>
        <Card className='mx-20'>
          <CardHeader
            action={
              <Typography align='right' className='font-normal text-textPrimary text-sm'>
                {/* {format(startDate, 'dd/MM/yyyy', { locale: id })} - {format(endDate, 'dd/MM/yyyy', { locale: id })} */}
              </Typography>
            }
            title={
              <Box className='grid grid-cols-3'>
                <Typography className='font-normal text-textPrimary text-base'>Deskripsi</Typography>
                <Typography align='left' className='font-normal text-textPrimary text-sm'>
                  Tipe Akun
                </Typography>
                <Typography align='right' className='font-normal text-textPrimary text-sm'>
                  Saldo
                </Typography>
              </Box>
            }
            className='bg-[#DBF7E8]'
          />
          <CardContent className='grid grid-cols-3 gap-2 p-4 mt-4'>
            {cashFlowData.map((row, idx) => renderRow(row, idx))}
          </CardContent>
          <CardActions className='p-0'>
            <div className='flex justify-between w-full h-full p-4 bg-[#F8DDDF]'>
              <Typography variant='h6'>Kas & Setara Kas pada Akhir Periode</Typography>
              <Typography variant='h6' color='error'>
                {toCurrency(5000000000)}
              </Typography>
            </div>
          </CardActions>
        </Card>
      </CardContent>
    </Card>
  )
}

export default CashFlowIndirectList
