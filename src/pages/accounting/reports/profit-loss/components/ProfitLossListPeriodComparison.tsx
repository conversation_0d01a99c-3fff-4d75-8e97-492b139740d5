import React from 'react'
import { <PERSON>, CardA<PERSON>, Card<PERSON>ontent, CardHeader, Divider, Typography, Box } from '@mui/material'
import { toCurrency } from '@/utils/helper' // Assuming this utility function exists
import { useProfitLoss } from '../../context/ProfitLossContext'
import { profitAndLossReportOptions } from '../../finance/components/dialogs/utils'
import { format } from 'date-fns'

// Tipe data untuk setiap baris laporan
type RowVariant = 'section' | 'group' | 'item' | 'subtotal' | 'total'
type PLRow = {
  variant: RowVariant
  label: string
  amounts?: (number | null)[]
  indent?: number
  periods?: number[]
  selisih?: number
  persentaseSelisih?: number
}

// Data laporan multi-periode
const initialReportData: PLRow[] = [
  {
    variant: 'section',
    label: 'Pendapatan',
    periods: [],
    selisih: null,
    persentaseSelisih: null
  },
  {
    variant: 'item',
    label: 'Jasa Penambangan/Sewa Alat',
    periods: [50000000000, 50000000000],
    selisih: 0,
    persentaseSelisih: 0,
    indent: 1
  },
  {
    variant: 'subtotal',
    label: 'Jumlah Pendapatan',
    periods: [50000000000, 50000000000],
    selisih: 0,
    persentaseSelisih: 0
  },
  {
    variant: 'section',
    label: 'Harga Pokok Penjualan',
    periods: [],
    selisih: null,
    persentaseSelisih: null
  },
  {
    variant: 'item',
    label: 'Biaya Pemakaian Suku Cadang',
    periods: [10000000000, 10000000000],
    selisih: 0,
    persentaseSelisih: 0,
    indent: 2
  }
]

const finalData = [-2000000, -2000000, 0, 0]

const ProfitLossPeriodComparison = ({ reportData = initialReportData }) => {
  const {
    site,
    filter: { reportType },
    allSearchParams
  } = useProfitLoss()

  const periods = [
    `${format(allSearchParams?.startDateEarly, 'dd/MM/yyyy')} - ${format(allSearchParams?.endDateEarly, 'dd/MM/yyyy')}`,
    `${format(allSearchParams?.startDateLate, 'dd/MM/yyyy')} - ${format(allSearchParams?.endDateLate, 'dd/MM/yyyy')}`
  ]

  const renderRow = (row: PLRow, idx: number) => {
    const ml = row.indent ? row.indent * 16 : 0

    // Warna untuk nilai positif (hijau) dan negatif (merah)
    const getAmountColor = (amount: number) => (amount >= 0 ? 'text-green-600' : 'text-red-600')

    switch (row.variant) {
      case 'section':
        return (
          <div key={idx} className='col-span-5 mt-2'>
            <Typography className='font-semibold'>{row.label}</Typography>
          </div>
        )
      case 'item':
        return (
          <React.Fragment key={idx}>
            <Typography style={{ marginLeft: ml }}>{row.label}</Typography>
            {row.periods.map((amount, pIdx) => (
              <Typography key={`p-${idx}-${pIdx}`} align='right'>
                {amount !== null ? toCurrency(amount) : '-'}
              </Typography>
            ))}
            <Typography align='right' className={getAmountColor(row.selisih)}>
              {row.selisih !== null ? toCurrency(row.selisih) : '-'}
            </Typography>
            <Typography align='right' className={getAmountColor(row.persentaseSelisih)}>
              {row.persentaseSelisih !== null ? `${row.persentaseSelisih}%` : '-'}
            </Typography>
          </React.Fragment>
        )
      case 'subtotal':
        return (
          <React.Fragment key={idx}>
            <div className='col-start-2 col-span-4'>
              <Divider variant='inset' />
            </div>
            <Typography className='font-semibold'>{row.label}</Typography>
            {row.periods.map((amount, pIdx) => (
              <Typography key={`p-${idx}-${pIdx}`} align='right' className='font-semibold'>
                {amount !== null ? toCurrency(amount) : '-'}
              </Typography>
            ))}
            <Typography align='right' className='font-semibold'>
              {row.selisih !== null ? toCurrency(row.selisih) : '-'}
            </Typography>
            <Typography align='right' className='font-semibold'>
              {row.persentaseSelisih !== null ? `${row.persentaseSelisih}%` : '-'}
            </Typography>
          </React.Fragment>
        )
      case 'total':
        const amountColor = getAmountColor(row.periods[0])
        return (
          <React.Fragment key={idx}>
            <div className='col-start-2 col-span-4'>
              <Divider variant='inset' />
            </div>
            <Typography className={`font-semibold ${amountColor}`}>{row.label}</Typography>
            {row.periods.map((amount, pIdx) => (
              <Typography key={`p-${idx}-${pIdx}`} align='right' className={`font-semibold ${amountColor}`}>
                {amount !== null ? toCurrency(amount) : '-'}
              </Typography>
            ))}
            <Typography align='right' className={`font-semibold ${amountColor}`}>
              {row.selisih !== null ? toCurrency(row.selisih) : '-'}
            </Typography>
            <Typography align='right' className={`font-semibold ${amountColor}`}>
              {row.persentaseSelisih !== null ? `${row.persentaseSelisih}%` : '-'}
            </Typography>
          </React.Fragment>
        )
      default:
        return null
    }
  }

  return (
    <Card>
      <CardContent className='flex flex-col gap-5 py-8'>
        {/* Header laporan */}
        <div className='flex flex-col items-center justify-center'>
          <Typography variant='h5' color='primary'>
            PT Equalindo Makmur Alam Sejahtera
          </Typography>
          <Typography variant='h4'>
            Laporan Laba Rugi ({profitAndLossReportOptions?.find(option => option.value === reportType)?.label})
          </Typography>
          <Typography variant='caption' className='font-semibold text-textPrimary'>
            {`${format(allSearchParams?.startDateEarly, 'dd/MM/yyyy')} - ${format(allSearchParams?.endDateEarly, 'dd/MM/yyyy')} dan ${format(allSearchParams?.startDateLate, 'dd/MM/yyyy')} - ${format(allSearchParams?.endDateLate, 'dd/MM/yyyy')}`}
          </Typography>
          <Typography className='font-semibold'>Lokasi: {site?.name}</Typography>
        </div>
        <Card className='mx-18'>
          {/* CardHeader untuk deskripsi dan periode */}
          <CardHeader
            className='bg-[#DBF7E8]'
            title={
              <Box className='grid grid-cols-5 gap-2'>
                <Typography className='font-normal text-textPrimary text-base'>Deskripsi</Typography>
                {periods.map((period, idx) => (
                  <Typography key={idx} align='right' className='font-normal text-textPrimary text-sm'>
                    {period}
                  </Typography>
                ))}
                <Typography align='right' className='font-normal text-textPrimary text-sm'>
                  Selisih
                </Typography>
                <Typography align='right' className='font-normal text-textPrimary text-sm'>
                  % Selisih
                </Typography>
              </Box>
            }
          />
          {/* CardContent untuk isi laporan */}
          <CardContent className='grid grid-cols-5 gap-2 p-4 mt-4'>
            {reportData.map((row, idx) => renderRow(row, idx))}
          </CardContent>
          {/* CardActions untuk laba bersih */}
          <CardActions className='p-0'>
            <div className='grid grid-cols-5 w-full h-full p-4 bg-[#F8DDDF]'>
              <Typography variant='h6'>Laba/Rugi Bersih (After Tax)</Typography>
              {finalData.map((amount, idx) => (
                <Typography key={idx} variant='h6' color='error' align='right' className='whitespace-nowrap'>
                  {toCurrency(amount)}
                </Typography>
              ))}
            </div>
          </CardActions>
        </Card>
      </CardContent>
    </Card>
  )
}

export default ProfitLossPeriodComparison
