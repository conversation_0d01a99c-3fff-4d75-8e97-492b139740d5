import React from 'react'
import { <PERSON>, <PERSON>A<PERSON>, CardContent, Card<PERSON>eader, Divider, Typography, Box } from '@mui/material'
import { toCurrency } from '@/utils/helper' // Assuming this utility function exists
import { profitAndLossReportOptions } from '../../finance/components/dialogs/utils'
import { useProfitLoss } from '../../context/ProfitLossContext'
import { format } from 'date-fns'

// Tipe data untuk setiap baris laporan
type RowVariant = 'section' | 'group' | 'item' | 'subtotal' | 'total'
type PLRow = {
  variant: RowVariant
  label: string
  jurnalAmount?: number
  jumlahAmount?: number
  indent?: number
}

// Data laporan multi-periode
const initialReportData: PLRow[] = [
  {
    variant: 'section',
    label: 'Pendapatan'
  },
  {
    variant: 'item',
    label: 'Jasa Penambangan/Sewa Alat',
    jurnalAmount: 50000000000,
    jumlahAmount: 50000000000,
    indent: 1
  },
  {
    variant: 'subtotal',
    label: 'Ju<PERSON><PERSON> Pen<PERSON>',
    jurnalAmount: 50000000000,
    jumlahAmount: 50000000000
  },
  {
    variant: 'section',
    label: 'Harga Pokok Penjualan'
  },
  {
    variant: 'group',
    label: 'Biaya Pemakaian Bahan',
    jurnalAmount: 35000000000,
    jumlahAmount: 35000000000,
    indent: 1
  },
  {
    variant: 'item',
    label: 'Biaya Pemakaian Suku Cadang',
    jurnalAmount: 10000000000,
    jumlahAmount: 10000000000,
    indent: 2
  },
  {
    variant: 'item',
    label: 'Biaya Pemakaian Material',
    jurnalAmount: 10000000000,
    jumlahAmount: 10000000000,
    indent: 2
  },
  {
    variant: 'item',
    label: 'Biaya Pemakaian Lain-Lain',
    jurnalAmount: 15000000000,
    jumlahAmount: 15000000000,
    indent: 2
  },
  {
    variant: 'group',
    label: 'Lorem Ipsum Dolor',
    jurnalAmount: 35000000000,
    jumlahAmount: 35000000000,
    indent: 1
  },
  {
    variant: 'item',
    label: 'Sit Amet Consectetur Adipiscing',
    jurnalAmount: 10000000000,
    jumlahAmount: 10000000000,
    indent: 2
  },
  {
    variant: 'item',
    label: 'Elit Sed Do Eiusmod',
    jurnalAmount: 25000000000,
    jumlahAmount: 25000000000,
    indent: 2
  },
  {
    variant: 'subtotal',
    label: 'Jumlah Harga Pokok Penjualan',
    jurnalAmount: 70000000000,
    jumlahAmount: 70000000000
  },
  {
    variant: 'total',
    label: 'Laba/Rugi Kotor',
    jurnalAmount: -20000000000,
    jumlahAmount: -20000000000
  }
]

const periods = ['Januari 2025', 'Februari 2025', 'Maret 2025']
const finalProfit = [-2000000, -2000000, -2000000]

const ProfitLossConsolidation = ({ reportData = initialReportData }) => {
  const {
    site,
    filter: { reportType },
    allSearchParams
  } = useProfitLoss()

  let finalProfitJurnal = -2000000
  let finalProfitJumlah = -2000000

  const renderRow = (row: PLRow, idx: number) => {
    const ml = row.indent ? row.indent * 16 : 0
    const amountColorClass = amount => (amount < 0 ? 'text-red-600' : 'text-gray-900')

    switch (row.variant) {
      case 'section':
        return (
          <div key={idx} className='col-span-3 mt-2'>
            <Typography className='font-semibold'>{row.label}</Typography>
          </div>
        )
      case 'group':
        return (
          <div key={idx} className='col-span-3'>
            <Typography className='font-semibold' style={{ marginLeft: ml }}>
              {row.label}
            </Typography>
          </div>
        )
      case 'item':
        return (
          <React.Fragment key={idx}>
            <Typography style={{ marginLeft: ml }}>{row.label}</Typography>
            <Typography align='right' className={amountColorClass(row.jurnalAmount)}>
              {toCurrency(row.jurnalAmount)}
            </Typography>
            <Typography align='right' className={amountColorClass(row.jumlahAmount)}>
              {toCurrency(row.jumlahAmount)}
            </Typography>
          </React.Fragment>
        )
      case 'subtotal':
        return (
          <React.Fragment key={idx}>
            <div className='col-start-2 col-span-2'>
              <Divider variant='inset' />
            </div>
            <Typography className='font-semibold'>{row.label}</Typography>
            <Typography align='right' className='font-semibold'>
              {toCurrency(row.jurnalAmount)}
            </Typography>
            <Typography align='right' className='font-semibold'>
              {toCurrency(row.jumlahAmount)}
            </Typography>
          </React.Fragment>
        )
      case 'total':
        return (
          <React.Fragment key={idx}>
            <div className='col-start-2 col-span-2'>
              <Divider variant='inset' />
            </div>
            <Typography className={`font-semibold ${amountColorClass(row.jurnalAmount)}`}>{row.label}</Typography>
            <Typography align='right' className={`font-semibold ${amountColorClass(row.jurnalAmount)}`}>
              {toCurrency(row.jurnalAmount)}
            </Typography>
            <Typography align='right' className={`font-semibold ${amountColorClass(row.jumlahAmount)}`}>
              {toCurrency(row.jumlahAmount)}
            </Typography>
          </React.Fragment>
        )
      default:
        return null
    }
  }

  return (
    <Card>
      <CardContent className='flex flex-col gap-5 py-8'>
        {/* Header laporan */}
        <div className='flex flex-col items-center justify-center'>
          <Typography variant='h5' color='primary'>
            PT Equalindo Makmur Alam Sejahtera
          </Typography>
          <Typography variant='h4'>
            Laporan Laba Rugi ({profitAndLossReportOptions?.find(option => option.value === reportType)?.label})
          </Typography>
          <Typography variant='caption' className='font-semibold text-textPrimary'>
            {format(allSearchParams?.date, 'dd/MM/yyyy')}
          </Typography>
          <Typography className='font-semibold'>Lokasi: {site?.name}</Typography>
        </div>
        <Card className='mx-18'>
          {/* CardHeader untuk deskripsi dan periode */}
          <CardHeader
            className='bg-[#DBF7E8]'
            title={
              <Box className='grid grid-cols-3 gap-2'>
                <Typography className='font-normal text-textPrimary text-base'>Deskripsi</Typography>
                <Typography align='right' className='font-normal text-textPrimary text-sm'>
                  JURNAL PERUSAHAAN
                </Typography>
                <Typography align='right' className='font-normal text-textPrimary text-sm'>
                  JUMLAH
                </Typography>
              </Box>
            }
          />
          {/* CardContent untuk isi laporan */}
          <CardContent className='grid grid-cols-3 gap-2 p-4 mt-4'>
            {reportData.map((row, idx) => renderRow(row, idx))}
          </CardContent>
          {/* CardActions untuk laba bersih */}
          <CardActions className='p-0'>
            <div className='grid grid-cols-3 w-full h-full p-4 bg-[#F8DDDF]'>
              <Typography variant='h6'>Laba/Rugi Bersih (After Tax)</Typography>
              <Typography variant='h6' align='right' color='error'>
                {toCurrency(finalProfitJurnal)}
              </Typography>
              <Typography variant='h6' align='right' color='error'>
                {toCurrency(finalProfitJumlah)}
              </Typography>
            </div>
          </CardActions>
        </Card>
      </CardContent>
    </Card>
  )
}

export default ProfitLossConsolidation
