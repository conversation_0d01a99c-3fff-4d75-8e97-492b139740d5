import React from 'react'
import { Page, Text, View, Document, StyleSheet, Font } from '@react-pdf/renderer'
import { JournalProfitLoss } from '@/types/reportTypes'
import { toCurrency } from '@/utils/helper'
import { format } from 'date-fns'
import { id } from 'date-fns/locale'
import { ProfitAndLossReportType } from '../../../finance/config/types'

// Register fonts
// Font.register({
//   family: 'Roboto',
//   fonts: [
//     { src: '/fonts/Roboto-Regular.ttf' },
//     { src: '/fonts/Roboto-Bold.ttf', fontWeight: 'bold' }
//   ]
// })

const styles = StyleSheet.create({
  page: {
    fontFamily: 'Helvetica',
    fontSize: 9,
    paddingTop: 30,
    paddingLeft: 40,
    paddingRight: 40,
    paddingBottom: 30,
    lineHeight: 1.5
  },
  titleContainer: {
    flexDirection: 'column',
    alignItems: 'center',
    marginBottom: 20
  },
  companyName: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#1A452C'
  },
  reportTitle: {
    fontSize: 12,
    fontWeight: 'bold',
    marginTop: 5
  },
  dateRange: {
    fontSize: 10,
    marginTop: 2
  },
  location: {
    fontSize: 10,
    fontWeight: 'bold'
  },
  table: {
    display: 'flex',
    width: 'auto',
    borderStyle: 'solid',
    borderWidth: 1,
    borderColor: '#E0E0E0'
  },
  tableHeader: {
    flexDirection: 'row',
    backgroundColor: '#DBF7E8',
    padding: 4
  },
  tableHeaderCol: {
    width: '50%',
    fontWeight: 'bold'
  },
  tableRow: {
    flexDirection: 'row',
    padding: 4,
    borderBottomColor: '#E0E0E0',
    borderBottomWidth: 1
  },
  tableCol: {
    width: '50%'
  },
  tableCell: {
    fontSize: 9
  },
  tableCellRight: {
    textAlign: 'right'
  },
  section: {
    fontWeight: 'bold',
    marginTop: 8
  },
  group: {
    fontWeight: 'bold'
  },
  subtotalRow: {
    flexDirection: 'row',
    padding: 4,
    borderTopColor: '#E0E0E0',
    borderTopWidth: 1
  },
  totalRow: {
    flexDirection: 'row',
    padding: 4,
    borderTopColor: '#E0E0E0',
    borderTopWidth: 1,
    marginTop: 4
  },
  summaryContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 8,
    backgroundColor: '#F8DDDF',
    marginTop: 10
  },
  summaryLabel: {
    fontSize: 12,
    fontWeight: 'bold'
  },
  summaryAmount: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#D32F2F'
  }
})

type ProfitLossPDFProps = {
  data: JournalProfitLoss | undefined
  filter: {
    startDate?: string
    endDate?: string
    reportType: ProfitAndLossReportType
  }
  site: { name: string } | undefined
}

const ProfitLossPDF = ({ data, filter, site }: ProfitLossPDFProps) => {
  const getReportTypeLabel = (type: ProfitAndLossReportType) => {
    // This should ideally come from a shared utility or config
    if (type === ProfitAndLossReportType.Standard) return 'Standard'
    return type
  }

  const netIncome = data?.summary.netIncome ?? 0

  return (
    <Document>
      <Page size='FOLIO' style={styles.page}>
        <View style={styles.titleContainer}>
          <Text style={styles.companyName}>PT Equalindo Makmur Alam Sejahtera</Text>
          <Text style={styles.reportTitle}>Laporan Laba Rugi ({getReportTypeLabel(filter.reportType)})</Text>
          <Text style={styles.dateRange}>
            {`${format(filter.startDate, 'dd/MM/yyyy', { locale: id })} - ${format(filter.endDate, 'dd/MM/yyyy', {
              locale: id
            })}`}
          </Text>
          <Text style={styles.location}>Lokasi: {site?.name}</Text>
        </View>

        <View style={styles.table}>
          <View style={styles.tableHeader}>
            <Text style={styles.tableHeaderCol}>Deskripsi</Text>
            <Text style={[styles.tableHeaderCol, styles.tableCellRight]}>
              {`${format(filter.startDate, 'dd/MM/yyyy', { locale: id })} - ${format(filter.endDate, 'dd/MM/yyyy', {
                locale: id
              })}`}
            </Text>
          </View>
          {data?.sections.map((section, sectionIdx) => (
            <View key={sectionIdx}>
              <View style={[styles.tableRow, styles.section]}>
                <Text>{section.title}</Text>
              </View>
              {section.subSections.map((subSection, subSectionIdx) => (
                <View key={subSectionIdx}>
                  <View style={[styles.tableRow, styles.group, { paddingLeft: 12 }]}>
                    <Text>{subSection.headerName}</Text>
                  </View>
                  {subSection.accounts.map((account, accountIdx) => (
                    <View key={accountIdx} style={[styles.tableRow, { paddingLeft: 24 }]}>
                      <Text style={styles.tableCol}>{account.isContra ? `(-) ${account.name}` : account.name}</Text>
                      <Text style={[styles.tableCol, styles.tableCellRight]}>{toCurrency(account.amount)}</Text>
                    </View>
                  ))}
                  {subSection.accounts.length > 0 && (
                    <View style={[styles.subtotalRow, { paddingLeft: 12 }]}>
                      <Text style={[styles.tableCol, styles.group]}>Jumlah {subSection.headerName}</Text>
                      <Text style={[styles.tableCol, styles.tableCellRight, styles.group]}>
                        {toCurrency(subSection.subTotal)}
                      </Text>
                    </View>
                  )}
                </View>
              ))}
              <View style={[styles.totalRow, styles.group]}>
                <Text style={styles.tableCol}>Jumlah {section.title}</Text>
                <Text style={[styles.tableCol, styles.tableCellRight]}>{toCurrency(section.subTotal)}</Text>
              </View>
            </View>
          ))}
        </View>

        <View style={styles.summaryContainer}>
          <Text style={styles.summaryLabel}>Laba/Rugi Bersih (After Tax)</Text>
          <Text style={[styles.summaryAmount, { color: netIncome < 0 ? '#D32F2F' : 'black' }]}>
            {toCurrency(netIncome)}
          </Text>
        </View>
      </Page>
    </Document>
  )
}

export default ProfitLossPDF
