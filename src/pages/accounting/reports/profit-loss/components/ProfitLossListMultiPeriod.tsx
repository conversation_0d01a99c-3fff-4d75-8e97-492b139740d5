import React from 'react'
import { Card, CardActions, CardContent, CardHeader, Divider, Typography, Box } from '@mui/material'
import { toCurrency } from '@/utils/helper' // Assuming this utility function exists
import { profitAndLossReportOptions } from '../../finance/components/dialogs/utils'
import { useProfitLoss } from '../../context/ProfitLossContext'

// Tipe data untuk setiap baris laporan
type RowVariant = 'section' | 'group' | 'item' | 'subtotal' | 'total'
type PLRow = {
  variant: RowVariant
  label: string
  amounts?: (number | null)[]
  indent?: number
}

// Data laporan multi-periode
const initialReportData: PLRow[] = [
  { variant: 'section', label: 'Pendapatan' },
  { variant: 'item', label: 'Jasa Penambangan/Sewa Alat', amounts: [50000000000, 50000000000, 50000000000], indent: 1 },
  { variant: 'subtotal', label: 'Ju<PERSON><PERSON>', amounts: [50000000000, 50000000000, 50000000000] },

  { variant: 'section', label: 'Harga Pokok Penjualan' },
  { variant: 'group', label: '<PERSON><PERSON>ya Pemakai<PERSON> Bahan', indent: 1 },
  {
    variant: 'item',
    label: 'Biaya Pemakaian Suku Cadang',
    amounts: [10000000000, 10000000000, 10000000000],
    indent: 2
  },
  { variant: 'item', label: 'Biaya Pemakaian Material', amounts: [10000000000, 10000000000, 10000000000], indent: 2 },
  { variant: 'item', label: 'Biaya Pemakaian Lain-Lain', amounts: [15000000000, 15000000000, 15000000000], indent: 2 },
  { variant: 'group', label: 'Lorem Ipsum Dolor', indent: 1 },
  {
    variant: 'item',
    label: 'Sit Amet Consectetur Adipiscing',
    amounts: [10000000000, 10000000000, 10000000000],
    indent: 2
  },
  { variant: 'item', label: 'Elit Sed Do Eiusmod', amounts: [25000000000, 25000000000, 25000000000], indent: 2 },
  { variant: 'subtotal', label: 'Jumlah Harga Pokok Penjualan', amounts: [70000000000, 70000000000, 70000000000] },

  { variant: 'total', label: 'Laba/Rugi Kotor', amounts: [-20000000000, -20000000000, -20000000000] }
]

const periods = ['Januari 2025', 'Februari 2025', 'Maret 2025']
const finalProfit = [-2000000, -2000000, -2000000]

const ProfitLossMultiPeriod = ({ reportData = initialReportData }) => {
  const {
    site,
    filter: { reportType },
    allSearchParams
  } = useProfitLoss()
  const renderRow = (row: PLRow, idx: number) => {
    const ml = row.indent ? row.indent * 16 : 0

    // Fungsi untuk merender kolom jumlah uang
    const renderAmounts = () =>
      row.amounts?.map((amount, amountIdx) => (
        <Typography key={`amount-${idx}-${amountIdx}`} align='right' className='whitespace-nowrap'>
          {amount !== null && amount !== undefined ? toCurrency(amount) : '-'}
        </Typography>
      ))

    switch (row.variant) {
      case 'section':
        return (
          <div key={idx} className='col-span-4 mt-2'>
            <Typography className='font-semibold'>{row.label}</Typography>
          </div>
        )
      case 'group':
        return (
          <div key={idx} className='col-span-4'>
            <Typography className='font-semibold' style={{ marginLeft: ml }}>
              {row.label}
            </Typography>
          </div>
        )
      case 'item':
        return (
          <>
            <Typography key={`label-${idx}`} style={{ marginLeft: ml }}>
              {row.label}
            </Typography>
            {renderAmounts()}
          </>
        )
      case 'subtotal':
        return (
          <>
            <div key={`divider-${idx}`} className='col-start-2 col-span-3'>
              <Divider variant='inset' />
            </div>
            <Typography key={`label-${idx}`} className='font-semibold'>
              {row.label}
            </Typography>
            {row.amounts?.map((amount, amountIdx) => (
              <Typography key={`amount-${idx}-${amountIdx}`} align='right' className='font-semibold whitespace-nowrap'>
                {amount !== null && amount !== undefined ? toCurrency(amount) : '-'}
              </Typography>
            ))}
          </>
        )
      case 'total':
        return (
          <>
            <div key={`divider-${idx}`} className='col-start-2 col-span-3'>
              <Divider variant='inset' />
            </div>
            <Typography key={`label-${idx}`} className='font-semibold'>
              {row.label}
            </Typography>
            {row.amounts?.map((amount, amountIdx) => (
              <Typography
                key={`amount-${idx}-${amountIdx}`}
                align='right'
                className={`font-semibold whitespace-nowrap ${amount && amount < 0 ? 'text-error' : ''}`}
              >
                {amount !== null && amount !== undefined ? toCurrency(amount) : '-'}
              </Typography>
            ))}
          </>
        )
      default:
        return null
    }
  }

  return (
    <Card>
      <CardContent className='flex flex-col gap-5 py-8'>
        {/* Header laporan */}
        <div className='flex flex-col items-center justify-center'>
          <Typography variant='h5' color='primary'>
            PT Equalindo Makmur Alam Sejahtera
          </Typography>
          <Typography variant='h4'>
            Laporan Laba Rugi ({profitAndLossReportOptions?.find(option => option.value === reportType)?.label})
          </Typography>
          <Typography variant='caption' className='font-semibold text-textPrimary'>
            {`${allSearchParams?.startMonth}/${allSearchParams?.startYear} - ${allSearchParams?.endMonth}/${allSearchParams?.endYear}`}
          </Typography>
          <Typography className='font-semibold'>Lokasi: {site?.name}</Typography>
        </div>
        <Card className='mx-18'>
          {/* CardHeader untuk deskripsi dan periode */}
          <CardHeader
            className='bg-[#DBF7E8]'
            title={
              <Box className='grid grid-cols-4 gap-2'>
                <Typography className='font-normal text-textPrimary text-base'>Deskripsi</Typography>
                {periods.map((period, idx) => (
                  <Typography key={idx} align='right' className='font-normal text-textPrimary text-sm'>
                    {period}
                  </Typography>
                ))}
              </Box>
            }
          />
          {/* CardContent untuk isi laporan */}
          <CardContent className='grid grid-cols-4 gap-2 p-4 mt-4'>
            {reportData.map((row, idx) => renderRow(row, idx))}
          </CardContent>
          {/* CardActions untuk laba bersih */}
          <CardActions className='p-0'>
            <div className='grid grid-cols-4 w-full h-full p-4 bg-[#F8DDDF]'>
              <Typography variant='h6'>Laba/Rugi Bersih (After Tax)</Typography>
              {finalProfit.map((amount, idx) => (
                <Typography key={idx} variant='h6' color='error' align='right' className='whitespace-nowrap'>
                  {toCurrency(amount)}
                </Typography>
              ))}
            </div>
          </CardActions>
        </Card>
      </CardContent>
    </Card>
  )
}

export default ProfitLossMultiPeriod
