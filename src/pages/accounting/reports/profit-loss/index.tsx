import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ton, Grid, Typography } from '@mui/material'
import { Link } from 'react-router-dom'
import ProfitLossList from './components/ProfitLossList'
import { useProfitLoss } from '../context/ProfitLossContext'
import { format } from 'date-fns'
import { id } from 'date-fns/locale'
import { ProfitAndLossReportType } from '../finance/config/types'
import ProfitLossMultiPeriodList from './components/ProfitLossListMultiPeriod'
import ProfitLossPeriodComparison from './components/ProfitLossListPeriodComparison'
import ProfitLossConsolidation from './components/ProfitLossListConsolidation'
import ProfitLossBudgetPeriod from './components/ProfitLossListBudgetPeriod'
import ProfitLossPDF from './components/document-template/ProfitLossPDF'
import { pdf } from '@react-pdf/renderer'
import { saveAs } from 'file-saver'

const ProfitLossReport = () => {
  const {
    filter: { reportType },
    site,
    profitLoss
  } = useProfitLoss()

  const handleExport = async () => {
    const blob = await pdf(
      <ProfitLossPDF data={profitLoss} filter={{ ...profitLoss.params, reportType }} site={site} />
    ).toBlob()
    const filename = `${profitLoss?.params?.startDate}-${profitLoss?.params?.endDate}.pdf`
    saveAs(blob, filename)
  }

  const handlePrint = async () => {
    const blob = await pdf(
      <ProfitLossPDF data={profitLoss} filter={{ ...profitLoss.params, reportType }} site={site} />
    ).toBlob()
    const url = URL.createObjectURL(blob)
    const printWindow = window.open(url, '_blank')
    printWindow.onload = () => {
      printWindow.print()
      printWindow.onafterprint = () => {
        printWindow.close()
      }
    }
  }

  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Breadcrumbs>
          <Link to='#' replace>
            <Typography color='var(--color-text-disabled)'>Laporan</Typography>
          </Link>
          <Link to='/report/finance' replace>
            <Typography color='var(--color-text-disabled)'>Keuangan</Typography>
          </Link>
          <Typography>Detil Laporan</Typography>
        </Breadcrumbs>
      </Grid>
      <Grid item xs={12}>
        <div className='flex justify-between items-end'>
          <div className='flex flex-col'>
            <Typography variant='h4'>Detil Laporan Laba/Rugi</Typography>
            <Typography>
              {/* {format(startDate, 'dd/MM/yyyy', { locale: id })} - {format(endDate, 'dd/MM/yyyy', { locale: id })} */}
            </Typography>
          </div>
          <div className='flex gap-2'>
            <Button
              color='secondary'
              variant='outlined'
              startIcon={<i className='ri-upload-2-line' />}
              onClick={handleExport}
              className='is-full sm:is-auto'
            >
              Ekspor
            </Button>
            <Button
              variant='contained'
              startIcon={<i className='ic-outline-local-printshop size-5' />}
              onClick={handlePrint}
              className='is-full sm:is-auto'
            >
              Cetak
            </Button>
          </div>
        </div>
      </Grid>
      <Grid item xs={12}>
        {{
          [ProfitAndLossReportType.Standard]: <ProfitLossList />,
          [ProfitAndLossReportType.MultiPeriod]: <ProfitLossMultiPeriodList />,
          [ProfitAndLossReportType.PeriodComparison]: <ProfitLossPeriodComparison />,
          [ProfitAndLossReportType.Consolidation]: <ProfitLossConsolidation />,
          [ProfitAndLossReportType.BudgetPeriod]: <ProfitLossBudgetPeriod />
        }[reportType] || <ProfitLossList />}
      </Grid>
    </Grid>
  )
}

export default ProfitLossReport
