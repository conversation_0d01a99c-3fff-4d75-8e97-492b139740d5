import { useAuth } from '@/contexts/AuthContext'
import { SiteType } from '@/types/companyTypes'
import React, { createContext, useEffect, useState } from 'react'
import { useSearchParams } from 'react-router-dom'
import { CashFlowReportType } from '../finance/config/types'

type OwnerEquityContextType = {
  site: SiteType
  reportType: CashFlowReportType
  filter: {
    startDate: string
    endDate: string
  }
}

const OwnerEquityContext = React.createContext<OwnerEquityContextType>({} as OwnerEquityContextType)

export const useOwnerEquityContext = () => {
  const context = React.useContext(OwnerEquityContext)
  if (context === undefined) {
    throw new Error('useCashFlowContext must be used within a CashFlowProvider')
  }
  return context
}

export const OwnerEquityProvider = ({ children }: { children: React.ReactNode }) => {
  const [searchParams] = useSearchParams()
  const { ownSiteList } = useAuth()
  const reportType = searchParams.get('reportType') as CashFlowReportType
  const allSearchParams: Record<string, string> = {}
  for (const [key, value] of searchParams.entries()) {
    allSearchParams[key] = value
  }

  const [selectedSite, setSelectedSite] = useState<SiteType | null>(null)

  useEffect(() => {
    if (ownSiteList) {
      const siteId = searchParams.get('siteId')
      if (siteId) {
        const site = ownSiteList.find(site => site.id === siteId)
        if (site) {
          setSelectedSite(site)
        }
      }
    }
  }, [ownSiteList])

  const value = {
    site: selectedSite,
    reportType,
    filter: {
      startDate: allSearchParams?.startDate,
      endDate: allSearchParams?.endDate
    }
  }

  return <OwnerEquityContext.Provider value={value}>{children}</OwnerEquityContext.Provider>
}
