import { ChildrenType } from '@/core/types'
import React, { createContext, useState } from 'react'
import { ReportTypeEnum } from '../finance/config/types'
import FinancialFocusDialog from '../finance/components/dialogs/financial-focus-dialog'
import RevenueExpenseDialog from '../finance/components/dialogs/revenue-expense-dialog'
import NetAssetDialog from '../finance/components/dialogs/net-asset-dialog'
import LiquidityRatioDialog from '../finance/components/dialogs/liquidity-ratio-dialog'
import OwnerEquityDialog from '../finance/components/dialogs/owner-equity-dialog'
import CashFlowDialog from '../finance/components/dialogs/cash-flow-dialog'
import ProfitLossDialog from '../finance/components/dialogs/profit-loss-dialog'
import BalandeSheetDialog from '../finance/components/dialogs/balance-sheet-dialog'
import RetainedEarningDialog from '../finance/components/dialogs/retained-earning-dialog'

type FinanceContextProps = {
  handleReportClick: (type: ReportTypeEnum) => void
}

const FinanceReportContext = createContext<FinanceContextProps>({} as FinanceContextProps)

export const useFinanceReport = () => {
  const context = React.useContext(FinanceReportContext)
  if (context === undefined) {
    throw new Error('useFinanceReport must be used within a FinanceReportContext')
  }
  return context
}

export const FinanceReportProvider = ({ children }: ChildrenType) => {
  const [
    {
      BalanceSheet,
      CashFlow,
      FinancialFocus,
      ProfitAndLoss,
      RevenueAndExpenseGraph,
      LiquidityRatioGraph,
      NetAssetGraph,
      OwnerEquityChanges,
      RetainedEarnings
    },
    setDialog
  ] = useState<{
    [ReportTypeEnum.BalanceSheet]?: boolean
    [ReportTypeEnum.CashFlow]?: boolean
    [ReportTypeEnum.FinancialFocus]?: boolean
    [ReportTypeEnum.LiquidityRatioGraph]?: boolean
    [ReportTypeEnum.NetAssetGraph]?: boolean
    [ReportTypeEnum.OwnerEquityChanges]?: boolean
    [ReportTypeEnum.ProfitAndLoss]?: boolean
    [ReportTypeEnum.RevenueAndExpenseGraph]?: boolean
    [ReportTypeEnum.RetainedEarnings]?: boolean
  }>({})

  const handleReportClick = (type: ReportTypeEnum) => {
    setDialog(current => ({ ...current, [type]: !current[type] }))
  }

  const value = {
    handleReportClick
  }
  return (
    <FinanceReportContext.Provider value={value}>
      {[
        {
          key: 'FinancialFocus',
          Dialog: FinancialFocusDialog
        },
        {
          key: 'RevenueAndExpenseGraph',
          Dialog: RevenueExpenseDialog
        },
        {
          key: 'NetAssetGraph',
          Dialog: NetAssetDialog
        },
        {
          key: 'LiquidityRatioGraph',
          Dialog: LiquidityRatioDialog
        },
        {
          key: 'OwnerEquityChanges',
          Dialog: OwnerEquityDialog
        },
        {
          key: 'CashFlow',
          Dialog: CashFlowDialog
        },
        {
          key: 'ProfitAndLoss',
          Dialog: ProfitLossDialog
        },
        {
          key: 'BalanceSheet',
          Dialog: BalandeSheetDialog
        },
        {
          key: 'RetainedEarnings',
          Dialog: RetainedEarningDialog
        }
      ].map(
        ({ key, Dialog }) =>
          !!eval(key) && (
            <Dialog open={eval(key)} onClose={() => setDialog(current => ({ ...current, [key]: false }))} key={key} />
          )
      )}
      {children}
    </FinanceReportContext.Provider>
  )
}
