import { useAuth } from '@/contexts/AuthContext'
import { SiteType } from '@/types/companyTypes'
import React, { createContext, useEffect, useState } from 'react'
import { useSearchParams } from 'react-router-dom'

type RevenueExpenseContextType = {
  site: SiteType
  filter: {
    startDate: string
    endDate: string
  }
}

const RevenueExpenseContext = createContext<RevenueExpenseContextType>({} as RevenueExpenseContextType)

export const useRevenueExpenseContext = () => {
  const context = React.useContext(RevenueExpenseContext)
  if (context === undefined) {
    throw new Error('RevenueExpenseContext must be used within RevenueExpenseProvider')
  }
  return context
}

export const RevenueExpenseProvider = ({ children }: { children: React.ReactNode }) => {
  const [searchParams] = useSearchParams()
  const { ownSiteList } = useAuth()
  const siteId = searchParams.get('siteId')

  const [selectedSite, setSelectedSite] = useState<SiteType | null>(null)

  let allSearchParams: Record<string, string> = {}
  for (const [key, value] of searchParams.entries()) {
    allSearchParams[key] = value
  }

  useEffect(() => {
    if (siteId) {
      setSelectedSite(ownSiteList?.find(site => site.id === siteId))
    }
  }, [siteId, ownSiteList])

  const value = {
    site: selectedSite,
    filter: {
      startDate: allSearchParams?.startDate,
      endDate: allSearchParams?.endDate
    }
  }

  return <RevenueExpenseContext.Provider value={value}>{children}</RevenueExpenseContext.Provider>
}
