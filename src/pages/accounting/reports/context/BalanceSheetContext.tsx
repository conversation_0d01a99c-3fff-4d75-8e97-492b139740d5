import { useAuth } from '@/contexts/AuthContext'
import { SiteType } from '@/types/companyTypes'
import React, { createContext, useEffect, useState } from 'react'
import { useSearchParams } from 'react-router-dom'
import { BalanceSheetReportType } from '../finance/config/types'

type BalanceSheetContextType = {
  site: SiteType
  reportType: BalanceSheetReportType
  filter: {
    startDate: string
    endDate: string
  }
  allSearchParams: Record<string, string>
}

const BalanceSheetContext = createContext<BalanceSheetContextType>({} as BalanceSheetContextType)

export const useBalanceSheet = () => {
  const context = React.useContext(BalanceSheetContext)
  if (context === undefined) {
    throw new Error('useCashFlowContext must be used within a CashFlowProvider')
  }
  return context
}

export const BalanceSheetProvider = ({ children }: { children: React.ReactNode }) => {
  const [searchParams] = useSearchParams()
  const { ownSiteList } = useAuth()
  const reportType = searchParams.get('reportType') as BalanceSheetReportType
  const allSearchParams: Record<string, string> = {}
  for (const [key, value] of searchParams.entries()) {
    allSearchParams[key] = value
  }

  const [selectedSite, setSelectedSite] = useState<SiteType | null>(null)

  useEffect(() => {
    if (ownSiteList) {
      const siteId = searchParams.get('siteId')
      if (siteId) {
        const site = ownSiteList.find(site => site.id === siteId)
        if (site) {
          setSelectedSite(site)
        }
      }
    }
  }, [ownSiteList])

  const value = {
    site: selectedSite,
    reportType,
    filter: {
      startDate: allSearchParams?.startDate,
      endDate: allSearchParams?.endDate
    },
    allSearchParams
  }

  return <BalanceSheetContext.Provider value={value}>{children}</BalanceSheetContext.Provider>
}
