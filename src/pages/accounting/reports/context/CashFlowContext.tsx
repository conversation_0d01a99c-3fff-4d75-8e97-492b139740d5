import { useAuth } from '@/contexts/AuthContext'
import { SiteType } from '@/types/companyTypes'
import React, { createContext, useEffect, useState } from 'react'
import { useSearchParams } from 'react-router-dom'
import { CashFlowReportType } from '../finance/config/types'

type CashFlowContextType = {
  site: SiteType
  reportType: CashFlowReportType
  filter: {
    startMonth: string
    endMonth: string
    startYear: string
    endYear: string
  }
  allSearchParams: Record<string, string>
}

const CashFlowContext = React.createContext<CashFlowContextType>({} as CashFlowContextType)

export const useCashFlowContext = () => {
  const context = React.useContext(CashFlowContext)
  if (context === undefined) {
    throw new Error('useCashFlowContext must be used within a CashFlowProvider')
  }
  return context
}

export const CashFlowProvider = ({ children }: { children: React.ReactNode }) => {
  const [searchParams] = useSearchParams()
  const { ownSiteList } = useAuth()
  const reportType = searchParams.get('reportType') as CashFlowReportType
  const allSearchParams: Record<string, string> = {}
  for (const [key, value] of searchParams.entries()) {
    allSearchParams[key] = value
  }

  const [selectedSite, setSelectedSite] = useState<SiteType | null>(null)

  useEffect(() => {
    if (ownSiteList) {
      const siteId = searchParams.get('siteId')
      if (siteId) {
        const site = ownSiteList.find(site => site.id === siteId)
        if (site) {
          setSelectedSite(site)
        }
      }
    }
  }, [ownSiteList])

  const value = {
    site: selectedSite,
    reportType,
    filter: {
      startMonth: allSearchParams?.startMonth,
      endMonth: allSearchParams?.endMonth,
      startYear: allSearchParams?.startYear,
      endYear: allSearchParams?.endYear
    },
    allSearchParams
  }

  return <CashFlowContext.Provider value={value}>{children}</CashFlowContext.Provider>
}
