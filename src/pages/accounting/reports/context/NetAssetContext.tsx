import { useAuth } from '@/contexts/AuthContext'
import { SiteType } from '@/types/companyTypes'
import React, { createContext, useEffect, useState } from 'react'
import { useSearchParams } from 'react-router-dom'

type NetAssetContextType = {
  site: SiteType
  filter: {
    startMonth: string
    startYear: string
    endMonth: string
    endYear: string
  }
}

const NetAssetContext = createContext<NetAssetContextType>({} as NetAssetContextType)

export const useNetAsset = () => {
  const context = React.useContext(NetAssetContext)
  if (context === undefined) {
    throw new Error('RevenueExpenseContext must be used within RevenueExpenseProvider')
  }
  return context
}

export const NetAssetProvider = ({ children }: { children: React.ReactNode }) => {
  const [searchParams] = useSearchParams()
  const { ownSiteList } = useAuth()
  const siteId = searchParams.get('siteId')

  const [selectedSite, setSelectedSite] = useState<SiteType | null>(null)

  let allSearchParams: Record<string, string> = {}
  for (const [key, value] of searchParams.entries()) {
    allSearchParams[key] = value
  }

  useEffect(() => {
    if (siteId) {
      setSelectedSite(ownSiteList?.find(site => site.id === siteId))
    }
  }, [siteId, ownSiteList])

  const value = {
    site: selectedSite,
    filter: {
      startMonth: allSearchParams?.startMonth,
      startYear: allSearchParams?.startYear,
      endMonth: allSearchParams?.endMonth,
      endYear: allSearchParams?.endYear
    }
  }

  return <NetAssetContext.Provider value={value}>{children}</NetAssetContext.Provider>
}
