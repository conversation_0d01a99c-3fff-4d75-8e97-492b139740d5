import { useAuth } from '@/contexts/AuthContext'
import { SiteType } from '@/types/companyTypes'
import React, { createContext, useEffect, useState } from 'react'
import { useSearchParams } from 'react-router-dom'

type LiquidityRatioContextType = {
  site: SiteType
  filter: {
    startMonth: string
    endMonth: string
    startYear: string
    endYear: string
  }
}

const LiquidityRatioContext = createContext<LiquidityRatioContextType>({} as LiquidityRatioContextType)

export const useLiquidityRatio = () => {
  const context = React.useContext(LiquidityRatioContext)
  if (context === undefined) {
    throw new Error('RevenueExpenseContext must be used within RevenueExpenseProvider')
  }
  return context
}

export const LiquidityRatioProvider = ({ children }: { children: React.ReactNode }) => {
  const [searchParams] = useSearchParams()
  const { ownSiteList } = useAuth()
  const siteId = searchParams.get('siteId')

  const [selectedSite, setSelectedSite] = useState<SiteType | null>(null)

  let allSearchParams: Record<string, string> = {}
  for (const [key, value] of searchParams.entries()) {
    allSearchParams[key] = value
  }

  useEffect(() => {
    if (siteId) {
      setSelectedSite(ownSiteList?.find(site => site.id === siteId))
    }
  }, [siteId, ownSiteList])

  const value = {
    site: selectedSite,
    filter: {
      startMonth: allSearchParams?.startMonth,
      endMonth: allSearchParams?.endMonth,
      startYear: allSearchParams?.startYear,
      endYear: allSearchParams?.endYear
    }
  }

  return <LiquidityRatioContext.Provider value={value}>{children}</LiquidityRatioContext.Provider>
}
