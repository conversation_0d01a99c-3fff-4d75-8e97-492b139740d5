import React, { useEffect, useState } from 'react'
import { ChildrenType } from '@/core/types'
import { useSearchParams } from 'react-router-dom'
import { ProfitAndLossReportType } from '../finance/config/types'
import { useAuth } from '@/contexts/AuthContext'
import { SiteType } from '@/types/companyTypes'
import { JournalProfitLoss } from '@/types/reportTypes'

type ProfitLossContextProps = {
  site: SiteType
  filter: {
    reportType: ProfitAndLossReportType
    startDate: string
    endDate: string
  }
  allSearchParams: Record<string, string>
  profitLoss: JournalProfitLoss
  setProfitLoss: React.Dispatch<React.SetStateAction<JournalProfitLoss>>
}

const ProfitLossContext = React.createContext<ProfitLossContextProps>({} as ProfitLossContextProps)

export const useProfitLoss = () => {
  const context = React.useContext(ProfitLossContext)
  if (context === undefined) {
    throw new Error('useProfitLoss must be used within a ProfitLossContext')
  }
  return context
}

export const ProfitLossProvider = ({ children }: ChildrenType) => {
  const [searchParams] = useSearchParams()
  const { ownSiteList } = useAuth()
  const [selectedSite, setSelectedSite] = useState<SiteType | null>(null)
  const [profitLoss, setProfitLoss] = useState<JournalProfitLoss | null>(null)
  const reportType = searchParams.get('reportType') as ProfitAndLossReportType
  const siteId = searchParams.get('siteId')
  const { startDate, endDate } = {
    startDate: searchParams.get('startDate'),
    endDate: searchParams.get('endDate')
  }
  const allSearchParams: Record<string, string> = {}

  for (const [key, value] of searchParams.entries()) {
    allSearchParams[key] = value
  }

  useEffect(() => {
    if (siteId) {
      setSelectedSite(ownSiteList?.find(site => site.id === siteId))
    }
  }, [siteId, ownSiteList])

  const value = {
    filter: {
      reportType,
      startDate,
      endDate
    },
    site: selectedSite,
    allSearchParams,
    profitLoss,
    setProfitLoss
  }
  return <ProfitLossContext.Provider value={value}>{children}</ProfitLossContext.Provider>
}
