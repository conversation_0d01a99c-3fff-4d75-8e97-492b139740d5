import { useAuth } from '@/contexts/AuthContext'
import { ChildrenType } from '@/core/types'
import { SiteType } from '@/types/companyTypes'
import React, { createContext, useEffect, useState } from 'react'
import { useSearchParams } from 'react-router-dom'

type RetainedEarningsContextProps = {
  site: SiteType
  year: string
  siteId: string
}

const RetainedEarningsContext = createContext<RetainedEarningsContextProps>({} as RetainedEarningsContextProps)

export const useRetainedEarnings = () => {
  const context = React.useContext(RetainedEarningsContext)
  if (context === undefined) {
    throw new Error('useFinancialFocus must be used within a FinancialFocusContext')
  }
  return context
}

export const RetainedEarningsProvider = ({ children }: ChildrenType) => {
  const [searchParams] = useSearchParams()
  const { ownSiteList } = useAuth()
  const [selectedSite, setSelectedSite] = useState<SiteType | null>(null)
  const year = searchParams.get('year')
  const siteId = searchParams.get('siteId')

  useEffect(() => {
    if (siteId) {
      setSelectedSite(ownSiteList?.find(site => site.id === siteId))
    }
  }, [siteId, ownSiteList])

  const value = {
    site: selectedSite,
    year,
    siteId
  }

  return <RetainedEarningsContext.Provider value={value}>{children}</RetainedEarningsContext.Provider>
}
