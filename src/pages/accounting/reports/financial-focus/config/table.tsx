import { toCurrency } from '@/utils/helper'
import { Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'

type FinancialFocusTable = {
  description: string
  thisYear: number
  previousYear: number
  percentage: number
}

const columHelper = createColumnHelper<FinancialFocusTable>()

export const tableColumns = (currentYear: string) => [
  columHelper.accessor('description', {
    header: () => (
      <Typography align='left' className='font-normal text-textPrimary text-sm'>
        Deskripsi
      </Typography>
    ),
    cell: info => <Typography>{info.getValue()}</Typography>
  }),
  columHelper.accessor('thisYear', {
    header: () => (
      <Typography align='right' className='font-normal text-textPrimary text-sm'>
        Tahun ini ({currentYear})
      </Typography>
    ),
    cell: info => (
      <Typography align='right' className='font-semibold'>
        {toCurrency(info.getValue())}
      </Typography>
    )
  }),
  columHelper.accessor('previousYear', {
    header: () => (
      <Typography align='right' className='font-normal text-textPrimary text-sm'>
        Tahun lalu ({+currentYear - 1})
      </Typography>
    ),
    cell: info => <Typography align='right'>{toCurrency(info.getValue())}</Typography>
  }),
  columHelper.accessor('percentage', {
    header: () => (
      <Typography align='right' className='font-normal text-textPrimary text-sm'>
        % Penambahan
      </Typography>
    ),
    cell: info => (
      <Typography align='right' className='font-semibold' color={info.getValue() > 0 ? 'primary' : 'secondary'}>
        {info.getValue()} %
      </Typography>
    )
  })
]
