import { Card, CardContent, Typography } from '@mui/material'
import { useMemo } from 'react'
import { tableColumns } from '../config/table'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import Table from '@/components/table'
import { useFinancialFocus } from '../../context/FinancialFocusContext'

const FinancialFocusList = () => {
  const { site, year } = useFinancialFocus()
  const tableOptions = useMemo(
    () => ({
      data: [
        {
          description: 'Total Pendapatan',
          thisYear: 70000000000,
          previousYear: 70000000000,
          percentage: 0
        },
        {
          description: 'Pendapatan Operasional',
          thisYear: 0,
          previousYear: 0,
          percentage: 0
        },
        {
          description: 'Laba/Rugi',
          thisYear: 0,
          previousYear: 0,
          percentage: 0
        },
        {
          description: 'Modal Kerja',
          thisYear: 30000000000,
          previousYear: 15000000000,
          percentage: 100
        },
        {
          description: 'Rasio Lancar',
          thisYear: 1.92,
          previousYear: 1.7,
          percentage: 12.9
        },
        {
          description: 'Liabilitas Jangka Panjang',
          thisYear: 0,
          previousYear: 0,
          percentage: 0
        },
        {
          description: 'Modal',
          thisYear: 1000000000,
          previousYear: 900000000,
          percentage: 11.1
        }
      ],
      columns: tableColumns(year),
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [year]
  )

  const table = useReactTable<any>(tableOptions)

  return (
    <Card>
      <CardContent className='flex flex-col gap-5 py-8'>
        <div className='flex flex-col items-center justify-center'>
          <Typography variant='h5' color='primary'>
            PT Equalindo Makmur Alam Sejahtera
          </Typography>
          <Typography variant='h4'>Laporan Fokus Keuangan</Typography>
          <Typography variant='caption' className='font-semibold text-textPrimary'>
            Per Tahun {year}
          </Typography>
          <Typography className='font-semibold'>Lokasi: {site?.name}</Typography>
        </div>
        <div className='rounded-[8px] shadow-md'>
          <Table
            headerColor='green'
            table={table}
            emptyLabel={
              <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
                <Typography> Belum ada data</Typography>
                <Typography className='text-sm text-gray-400'>
                  Semua data untuk laporan ini akan ditampilkan di sini
                </Typography>
              </td>
            }
            disablePagination
          />
        </div>
      </CardContent>
    </Card>
  )
}

export default FinancialFocusList
