import { Breadcrumbs, Button, Grid, Typography } from '@mui/material'
import { Link } from 'react-router-dom'
import FinancialFocusList from './components/FinancialFocusList'
import { useFinancialFocus } from '../context/FinancialFocusContext'

const FinancialFocusReport = () => {
  const { year } = useFinancialFocus()
  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Breadcrumbs>
          <Link to='#' replace>
            <Typography color='var(--color-text-disabled)'>Laporan</Typography>
          </Link>
          <Link to='/report/finance' replace>
            <Typography color='var(--color-text-disabled)'>Ke<PERSON>an</Typography>
          </Link>
          <Typography>Detil Laporan</Typography>
        </Breadcrumbs>
      </Grid>
      <Grid item xs={12}>
        <div className='flex justify-between items-end'>
          <div className='flex flex-col'>
            <Typography variant='h4'>Detil Laporan Fokus <PERSON></Typography>
            <Typography>Per <PERSON>hun {year}</Typography>
          </div>
          <div className='flex gap-2'>
            <Button
              color='secondary'
              variant='outlined'
              startIcon={<i className='ri-upload-2-line' />}
              className='is-full sm:is-auto'
            >
              Ekspor
            </Button>
            <Button
              variant='contained'
              startIcon={<i className='ic-outline-local-printshop size-5' />}
              className='is-full sm:is-auto'
            >
              Cetak
            </Button>
          </div>
        </div>
      </Grid>
      <Grid item xs={12}>
        <FinancialFocusList />
      </Grid>
    </Grid>
  )
}

export default FinancialFocusReport
