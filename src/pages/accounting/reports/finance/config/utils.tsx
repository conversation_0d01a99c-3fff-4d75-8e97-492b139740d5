import { FinancialReportOption, ReportTypeEnum } from './types'

export const financialReportOptions: FinancialReportOption[] = [
  {
    label: 'Foku<PERSON>',
    description: 'Lihat laporan keuangan komprehensif',
    href: '/reports/financial-focus',
    type: ReportTypeEnum.FinancialFocus
  },
  {
    label: 'Laba/Rugi',
    description: 'Lihat laporan laba/rugi perusahaan sesuai periode',
    href: '/reports/profit-and-loss',
    type: ReportTypeEnum.ProfitAndLoss
  },
  {
    label: 'Neraca',
    description: 'Lihat laporan neraca perusahaan sesuai periode',
    href: '/reports/balance-sheet',
    type: ReportTypeEnum.BalanceSheet
  },
  {
    label: 'Arus Kas',
    description: 'Lihat arus kas dan aliran keluar kas sesuai periode',
    href: '/reports/cash-flow',
    type: ReportTypeEnum.CashFlow
  },
  {
    label: 'Grafik Pendapatan & Beban',
    description: 'Lihat grafik pendapatan dan beban sesuai periode',
    href: '/reports/revenue-and-expense-graph',
    type: ReportTypeEnum.RevenueAndExpenseGraph
  },
  {
    label: 'Grafik Harta Bersih',
    description: 'Lihat grafik nilai aset berbanding nilai kewajiban sesuai periode',
    href: '/reports/net-asset-graph',
    type: ReportTypeEnum.NetAssetGraph
  },
  {
    label: 'Grafik Rasio Likuiditas',
    description: 'Lihat total nilai aset likuid pada utang',
    href: '/reports/liquidity-ratio-graph',
    type: ReportTypeEnum.LiquidityRatioGraph
  },
  {
    label: 'Perubahan Ekuitas Pemilik',
    description: 'Lihat perubahan ekuitas pemilik',
    href: '/reports/owner-equity-changes',
    type: ReportTypeEnum.OwnerEquityChanges
  },
  {
    label: 'Laba Ditahan',
    description: 'Lihat total nilai laba ditahan selama periode tertentu',
    href: '/reports/retained-earnings',
    type: ReportTypeEnum.RetainedEarnings
  }
]
