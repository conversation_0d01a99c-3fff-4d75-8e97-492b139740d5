export enum ReportTypeEnum {
  FinancialFocus = 'FinancialFocus',
  ProfitAndLoss = 'ProfitAndLoss',
  BalanceSheet = 'BalanceSheet',
  CashFlow = 'CashFlow',
  RevenueAndExpenseGraph = 'RevenueAndExpenseGraph',
  NetAssetGraph = 'NetAssetGraph',
  LiquidityRatioGraph = 'LiquidityRatioGraph',
  OwnerEquityChanges = 'OwnerEquityChanges',
  RetainedEarnings = 'RetainedEarnings'
}

export enum CashFlowReportType {
  DirectMethod = 'DirectMethod',
  IndirectMethod = 'IndirectMethod',
  MonthlyDirect = 'MonthlyDirect',
  MonthlyIndirect = 'MonthlyIndirect',
  DetailedIndirect = 'DetailedIndirect'
}

export enum ProfitAndLossReportType {
  Standard = 'Standard',
  MultiPeriod = 'MultiPeriod',
  PeriodComparison = 'PeriodComparison',
  Consolidation = 'Consolidation',
  BudgetPeriod = 'BudgetPeriod'
}

export enum BalanceSheetReportType {
  Standard = 'Standar',
  MultiPeriod = 'MultiPeriod',
  MonthlyComparison = 'MonthlyComparison',
  Consolidation = 'Consolidation',
  BudgetPeriod = 'BudgetPeriod',
  IndukSkontro = 'IndukSkontro'
}

export type FinancialReportOption = {
  label: string
  description: string
  href: string
  type: ReportTypeEnum
}

export type DialogProps = {
  open: boolean
  onClose: (open: boolean) => void
}
