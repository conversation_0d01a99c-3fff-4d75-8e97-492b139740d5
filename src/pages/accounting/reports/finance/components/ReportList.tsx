import ChevronRight from '@/components/menu/svg/ChevronRight'
import { Card, CardContent, Grid, IconButton, Typography } from '@mui/material'
import { financialReportOptions } from '../config/utils'
import { useFinanceReport } from '../../context/FinanceReportContext'

const ReportList = () => {
  const { handleReportClick } = useFinanceReport()
  return (
    <Grid container spacing={4}>
      {financialReportOptions.map(report => (
        <Grid key={report.type} item xs={12} md={6}>
          <Card>
            <CardContent className='flex justify-between'>
              <div className='flex flex-col'>
                <Typography variant='h5'>{report.label}</Typography>
                <Typography>{report.description}</Typography>
              </div>
              <IconButton onClick={() => handleReportClick(report.type)}>
                <ChevronRight />
              </IconButton>
            </CardContent>
          </Card>
        </Grid>
      ))}
    </Grid>
  )
}

export default ReportList
