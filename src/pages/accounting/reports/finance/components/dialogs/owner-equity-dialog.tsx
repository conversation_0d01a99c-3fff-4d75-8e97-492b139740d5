import { useState } from 'react'
import {
  Dialog,
  DialogContent,
  DialogTitle,
  IconButton,
  Grid,
  Typography,
  TextField,
  DialogActions,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material'
import { useAuth } from '@/contexts/AuthContext'
import { useLocation } from 'react-router-dom'
import { useRouter } from '@/routes/hooks'
import { Controller, useForm } from 'react-hook-form'
import AppReactDatepicker from '@/components/libs/styles/AppReactDatepicker'
import { format, formatISO, toDate } from 'date-fns'
import LoadingButton from '@mui/lab/LoadingButton'
import { DialogProps } from '../../config/types'

const OwnerEquityDialog = ({ open, onClose }: DialogProps) => {
  const { ownSiteList } = useAuth()
  const { pathname } = useLocation()
  const router = useRouter()
  const { control, handleSubmit } = useForm()

  const handleClose = () => {
    onClose(false)
  }

  const onSubmitFilter = inputValues => {
    if (inputValues.startDate) {
      inputValues.startDate = format(inputValues.startDate, 'MM-dd-yyyy')
    }
    if (inputValues.endDate) {
      inputValues.endDate = format(inputValues.endDate, 'MM-dd-yyyy')
    }
    router.push(`${pathname}/owner-equity?${new URLSearchParams(inputValues).toString()}`)
    handleClose()
  }

  return (
    <Dialog fullWidth maxWidth='sm' scroll='body' open={open} onClose={handleClose}>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-16'>
        Laporan Perubahan Ekuitas Pemilik
        <Typography component='span' className='flex flex-col text-center'>
          Pilih parameter yang akan ditampilkan di laporan ini
        </Typography>
      </DialogTitle>
      <form onSubmit={e => e.preventDefault()}>
        <DialogContent className='overflow-visible pbs-0 sm:pbe-6 sm:px-16'>
          <IconButton onClick={handleClose} className='absolute block-start-4 inline-end-4'>
            <i className='ri-close-line text-textSecondary' />
          </IconButton>
          <Grid container spacing={4}>
            <Grid item xs={12} md={6}>
              <Controller
                control={control}
                name='startDate'
                rules={{ required: true }}
                render={({ field: { value, onChange }, fieldState: { error } }) => (
                  <AppReactDatepicker
                    boxProps={{ className: 'is-full' }}
                    selected={value ? toDate(value) : undefined}
                    onChange={(date: Date) => onChange(formatISO(date))}
                    dateFormat='dd/MM/yyyy'
                    customInput={
                      <TextField fullWidth label='Dari tanggal' placeholder='Pilih Tanggal' error={!!error} />
                    }
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Controller
                control={control}
                name='endDate'
                rules={{ required: true }}
                render={({ field: { value, onChange }, fieldState: { error } }) => (
                  <AppReactDatepicker
                    boxProps={{ className: 'is-full' }}
                    selected={value ? toDate(value) : undefined}
                    onChange={(date: Date) => onChange(formatISO(date))}
                    dateFormat='dd/MM/yyyy'
                    customInput={
                      <TextField fullWidth label='Sampai tanggal' placeholder='Pilih Tanggal' error={!!error} />
                    }
                  />
                )}
              />
            </Grid>
            <Grid item xs={12}>
              <Controller
                control={control}
                name='siteId'
                rules={{ required: true }}
                render={({ field: { value, onChange }, fieldState: { error } }) => (
                  <FormControl fullWidth>
                    <InputLabel id='site-select'>Lokasi</InputLabel>
                    <Select
                      value={value}
                      onChange={onChange}
                      labelId='site-select'
                      id='site-select'
                      label='Lokasi'
                      placeholder='Pilih Lokasi'
                      error={!!error}
                    >
                      {ownSiteList?.map(site => (
                        <MenuItem key={site.id} value={site.id}>
                          {site.name}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                )}
              />
            </Grid>
          </Grid>
        </DialogContent>
      </form>
      <DialogActions className='justify-center pbs-0 sm:pbe-10 sm:px-16'>
        <Button variant='outlined' onClick={handleClose}>
          Batalkan
        </Button>
        <LoadingButton variant='contained' color='primary' onClick={handleSubmit(onSubmitFilter)}>
          Tampilkan
        </LoadingButton>
      </DialogActions>
    </Dialog>
  )
}

export default OwnerEquityDialog
