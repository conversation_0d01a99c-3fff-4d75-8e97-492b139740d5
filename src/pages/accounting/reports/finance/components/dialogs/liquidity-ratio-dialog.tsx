import AppReactDatepicker from '@/components/libs/styles/AppReactDatepicker'
import { useAuth } from '@/contexts/AuthContext'
import { useRouter } from '@/routes/hooks'
import LoadingButton from '@mui/lab/LoadingButton'
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography
} from '@mui/material'
import { format, formatISO, toDate } from 'date-fns'
import { Controller, useForm, useWatch } from 'react-hook-form'
import { useLocation, useSearchParams } from 'react-router-dom'
import { DialogProps } from '../../config/types'

const LiquidityRatioDialog = ({ open, onClose }: DialogProps) => {
  const { ownSiteList } = useAuth()
  const { pathname } = useLocation()
  const router = useRouter()
  const { control, handleSubmit } = useForm()

  const startMonth = useWatch({ control, name: 'startMonth' })
  const startYear = useWatch({ control, name: 'startYear' })

  const handleClose = () => {
    onClose(false)
  }

  const onSubmitFilter = inputValues => {
    if (inputValues.startMonth) {
      inputValues.startMonth = format(inputValues.startMonth, 'MM')
    }
    if (inputValues.endMonth) {
      inputValues.endMonth = format(inputValues.endMonth, 'MM')
    }
    if (inputValues.startYear) {
      inputValues.startYear = format(inputValues.startYear, 'yyyy')
    }
    if (inputValues.endYear) {
      inputValues.endYear = format(inputValues.endYear, 'yyyy')
    }
    router.push(`${pathname}/liquidity-ratio?${new URLSearchParams(inputValues).toString()}`)
    handleClose()
  }

  return (
    <Dialog fullWidth maxWidth='sm' scroll='body' open={open} onClose={handleClose}>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-16'>
        Grafik Rasio Liquiditas
        <Typography component='span' className='flex flex-col text-center'>
          Pilih parameter yang akan ditampilkan di laporan ini
        </Typography>
      </DialogTitle>
      <form onSubmit={e => e.preventDefault()}>
        <DialogContent className='overflow-visible pbs-0 sm:pbe-6 sm:px-16'>
          <IconButton onClick={handleClose} className='absolute block-start-4 inline-end-4'>
            <i className='ri-close-line text-textSecondary' />
          </IconButton>
          <Grid container spacing={4}>
            <Grid item xs={12} md={6}>
              <Controller
                control={control}
                name='startMonth'
                rules={{ required: true }}
                render={({ field: { value, onChange }, fieldState: { error } }) => (
                  <AppReactDatepicker
                    boxProps={{ className: 'is-full' }}
                    selected={value ? toDate(value) : undefined}
                    onChange={(date: Date) => onChange(formatISO(date))}
                    dateFormat='MMMM'
                    showMonthYearPicker
                    customInput={<TextField fullWidth label='Dari Bulan' placeholder='Pilih Bulan' error={!!error} />}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Controller
                control={control}
                name='startYear'
                rules={{ required: true }}
                render={({ field: { value, onChange }, fieldState: { error } }) => (
                  <AppReactDatepicker
                    boxProps={{ className: 'is-full' }}
                    selected={value ? toDate(value) : undefined}
                    onChange={(date: Date) => onChange(formatISO(date))}
                    dateFormat='YYYY'
                    showYearPicker
                    customInput={<TextField fullWidth label='Tahun' placeholder='Pilih Tahun' error={!!error} />}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Controller
                control={control}
                name='endMonth'
                rules={{ required: true }}
                render={({ field: { value, onChange }, fieldState: { error } }) => (
                  <AppReactDatepicker
                    boxProps={{ className: 'is-full' }}
                    selected={value ? toDate(value) : undefined}
                    onChange={(date: Date) => onChange(formatISO(date))}
                    dateFormat='MMMM'
                    showMonthYearPicker
                    minDate={startMonth ? toDate(startMonth) : undefined}
                    customInput={<TextField fullWidth label='Sampai Bulan' placeholder='Pilih Bulan' error={!!error} />}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Controller
                control={control}
                name='endYear'
                rules={{ required: true }}
                render={({ field: { value, onChange }, fieldState: { error } }) => (
                  <AppReactDatepicker
                    boxProps={{ className: 'is-full' }}
                    selected={value ? toDate(value) : undefined}
                    onChange={(date: Date) => onChange(formatISO(date))}
                    dateFormat='yyyy'
                    showYearPicker
                    minDate={startYear ? toDate(startYear) : undefined}
                    customInput={<TextField fullWidth label='Tahun' placeholder='Pilih Tahun' error={!!error} />}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12}>
              <Controller
                control={control}
                name='siteId'
                rules={{ required: true }}
                render={({ field: { value, onChange }, fieldState: { error } }) => (
                  <FormControl fullWidth>
                    <InputLabel id='site-select'>Lokasi</InputLabel>
                    <Select
                      value={value}
                      onChange={onChange}
                      labelId='site-select'
                      id='site-select'
                      label='Lokasi'
                      placeholder='Pilih Lokasi'
                      error={!!error}
                    >
                      {ownSiteList?.map(site => (
                        <MenuItem key={site.id} value={site.id}>
                          {site.name}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                )}
              />
            </Grid>
          </Grid>
        </DialogContent>
      </form>
      <DialogActions className='justify-center pbs-0 sm:pbe-10 sm:px-16'>
        <Button variant='outlined' onClick={handleClose}>
          Batalkan
        </Button>
        <LoadingButton variant='contained' color='primary' onClick={handleSubmit(onSubmitFilter)}>
          Tampilkan
        </LoadingButton>
      </DialogActions>
    </Dialog>
  )
}

export default LiquidityRatioDialog
