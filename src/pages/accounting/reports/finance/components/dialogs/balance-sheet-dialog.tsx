import AppReactDatepicker from '@/components/libs/styles/AppReactDatepicker'
import { useAuth } from '@/contexts/AuthContext'
import { useRouter } from '@/routes/hooks'
import LoadingButton from '@mui/lab/LoadingButton'
import {
  Button,
  Checkbox,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  FormControlLabel,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography
} from '@mui/material'
import { format, formatISO, toDate } from 'date-fns'
import { Controller, useForm, useWatch } from 'react-hook-form'
import { useLocation } from 'react-router-dom'
import { BalanceSheetReportType, DialogProps, ProfitAndLossReportType } from '../../config/types'
import { balanceSheetReportOptions } from './utils'
import { isNullOrUndefined } from '@/utils/helper'

const BalandeSheetDialog = ({ open, onClose }: DialogProps) => {
  const { ownSiteList } = useAuth()
  const { pathname } = useLocation()
  const router = useRouter()
  const { control, handleSubmit, reset, getValues } = useForm()

  const reportTypeWatch = useWatch({
    control,
    name: 'reportType'
  })

  const [startMonthWatch, startYearWatch, startDateWatch] = useWatch({
    control,
    name: ['startMonth', 'startYear', 'startDate'],
    defaultValue: {}
  })

  const handleClose = () => {
    onClose(false)
  }

  const onSubmitFilter = inputValues => {
    if (inputValues.year) {
      inputValues.year = format(inputValues.year, 'yyyy')
    } else {
      delete inputValues.year
    }
    if (inputValues.date) {
      inputValues.date = format(inputValues.date, 'MM-dd-yyyy')
    } else {
      delete inputValues.date
    }
    if (inputValues.startMonth) {
      inputValues.startMonth = format(inputValues.startMonth, 'MM')
    } else {
      delete inputValues.startMonth
    }
    if (inputValues.endMonth) {
      inputValues.endMonth = format(inputValues.endMonth, 'MM')
    } else {
      delete inputValues.endMonth
    }
    if (inputValues.startYear) {
      inputValues.startYear = format(inputValues.startYear, 'yyyy')
    } else {
      delete inputValues.startYear
    }
    if (inputValues.endYear) {
      inputValues.endYear = format(inputValues.endYear, 'yyyy')
    } else {
      delete inputValues.endYear
    }
    if (inputValues.startDate) {
      inputValues.startDate = format(inputValues.startDate, 'MM-dd-yyyy')
    } else {
      delete inputValues.startDate
    }
    if (inputValues.endDate) {
      inputValues.endDate = format(inputValues.endDate, 'MM-dd-yyyy')
    } else {
      delete inputValues.endDate
    }
    inputValues.showTotal = !!inputValues.showTotal
    inputValues.showParentAccount = !!inputValues.showParentAccount
    inputValues.showSubAccount = !!inputValues.showSubAccount
    inputValues.showZeroBalance = !!inputValues.showZeroBalance
    inputValues.showParentBalance = !!inputValues.showParentBalance

    router.push(`${pathname}/balance-sheet?${new URLSearchParams(inputValues).toString()}`)
    handleClose()
  }

  return (
    <Dialog fullWidth maxWidth='sm' scroll='body' open={open} onClose={handleClose}>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-16'>
        Laporan Neraca
        <Typography component='span' className='flex flex-col text-center'>
          Pilih parameter yang akan ditampilkan di laporan ini
        </Typography>
      </DialogTitle>
      <form onSubmit={e => e.preventDefault()}>
        <DialogContent className='overflow-visible pbs-0 sm:pbe-6 sm:px-16'>
          <IconButton onClick={handleClose} className='absolute block-start-4 inline-end-4'>
            <i className='ri-close-line text-textSecondary' />
          </IconButton>
          <Grid container spacing={4}>
            <Grid item xs={12}>
              <Controller
                control={control}
                name='reportType'
                rules={{ required: true }}
                render={({ field: { value, onChange }, fieldState: { error } }) => (
                  <FormControl fullWidth>
                    <InputLabel id='report-type-select'>Tipe Laporan</InputLabel>
                    <Select
                      value={value}
                      onChange={onChange}
                      labelId='report-type-select'
                      id='report-type-select'
                      label='Tipe Laporan'
                      onBlur={() => {
                        reset({
                          ...getValues(),
                          startMonth: undefined,
                          startYear: undefined,
                          endMonth: undefined,
                          endYear: undefined,
                          year: undefined
                        })
                      }}
                      placeholder='Pilih Tipe Laporan'
                      error={!!error}
                    >
                      {balanceSheetReportOptions.map(pnl => (
                        <MenuItem key={pnl.value} value={pnl.value}>
                          {pnl.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                )}
              />
            </Grid>
            {{
              [BalanceSheetReportType.Standard]: (
                <>
                  <Grid item xs={12} md={6}>
                    <Controller
                      control={control}
                      name='startDate'
                      rules={{
                        validate: value => {
                          if (isNullOrUndefined(value) && reportTypeWatch === ProfitAndLossReportType.Standard) {
                            return 'Wajib diisi'
                          }
                          return true
                        }
                      }}
                      render={({ field: { value, onChange }, fieldState: { error } }) => (
                        <AppReactDatepicker
                          boxProps={{ className: 'is-full' }}
                          selected={value ? toDate(value) : undefined}
                          onChange={(date: Date) => onChange(formatISO(date))}
                          dateFormat='dd/MM/yyyy'
                          customInput={
                            <TextField fullWidth label='Dari Tanggal' placeholder='Pilih tanggal' error={!!error} />
                          }
                        />
                      )}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Controller
                      control={control}
                      name='endDate'
                      rules={{
                        validate: value => {
                          if (isNullOrUndefined(value) && reportTypeWatch === ProfitAndLossReportType.Standard) {
                            return 'Wajib diisi'
                          }
                          return true
                        }
                      }}
                      render={({ field: { value, onChange }, fieldState: { error } }) => (
                        <AppReactDatepicker
                          boxProps={{ className: 'is-full' }}
                          selected={value ? toDate(value) : undefined}
                          onChange={(date: Date) => onChange(formatISO(date))}
                          dateFormat='dd/MM/yyyy'
                          minDate={startDateWatch ? toDate(startDateWatch) : undefined}
                          customInput={
                            <TextField fullWidth label='Sampai Tanggal' placeholder='Pilih Tanggal' error={!!error} />
                          }
                        />
                      )}
                    />
                  </Grid>
                </>
              ),
              [BalanceSheetReportType.MultiPeriod]: (
                <>
                  <Grid item xs={12} md={6}>
                    <Controller
                      control={control}
                      name='startMonth'
                      rules={{
                        validate: value => {
                          if (isNullOrUndefined(value) && reportTypeWatch === ProfitAndLossReportType.MultiPeriod) {
                            return 'Wajib diisi'
                          }
                          return true
                        }
                      }}
                      render={({ field: { value, onChange }, fieldState: { error } }) => (
                        <AppReactDatepicker
                          boxProps={{ className: 'is-full' }}
                          selected={value ? toDate(value) : undefined}
                          onChange={(date: Date) => onChange(formatISO(date))}
                          dateFormat='MMMM'
                          showMonthYearPicker
                          customInput={
                            <TextField fullWidth label='Dari Bulan' placeholder='Pilih bulan' error={!!error} />
                          }
                        />
                      )}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Controller
                      control={control}
                      name='startYear'
                      rules={{
                        validate: value => {
                          if (isNullOrUndefined(value) && reportTypeWatch === ProfitAndLossReportType.MultiPeriod) {
                            return 'Wajib diisi'
                          }
                          return true
                        }
                      }}
                      render={({ field: { value, onChange }, fieldState: { error } }) => (
                        <AppReactDatepicker
                          boxProps={{ className: 'is-full' }}
                          selected={value ? toDate(value) : undefined}
                          onChange={(date: Date) => onChange(formatISO(date))}
                          dateFormat='YYYY'
                          showYearPicker
                          customInput={<TextField fullWidth label='Tahun' placeholder='Pilih Tahun' error={!!error} />}
                        />
                      )}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Controller
                      control={control}
                      name='endMonth'
                      rules={{
                        validate: value => {
                          if (isNullOrUndefined(value) && reportTypeWatch === ProfitAndLossReportType.MultiPeriod) {
                            return 'Wajib diisi'
                          }
                          return true
                        }
                      }}
                      render={({ field: { value, onChange }, fieldState: { error } }) => (
                        <AppReactDatepicker
                          boxProps={{ className: 'is-full' }}
                          selected={value ? toDate(value) : undefined}
                          onChange={(date: Date) => onChange(formatISO(date))}
                          dateFormat='MMMM'
                          showMonthYearPicker
                          minDate={startMonthWatch ? toDate(startMonthWatch) : undefined}
                          customInput={
                            <TextField fullWidth label='Sampai Bulan' placeholder='Pilih bulan' error={!!error} />
                          }
                        />
                      )}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Controller
                      control={control}
                      name='endYear'
                      rules={{
                        validate: value => {
                          if (isNullOrUndefined(value) && reportTypeWatch === ProfitAndLossReportType.MultiPeriod) {
                            return 'Wajib diisi'
                          }
                          return true
                        }
                      }}
                      render={({ field: { value, onChange }, fieldState: { error } }) => (
                        <AppReactDatepicker
                          boxProps={{ className: 'is-full' }}
                          selected={value ? toDate(value) : undefined}
                          onChange={(date: Date) => onChange(formatISO(date))}
                          dateFormat='yyyy'
                          showYearPicker
                          minDate={startYearWatch ? toDate(startYearWatch) : undefined}
                          customInput={<TextField fullWidth label='Tahun' placeholder='Pilih Tahun' error={!!error} />}
                        />
                      )}
                    />
                  </Grid>
                </>
              ),
              [BalanceSheetReportType.MonthlyComparison]: (
                <>
                  <Grid item xs={12}>
                    <Typography className='font-semibold'>Bandingkan Periode</Typography>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Controller
                      control={control}
                      name='startMonth'
                      rules={{
                        validate: value => {
                          if (
                            isNullOrUndefined(value) &&
                            reportTypeWatch === ProfitAndLossReportType.PeriodComparison
                          ) {
                            return 'Wajib diisi'
                          }
                          return true
                        }
                      }}
                      render={({ field: { value, onChange }, fieldState: { error } }) => (
                        <AppReactDatepicker
                          boxProps={{ className: 'is-full' }}
                          selected={value ? toDate(value) : undefined}
                          onChange={(date: Date) => onChange(formatISO(date))}
                          dateFormat='MMMM'
                          showMonthYearPicker
                          customInput={<TextField fullWidth label='Bulan' placeholder='Pilih bulan' error={!!error} />}
                        />
                      )}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Controller
                      control={control}
                      name='startYear'
                      rules={{
                        validate: value => {
                          if (
                            isNullOrUndefined(value) &&
                            reportTypeWatch === ProfitAndLossReportType.PeriodComparison
                          ) {
                            return 'Wajib diisi'
                          }
                          return true
                        }
                      }}
                      render={({ field: { value, onChange }, fieldState: { error } }) => (
                        <AppReactDatepicker
                          boxProps={{ className: 'is-full' }}
                          selected={value ? toDate(value) : undefined}
                          onChange={(date: Date) => onChange(formatISO(date))}
                          dateFormat='YYYY'
                          showYearPicker
                          customInput={<TextField fullWidth label='Tahun' placeholder='Pilih Tahun' error={!!error} />}
                        />
                      )}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Controller
                      control={control}
                      name='endMonth'
                      rules={{
                        validate: value => {
                          if (
                            isNullOrUndefined(value) &&
                            reportTypeWatch === ProfitAndLossReportType.PeriodComparison
                          ) {
                            return 'Wajib diisi'
                          }
                          return true
                        }
                      }}
                      render={({ field: { value, onChange }, fieldState: { error } }) => (
                        <AppReactDatepicker
                          boxProps={{ className: 'is-full' }}
                          selected={value ? toDate(value) : undefined}
                          onChange={(date: Date) => onChange(formatISO(date))}
                          dateFormat='MMMM'
                          showMonthYearPicker
                          minDate={startMonthWatch ? toDate(startMonthWatch) : undefined}
                          customInput={<TextField fullWidth label='Bulan' placeholder='Pilih bulan' error={!!error} />}
                        />
                      )}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Controller
                      control={control}
                      name='endYear'
                      rules={{
                        validate: value => {
                          if (
                            isNullOrUndefined(value) &&
                            reportTypeWatch === ProfitAndLossReportType.PeriodComparison
                          ) {
                            return 'Wajib diisi'
                          }
                          return true
                        }
                      }}
                      render={({ field: { value, onChange }, fieldState: { error } }) => (
                        <AppReactDatepicker
                          boxProps={{ className: 'is-full' }}
                          selected={value ? toDate(value) : undefined}
                          onChange={(date: Date) => onChange(formatISO(date))}
                          dateFormat='yyyy'
                          showYearPicker
                          minDate={startYearWatch ? toDate(startYearWatch) : undefined}
                          customInput={<TextField fullWidth label='Tahun' placeholder='Pilih Tahun' error={!!error} />}
                        />
                      )}
                    />
                  </Grid>
                </>
              ),
              [BalanceSheetReportType.Consolidation]: (
                <Grid item xs={12}>
                  <Controller
                    control={control}
                    name='date'
                    rules={{
                      validate: value => {
                        if (isNullOrUndefined(value) && reportTypeWatch === ProfitAndLossReportType.Consolidation) {
                          return 'Wajib diisi'
                        }
                        return true
                      }
                    }}
                    render={({ field: { value, onChange }, fieldState: { error } }) => (
                      <AppReactDatepicker
                        boxProps={{ className: 'is-full' }}
                        selected={value ? toDate(value) : undefined}
                        onChange={(date: Date) => onChange(formatISO(date))}
                        dateFormat='dd/MM/yyyy'
                        minDate={startYearWatch ? toDate(startYearWatch) : undefined}
                        customInput={
                          <TextField fullWidth label='Per Tanggal' placeholder='Pilih Tanggal' error={!!error} />
                        }
                      />
                    )}
                  />
                </Grid>
              ),
              [BalanceSheetReportType.BudgetPeriod]: (
                <>
                  <Grid item xs={12} md={6}>
                    <Controller
                      control={control}
                      name='startMonth'
                      rules={{
                        validate: value => {
                          if (isNullOrUndefined(value) && reportTypeWatch === ProfitAndLossReportType.BudgetPeriod) {
                            return 'Wajib diisi'
                          }
                          return true
                        }
                      }}
                      render={({ field: { value, onChange }, fieldState: { error } }) => (
                        <AppReactDatepicker
                          boxProps={{ className: 'is-full' }}
                          selected={value ? toDate(value) : undefined}
                          onChange={(date: Date) => onChange(formatISO(date))}
                          dateFormat='MMMM'
                          showMonthYearPicker
                          customInput={
                            <TextField fullWidth label='Dari Periode' placeholder='Pilih bulan' error={!!error} />
                          }
                        />
                      )}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Controller
                      control={control}
                      name='startYear'
                      rules={{
                        validate: value => {
                          if (isNullOrUndefined(value) && reportTypeWatch === ProfitAndLossReportType.BudgetPeriod) {
                            return 'Wajib diisi'
                          }
                          return true
                        }
                      }}
                      render={({ field: { value, onChange }, fieldState: { error } }) => (
                        <AppReactDatepicker
                          boxProps={{ className: 'is-full' }}
                          selected={value ? toDate(value) : undefined}
                          onChange={(date: Date) => onChange(formatISO(date))}
                          dateFormat='YYYY'
                          showYearPicker
                          customInput={<TextField fullWidth label='Tahun' placeholder='Pilih Tahun' error={!!error} />}
                        />
                      )}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Controller
                      control={control}
                      name='endMonth'
                      rules={{
                        validate: value => {
                          if (isNullOrUndefined(value) && reportTypeWatch === ProfitAndLossReportType.BudgetPeriod) {
                            return 'Wajib diisi'
                          }
                          return true
                        }
                      }}
                      render={({ field: { value, onChange }, fieldState: { error } }) => (
                        <AppReactDatepicker
                          boxProps={{ className: 'is-full' }}
                          selected={value ? toDate(value) : undefined}
                          onChange={(date: Date) => onChange(formatISO(date))}
                          dateFormat='MMMM'
                          showMonthYearPicker
                          minDate={startMonthWatch ? toDate(startMonthWatch) : undefined}
                          customInput={
                            <TextField fullWidth label='Sampai Periode' placeholder='Pilih bulan' error={!!error} />
                          }
                        />
                      )}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Controller
                      control={control}
                      name='endYear'
                      rules={{
                        validate: value => {
                          if (isNullOrUndefined(value) && reportTypeWatch === ProfitAndLossReportType.BudgetPeriod) {
                            return 'Wajib diisi'
                          }
                          return true
                        }
                      }}
                      render={({ field: { value, onChange }, fieldState: { error } }) => (
                        <AppReactDatepicker
                          boxProps={{ className: 'is-full' }}
                          selected={value ? toDate(value) : undefined}
                          onChange={(date: Date) => onChange(formatISO(date))}
                          dateFormat='yyyy'
                          showYearPicker
                          minDate={startYearWatch ? toDate(startYearWatch) : undefined}
                          customInput={<TextField fullWidth label='Tahun' placeholder='Pilih Tahun' error={!!error} />}
                        />
                      )}
                    />
                  </Grid>
                </>
              ),
              [BalanceSheetReportType.IndukSkontro]: (
                <Grid item xs={12}>
                  <Controller
                    control={control}
                    name='date'
                    rules={{ required: true }}
                    render={({ field: { value, onChange }, fieldState: { error } }) => (
                      <AppReactDatepicker
                        boxProps={{ className: 'is-full' }}
                        selected={value ? toDate(value) : undefined}
                        onChange={(date: Date) => onChange(formatISO(date))}
                        dateFormat='dd/MM/yyyy'
                        customInput={
                          <TextField fullWidth label='Per Tanggal' placeholder='Pilih Tanggal' error={!!error} />
                        }
                      />
                    )}
                  />
                </Grid>
              )
            }[reportTypeWatch] || null}
            <Grid item xs={12}>
              <Controller
                control={control}
                name='siteId'
                rules={{ required: true }}
                render={({ field: { value, onChange }, fieldState: { error } }) => (
                  <FormControl fullWidth>
                    <InputLabel id='site-select'>Lokasi</InputLabel>
                    <Select
                      value={value}
                      onChange={onChange}
                      labelId='site-select'
                      id='site-select'
                      label='Lokasi'
                      placeholder='Pilih Lokasi'
                      error={!!error}
                    >
                      {ownSiteList?.map(site => (
                        <MenuItem key={site.id} value={site.id}>
                          {site.name}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                )}
              />
            </Grid>
            <Grid item xs={12}>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Controller
                    control={control}
                    name='showTotal'
                    render={({ field: { value, onChange } }) => (
                      <FormControlLabel
                        control={<Checkbox onChange={onChange} checked={!!value} />}
                        label='Hanya Tampilkan Total'
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12}>
                  <Controller
                    control={control}
                    name='showParentAccount'
                    render={({ field: { value, onChange } }) => (
                      <FormControlLabel
                        control={<Checkbox onChange={onChange} checked={!!value} />}
                        label='Tampilkan Akun Induk'
                      />
                    )}
                  />
                </Grid>
                <Grid item xs={12}>
                  <Controller
                    control={control}
                    name='showSubAccount'
                    render={({ field: { value, onChange } }) => (
                      <FormControlLabel
                        control={<Checkbox onChange={onChange} checked={!!value} />}
                        label='Tampilkan Sub Akun'
                      />
                    )}
                  />
                </Grid>
                <Grid item xs={12}>
                  <Controller
                    control={control}
                    name='showZeroBalance'
                    render={({ field: { value, onChange } }) => (
                      <FormControlLabel
                        control={<Checkbox onChange={onChange} checked={!!value} />}
                        label='Tampilkan data dengan saldo nol'
                      />
                    )}
                  />
                </Grid>
                <Grid item xs={12}>
                  <Controller
                    control={control}
                    name='showParentBalance'
                    render={({ field: { value, onChange } }) => (
                      <FormControlLabel
                        control={<Checkbox onChange={onChange} checked={!!value} />}
                        label='Tampilkan saldo akun induk'
                      />
                    )}
                  />
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </DialogContent>
      </form>
      <DialogActions className='justify-center pbs-0 sm:pbe-10 sm:px-16'>
        <Button variant='outlined' onClick={handleClose}>
          Batalkan
        </Button>
        <LoadingButton variant='contained' color='primary' onClick={handleSubmit(onSubmitFilter, console.error)}>
          Tampilkan
        </LoadingButton>
      </DialogActions>
    </Dialog>
  )
}

export default BalandeSheetDialog
