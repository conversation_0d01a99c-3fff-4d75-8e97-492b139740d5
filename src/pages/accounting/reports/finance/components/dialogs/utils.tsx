import { BalanceSheetReportType, CashFlowReportType, ProfitAndLossReportType } from '../../config/types'

export const cashFlowReportOptions = [
  {
    label: 'Metode Langsung',
    value: CashFlowReportType.DirectMethod
  },
  {
    label: 'Metode Tidak Langsung',
    value: CashFlowReportType.IndirectMethod
  },
  {
    label: 'Per Bulan - Langsung',
    value: CashFlowReportType.MonthlyDirect
  },
  {
    label: 'Per Bulan - Tidak Langsung',
    value: CashFlowReportType.MonthlyIndirect
  },
  {
    label: 'Rincian - Metode Tidak Langsung',
    value: CashFlowReportType.DetailedIndirect
  }
]

export const profitAndLossReportOptions = [
  {
    label: 'Standar',
    value: ProfitAndLossReportType.Standard
  },
  {
    label: 'Multi Periode',
    value: ProfitAndLossReportType.MultiPeriod
  },
  {
    label: 'Perbandingan Periode',
    value: ProfitAndLossReportType.PeriodComparison
  },
  {
    label: 'Konsolidasi',
    value: ProfitAndLossReportType.Consolidation
  },
  {
    label: 'Anggaran Periode',
    value: ProfitAndLossReportType.BudgetPeriod
  }
]

export const balanceSheetReportOptions = [
  {
    label: 'Standar',
    value: BalanceSheetReportType.Standard
  },
  {
    label: 'Multi Periode',
    value: BalanceSheetReportType.MultiPeriod
  },
  {
    label: 'Perbandingan Bulan',
    value: BalanceSheetReportType.MonthlyComparison
  },
  {
    label: 'Konsolidasi',
    value: BalanceSheetReportType.Consolidation
  },
  {
    label: 'Anggaran Periode',
    value: BalanceSheetReportType.BudgetPeriod
  },
  {
    label: 'Induk Skontro',
    value: BalanceSheetReportType.IndukSkontro
  }
]
