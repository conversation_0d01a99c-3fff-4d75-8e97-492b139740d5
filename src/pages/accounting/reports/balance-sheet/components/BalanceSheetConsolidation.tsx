import { Box, Card, CardContent, CardHeader, Typography } from '@mui/material'
import { classNames, isNullOrUndefined, toCurrency } from '@/utils/helper'
import { format } from 'date-fns'
import CustomDivider from '@/components/CustomDivider'
import { useBalanceSheet } from '../../context/BalanceSheetContext'
import { balanceSheetReportOptions } from '../../finance/components/dialogs/utils'

const BalanceSheetConsolidation = () => {
  const { site, filter, reportType, allSearchParams } = useBalanceSheet()

  const { startDate, endDate } = filter

  type RowVariant = 'section' | 'group' | 'item' | 'subtotal' | 'total' | 'group-total' | 'summary'
  type PLRow = {
    variant: RowVariant
    label: string
    amount?: number
    journal?: number
    indent?: number
    color?: 'default' | 'error'
  }

  // Template rows; replace amounts/descriptions with BE data
  const rows: PLRow[] = [
    { variant: 'section', label: 'Aktiva' },

    { variant: 'section', label: 'Aktiva Lancar', indent: 1 },

    {
      variant: 'group',
      label: 'Kas & Bank',
      journal: 500000,
      amount: 500000,
      indent: 2
    },
    {
      variant: 'item',
      label: 'Kas Samarinda',
      journal: *********,
      amount: *********,
      indent: 3
    },
    {
      variant: 'item',
      label: 'Kas Siluq Ngurai',
      journal: *********,
      amount: *********,
      indent: 3
    },

    { variant: 'group', label: 'Bank IDR', indent: 2 },
    {
      variant: 'item',
      label: 'Bank Mandiri Giro',
      journal: *********,
      amount: *********,
      indent: 3
    },
    {
      variant: 'item',
      label: 'Bank BRI',
      journal: *********,
      amount: *********,
      indent: 3
    },
    {
      variant: 'item',
      label: 'Bank BCA',
      journal: *********,
      amount: *********,
      indent: 3
    },
    {
      variant: 'item',
      label: 'Bank Mandiri Siluq Ngurai',
      journal: *********,
      amount: *********,
      indent: 3
    },

    {
      variant: 'subtotal',
      label: 'Jumlah Kas & Bank',
      amount: **********,
      journal: **********,
      indent: 2
    }
  ]

  const header = ['Jurnal Perusahaan', 'Jumlah']
  const totalColumns = header.length + 1

  const renderRow = (row: PLRow, idx: number) => {
    const ml = row.indent ? row.indent * 16 : 0

    switch (row.variant) {
      case 'section':
        return (
          <div key={idx} className={`col-span-${totalColumns} mt-2`} style={{ marginLeft: ml }}>
            <Typography className='font-semibold'>{row.label}</Typography>
          </div>
        )
      case 'group':
        return (
          <>
            <Typography
              key={`gl-${idx}`}
              className={classNames('font-semibold', row?.amount && row?.journal ? '' : `col-span-${totalColumns}`)}
              style={{ marginLeft: ml }}
            >
              {row.label}
            </Typography>
            {row?.amount && row?.journal && (
              <>
                <Typography
                  key={`r-${idx}`}
                  align='right'
                  className={row.journal && row.journal < 0 ? 'text-error font-semibold' : 'font-semibold'}
                >
                  {!isNullOrUndefined(row.journal) ? toCurrency(row.journal) : '-'}
                </Typography>
                <Typography
                  key={`r-${idx}`}
                  align='right'
                  className={row.amount && row.amount < 0 ? 'text-error font-semibold' : 'font-semibold'}
                >
                  {!isNullOrUndefined(row.amount) ? toCurrency(row.amount) : '-'}
                </Typography>
              </>
            )}
          </>
        )
      case 'item':
        return (
          <>
            <Typography key={`l-${idx}`} style={{ marginLeft: ml }}>
              {row.label}
            </Typography>
            <Typography key={`r-${idx}`} align='right'>
              {toCurrency(row.journal ?? 0)}
            </Typography>
            <Typography key={`r-${idx}`} align='right' className={row.amount && row.amount < 0 ? 'text-error' : ''}>
              {!isNullOrUndefined(row.amount) ? toCurrency(row.amount) : '-'}
            </Typography>
          </>
        )
      case 'group-total':
        return (
          <>
            <Typography key={`lgt-${idx}`} className='font-semibold' style={{ marginLeft: ml }}>
              {row.label}
            </Typography>
            <Typography key={`r-${idx}`} align='right'>
              {toCurrency(row.journal ?? 0)}
            </Typography>
            <Typography
              key={`rgt-${idx}`}
              align='right'
              className={`font-semibold ${row.amount && row.amount < 0 ? 'text-error' : ''}`}
            >
              {!isNullOrUndefined(row.amount) ? toCurrency(row.amount) : '-'}
            </Typography>
          </>
        )
      case 'subtotal':
        return (
          <>
            <div key={`d-${idx}`} className='col-start-2'>
              <CustomDivider color='var(--text-textPrimary)' variant='inset' />
            </div>
            <div key={`dd-${idx}`}>
              <CustomDivider variant='inset' color='var(--text-textPrimary)' />
            </div>
            <Typography key={`ls-${idx}`} className='font-semibold' style={{ marginLeft: ml }}>
              {row.label}
            </Typography>
            <Typography align='right'>{toCurrency(row.journal ?? 0)}</Typography>
            <Typography align='right' className={row.amount && row.amount < 0 ? 'text-error' : ''}>
              {!isNullOrUndefined(row.amount) ? toCurrency(row.amount) : '-'}
            </Typography>
          </>
        )
      case 'total':
        return (
          <>
            <div key={`d2-${idx}`} className='col-span-2'>
              <CustomDivider variant='inset' />
            </div>
            <Typography key={`lt-${idx}`} className='font-semibold'>
              {row.label}
            </Typography>
            <Typography key={`r-${idx}`} align='right'>
              {toCurrency(row.journal ?? 0)}
            </Typography>
            <Typography
              key={`rt-${idx}`}
              align='right'
              className={`font-semibold ${row.amount && row.amount < 0 ? 'text-error' : ''}`}
            >
              {!isNullOrUndefined(row.amount) ? toCurrency(row.amount) : '-'}
            </Typography>
          </>
        )
      default:
        return null
    }
  }

  return (
    <Card>
      <CardContent className='flex flex-col gap-5 py-8'>
        <div className='flex flex-col items-center justify-center'>
          <Typography variant='h5' color='primary'>
            PT Equalindo Makmur Alam Sejahtera
          </Typography>
          <Typography variant='h4'>
            Laporan Neraca ({balanceSheetReportOptions.find(opt => opt.value === reportType)?.label})
          </Typography>
          <Typography variant='caption' className='font-semibold text-textPrimary'>
            Per {allSearchParams?.date ? format(allSearchParams?.date, 'dd/MM/yyyy') : '-'}
          </Typography>
          <Typography className='font-semibold'>Lokasi: {site?.name}</Typography>
        </div>
        <Card className='mx-2'>
          <CardHeader
            title={
              <Box className={`grid grid-cols-${totalColumns}`}>
                <Typography className='font-normal text-textPrimary text-base'>Deskripsi</Typography>
                {header.map(p => (
                  <Typography align='right' className='font-normal text-textPrimary text-sm'>
                    {p}
                  </Typography>
                ))}
              </Box>
            }
            className='bg-[#DBF7E8]'
          />
          <CardContent className={`grid grid-cols-${totalColumns} gap-2 p-4 mt-4`}>
            {rows.map((row, idx) => renderRow(row, idx))}
          </CardContent>
        </Card>
      </CardContent>
    </Card>
  )
}

export default BalanceSheetConsolidation
