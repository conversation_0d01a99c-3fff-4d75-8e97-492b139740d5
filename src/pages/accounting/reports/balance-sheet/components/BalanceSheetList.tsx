import { Card, CardActions, CardContent, CardHeader, Divider, Typography } from '@mui/material'
import { toCurrency } from '@/utils/helper'
import { format } from 'date-fns'
import { id } from 'date-fns/locale'
import { useBalanceSheet } from '../../context/BalanceSheetContext'
import { balanceSheetReportOptions } from '../../finance/components/dialogs/utils'

const BalanceSheetList = () => {
  const { site, filter, reportType } = useBalanceSheet()

  const { startDate, endDate } = filter

  type RowVariant = 'section' | 'group' | 'item' | 'subtotal' | 'total' | 'group-total' | 'summary'
  type PLRow = {
    variant: RowVariant
    label: string
    amount?: number | null
    indent?: number
    color?: 'default' | 'error'
  }

  // Template rows; replace amounts/descriptions with BE data
  const rows: PLRow[] = [
    { variant: 'section', label: 'Aktiva' },

    { variant: 'group', label: 'Aktiva Lancar', indent: 1 },

    { variant: 'group', label: 'Kas & Bank', indent: 2 },
    { variant: 'item', label: 'Kas <PERSON>', amount: *********, indent: 3 },
    { variant: 'item', label: 'Kas Siluq Ngurai', amount: *********, indent: 3 },

    { variant: 'group', label: 'Bank IDR', indent: 2 },
    { variant: 'item', label: 'Bank Mandiri Giro', amount: *********, indent: 3 },
    { variant: 'item', label: 'Bank BRI', amount: *********, indent: 3 },
    { variant: 'item', label: 'Bank BCA', amount: *********, indent: 3 },
    { variant: 'item', label: 'Bank Mandiri Siluq Ngurai', amount: *********, indent: 3 },

    { variant: 'subtotal', label: 'Jumlah Kas & Bank', amount: **********, indent: 2 }
  ]

  // Calculate total equity at end of period
  const totalEquity = ********* + 0 // Ekuitas awal + total penambahan

  const renderRow = (row: PLRow, idx: number) => {
    const amountText = row.amount !== null && row.amount !== undefined ? toCurrency(row.amount) : '-'
    const ml = row.indent ? row.indent * 16 : 0

    switch (row.variant) {
      case 'section':
        return (
          <div key={idx} className='col-span-2 mt-2'>
            <Typography className='font-semibold'>{row.label}</Typography>
          </div>
        )
      case 'group':
        return (
          <div key={idx} className='col-span-2'>
            <Typography className='font-semibold' style={{ marginLeft: ml }}>
              {row.label}
            </Typography>
          </div>
        )
      case 'item':
        return (
          <>
            <Typography key={`l-${idx}`} style={{ marginLeft: ml }}>
              {row.label}
            </Typography>
            <Typography key={`r-${idx}`} align='right' className={row.amount && row.amount < 0 ? 'text-error' : ''}>
              {amountText}
            </Typography>
          </>
        )
      case 'group-total':
        return (
          <>
            <Typography key={`lgt-${idx}`} className='font-semibold' style={{ marginLeft: ml }}>
              {row.label}
            </Typography>
            <Typography
              key={`rgt-${idx}`}
              align='right'
              className={`font-semibold ${row.amount && row.amount < 0 ? 'text-error' : ''}`}
            >
              {amountText}
            </Typography>
          </>
        )
      case 'summary':
        return (
          <>
            <div key={`d-${idx}`} className='col-span-2'>
              <Divider variant='inset' />
            </div>
            <Typography key={`ls-${idx}`}>{row.label}</Typography>
            <Typography key={`rs-${idx}`} align='right' className={row.amount && row.amount < 0 ? 'text-error' : ''}>
              {amountText}
            </Typography>
          </>
        )
      case 'total':
        return (
          <>
            <div key={`d2-${idx}`} className='col-span-2'>
              <Divider variant='inset' />
            </div>
            <Typography key={`lt-${idx}`} className='font-semibold'>
              {row.label}
            </Typography>
            <Typography
              key={`rt-${idx}`}
              align='right'
              className={`font-semibold ${row.amount && row.amount < 0 ? 'text-error' : ''}`}
            >
              {amountText}
            </Typography>
          </>
        )
      default:
        return null
    }
  }

  return (
    <Card>
      <CardContent className='flex flex-col gap-5 py-8'>
        <div className='flex flex-col items-center justify-center'>
          <Typography variant='h5' color='primary'>
            PT Equalindo Makmur Alam Sejahtera
          </Typography>
          <Typography variant='h4'>
            Laporan Neraca ({balanceSheetReportOptions.find(opt => opt.value === reportType)?.label})
          </Typography>
          <Typography variant='caption' className='font-semibold text-textPrimary'>
            {/* {format(startDate, 'dd/MM/yyyy', { locale: id })} - {format(endDate, 'dd/MM/yyyy', { locale: id })} */}
          </Typography>
          <Typography className='font-semibold'>Lokasi: {site?.name}</Typography>
        </div>
        <Card className='mx-20'>
          <CardHeader
            action={
              <Typography align='right' className='font-normal text-textPrimary text-sm'>
                Nilai
              </Typography>
            }
            title={<Typography className='font-normal text-textPrimary text-base'>Deskripsi</Typography>}
            className='bg-[#DBF7E8]'
          />
          <CardContent className='grid grid-cols-2 gap-2 p-4 mt-4'>
            {rows.map((row, idx) => renderRow(row, idx))}
          </CardContent>
        </Card>
      </CardContent>
    </Card>
  )
}

export default BalanceSheetList
