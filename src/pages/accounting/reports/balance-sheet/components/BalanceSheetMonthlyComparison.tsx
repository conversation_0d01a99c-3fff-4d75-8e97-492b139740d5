import { <PERSON>, Card, CardContent, CardHeader, Typography } from '@mui/material'
import { classNames, isNullOrUndefined, toCurrency } from '@/utils/helper'
import CustomDivider from '@/components/CustomDivider'
import { useBalanceSheet } from '../../context/BalanceSheetContext'
import { balanceSheetReportOptions } from '../../finance/components/dialogs/utils'
import { monthFormatter } from '../../config/utils'

const BalanceSheetMonthlyComparison = () => {
  const { site, filter, reportType, allSearchParams } = useBalanceSheet()

  const { startDate, endDate } = filter

  type RowVariant = 'section' | 'group' | 'item' | 'subtotal' | 'total' | 'group-total' | 'summary'
  type PLRow = {
    variant: RowVariant
    label: string
    amount?: number[]
    indent?: number
    different?: number
    percents?: number
    color?: 'default' | 'error'
  }

  // Template rows; replace amounts/descriptions with BE data
  const rows: PLRow[] = [
    { variant: 'section', label: 'Aktiva' },

    { variant: 'section', label: 'Aktiva Lancar', indent: 1 },

    {
      variant: 'group',
      label: 'Kas & Bank',
      amount: [500000, 500000, 250000],
      different: 500000,
      percents: 0.1,
      indent: 2
    },
    {
      variant: 'item',
      label: 'Kas Samarinda',
      amount: [*********, *********, *********],
      indent: 3,
      different: 5000000,
      percents: 0.5
    },
    {
      variant: 'item',
      label: 'Kas Siluq Ngurai',
      amount: [*********, *********, *********],
      indent: 3,
      different: 5000000,
      percents: 0.5
    },

    { variant: 'group', label: 'Bank IDR', indent: 2 },
    {
      variant: 'item',
      label: 'Bank Mandiri Giro',
      amount: [*********, *********, *********],
      indent: 3,
      different: *********,
      percents: 0.5
    },
    {
      variant: 'item',
      label: 'Bank BRI',
      amount: [*********, **********, *********],
      indent: 3,
      different: *********,
      percents: 0.5
    },
    {
      variant: 'item',
      label: 'Bank BCA',
      amount: [*********, *********, *********],
      indent: 3,
      different: 0,
      percents: 0
    },
    {
      variant: 'item',
      label: 'Bank Mandiri Siluq Ngurai',
      amount: [*********, **********, *********],
      indent: 3,
      different: **********,
      percents: 0.5
    },

    {
      variant: 'subtotal',
      label: 'Jumlah Kas & Bank',
      amount: [**********, **********, **********],
      indent: 2,
      different: **********,
      percents: 1
    }
  ]

  const periods = ['Januari 2025', 'Februari 2025', 'Maret 2025']
  const totalColumns = periods.length + 3

  const gridTemplateColumns = `repeat(${totalColumns}, minmax(0, 1fr))`

  const renderRow = (row: PLRow, idx: number) => {
    const ml = row.indent ? row.indent * 16 : 0

    switch (row.variant) {
      case 'section':
        return (
          <div key={idx} className={`mt-2`} style={{ marginLeft: ml, gridColumn: `span ${totalColumns}` }}>
            <Typography className='font-semibold'>{row.label}</Typography>
          </div>
        )
      case 'group':
        return (
          <>
            <Typography
              key={`gl-${idx}`}
              className={classNames('font-semibold')}
              style={{
                marginLeft: ml,
                ...(row?.amount && row?.different && row?.percents ? {} : { gridColumn: `span ${totalColumns}` })
              }}
            >
              {row.label}
            </Typography>
            {row?.amount?.map(amount => (
              <Typography
                key={`r-${idx}`}
                align='right'
                className={amount && amount < 0 ? 'text-error font-semibold' : 'font-semibold'}
              >
                {!isNullOrUndefined(amount) ? toCurrency(amount) : '-'}
              </Typography>
            )) ?? null}
            {row?.different && row?.percents ? (
              <>
                <Typography key={`r-${idx}`} align='right' className='font-semibold'>
                  {toCurrency(row?.different ?? 0)}
                </Typography>
                <Typography key={`r-${idx}`} align='right' className='font-semibold'>
                  {row?.percents ?? 0} %
                </Typography>
              </>
            ) : null}
          </>
        )
      case 'item':
        return (
          <>
            <Typography key={`l-${idx}`} style={{ marginLeft: ml }}>
              {row.label}
            </Typography>
            {row.amount.map(amount => (
              <Typography key={`r-${idx}`} align='right' className={amount && amount < 0 ? 'text-error' : ''}>
                {!isNullOrUndefined(amount) ? toCurrency(amount) : '-'}
              </Typography>
            ))}
            <Typography key={`r-${idx}`} align='right'>
              {toCurrency(row.different ?? 0)}
            </Typography>
            <Typography key={`r-${idx}`} align='right'>
              {row.percents ?? 0} %
            </Typography>
          </>
        )
      case 'group-total':
        return (
          <>
            <Typography key={`lgt-${idx}`} className='font-semibold' style={{ marginLeft: ml }}>
              {row.label}
            </Typography>
            {row.amount.map(amount => (
              <Typography
                key={`rgt-${idx}`}
                align='right'
                className={`font-semibold ${amount && amount < 0 ? 'text-error' : ''}`}
              >
                {!isNullOrUndefined(amount) ? toCurrency(amount) : '-'}
              </Typography>
            ))}
            <Typography key={`r-${idx}`} align='right'>
              {toCurrency(row.different ?? 0)}
            </Typography>
            <Typography key={`r-${idx}`} align='right'>
              {row.percents ?? 0} %
            </Typography>
          </>
        )
      case 'subtotal':
        return (
          <>
            <div key={`dt-${idx}`}></div>
            {row.amount.map(() => (
              <div key={`d-${idx}`}>
                <CustomDivider color='var(--text-textPrimary)' variant='fullWidth' />
              </div>
            ))}
            <div key={`dd-${idx}`}>
              <CustomDivider variant='fullWidth' color='var(--text-textPrimary)' />
            </div>
            <div key={`dpd-${idx}`}>
              <CustomDivider variant='fullWidth' color='var(--text-textPrimary)' />
            </div>
            <Typography key={`ls-${idx}`} className='font-semibold' style={{ marginLeft: ml }}>
              {row.label}
            </Typography>
            {row.amount.map(amount => (
              <Typography align='right' className={amount && amount < 0 ? 'text-error' : ''}>
                {!isNullOrUndefined(amount) ? toCurrency(amount) : '-'}
              </Typography>
            ))}
            <Typography align='right'>{toCurrency(row.different ?? 0)}</Typography>
            <Typography align='right'>{row.percents ?? 0} %</Typography>
          </>
        )
      case 'total':
        return (
          <>
            <div key={`d2-${idx}`} className='col-span-2'>
              <CustomDivider variant='inset' />
            </div>
            <Typography key={`lt-${idx}`} className='font-semibold'>
              {row.label}
            </Typography>
            {row.amount.map((amount, i) => (
              <Typography
                key={`rt-${idx}-${i}`}
                align='right'
                className={`font-semibold ${amount && amount < 0 ? 'text-error' : ''}`}
              >
                {!isNullOrUndefined(amount) ? toCurrency(amount) : '-'}
              </Typography>
            ))}
            <Typography key={`r-${idx}`} align='right'>
              {toCurrency(row.different ?? 0)}
            </Typography>
            <Typography key={`r-${idx}`} align='right'>
              {row.percents ?? 0} %
            </Typography>
          </>
        )
      default:
        return null
    }
  }

  return (
    <Card>
      <CardContent className='flex flex-col gap-5 py-8'>
        <div className='flex flex-col items-center justify-center'>
          <Typography variant='h5' color='primary'>
            PT Equalindo Makmur Alam Sejahtera
          </Typography>
          <Typography variant='h4'>
            Laporan Neraca ({balanceSheetReportOptions.find(opt => opt.value === reportType)?.label})
          </Typography>
          <Typography variant='caption' className='font-semibold text-textPrimary'>
            {`${monthFormatter(allSearchParams?.startMonth)} ${allSearchParams?.startYear} - ${monthFormatter(allSearchParams?.endMonth)} ${allSearchParams?.endYear}`}
          </Typography>
          <Typography className='font-semibold'>Lokasi: {site?.name}</Typography>
        </div>
        <Card className='mx-2'>
          <CardHeader
            title={
              <Box className={`grid`} style={{ gridTemplateColumns }}>
                <Typography className='font-normal text-textPrimary text-base'>Deskripsi</Typography>
                {periods.map(p => (
                  <Typography align='right' className='font-normal text-textPrimary text-sm'>
                    {p}
                  </Typography>
                ))}
                <Typography align='right' className='font-normal text-textPrimary text-sm'>
                  Selisih
                </Typography>
                <Typography align='right' className='font-normal text-textPrimary text-sm'>
                  % Selisih
                </Typography>
              </Box>
            }
            className='bg-[#DBF7E8]'
          />
          <CardContent className={`grid gap-2 p-4 mt-4`} style={{ gridTemplateColumns }}>
            {rows.map((row, idx) => renderRow(row, idx))}
          </CardContent>
        </Card>
      </CardContent>
    </Card>
  )
}

export default BalanceSheetMonthlyComparison
