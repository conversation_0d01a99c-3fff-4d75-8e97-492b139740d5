import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ton, Grid, Typography } from '@mui/material'
import { Link } from 'react-router-dom'
import BalanceSheetList from './components/BalanceSheetList'
import { useBalanceSheet } from '../context/BalanceSheetContext'
import { BalanceSheetReportType } from '../finance/config/types'
import BalanceSheetMultiPeriod from './components/BalanceSheetMultiPeriod'
import BalanceSheetMonthlyComparison from './components/BalanceSheetMonthlyComparison'
import BalanceSheetConsolidation from './components/BalanceSheetConsolidation'
import BalanceSheetBudgetPeriod from './components/BalanceSheetBudgetPeriod'
import BalanceSheetListSkontro from './components/BalanceSheetListSkontro'

const BalanceSheetReport = () => {
  const { filter, reportType } = useBalanceSheet()

  const { startDate, endDate } = filter

  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Breadcrumbs>
          <Link to='#' replace>
            <Typography color='var(--color-text-disabled)'>Laporan</Typography>
          </Link>
          <Link to='/report/finance' replace>
            <Typography color='var(--color-text-disabled)'>Keuangan</Typography>
          </Link>
          <Typography>Detil Laporan</Typography>
        </Breadcrumbs>
      </Grid>
      <Grid item xs={12}>
        <div className='flex justify-between items-end'>
          <div className='flex flex-col'>
            <Typography variant='h4'>Detil Laporan Neraca</Typography>
            <Typography>
              {/* {format(startDate, 'dd/MM/yyyy', { locale: id })} - {format(endDate, 'dd/MM/yyyy', { locale: id })} */}
            </Typography>
          </div>
          <div className='flex gap-2'>
            <Button
              color='secondary'
              variant='outlined'
              startIcon={<i className='ri-upload-2-line' />}
              className='is-full sm:is-auto'
            >
              Ekspor
            </Button>
            <Button
              variant='contained'
              startIcon={<i className='ic-outline-local-printshop size-5' />}
              className='is-full sm:is-auto'
            >
              Cetak
            </Button>
          </div>
        </div>
      </Grid>
      <Grid item xs={12}>
        {{
          [BalanceSheetReportType.Standard]: <BalanceSheetList />,
          [BalanceSheetReportType.MultiPeriod]: <BalanceSheetMultiPeriod />,
          [BalanceSheetReportType.MonthlyComparison]: <BalanceSheetMonthlyComparison />,
          [BalanceSheetReportType.Consolidation]: <BalanceSheetConsolidation />,
          [BalanceSheetReportType.BudgetPeriod]: <BalanceSheetBudgetPeriod />,
          [BalanceSheetReportType.IndukSkontro]: <BalanceSheetListSkontro />
        }[reportType] || <BalanceSheetList />}
      </Grid>
    </Grid>
  )
}

export default BalanceSheetReport
