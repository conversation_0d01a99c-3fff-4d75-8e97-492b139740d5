import { Card, CardActions, CardContent, CardHeader, Divider, Typography } from '@mui/material'
import { ResponsiveBar } from '@nivo/bar'
import { format } from 'date-fns'
import { id } from 'date-fns/locale'
import { toCurrency } from '@/utils/helper'
import { useRevenueExpenseContext } from '../../context/RevenueExpenseContext'
import { useLiquidityRatio } from '../../context/LiquidityRatioContext'

const LiquidityRatioGraph = () => {
  const { site, filter } = useLiquidityRatio()

  const { startMonth, startYear, endMonth, endYear } = filter

  // Chart data based on the image - Kas & Bank vs Utang Usaha for 6 months
  const chartData = [
    {
      month: 'Jan 2025',
      'Kas & Bank': *********,
      'Utang Usaha': -********
    },
    {
      month: 'Feb 2025',
      'Kas & Bank': 1********,
      'Utang Usaha': -********
    },
    {
      month: 'Mar 2025',
      'Kas & Bank': *********,
      'Utang Usaha': -********
    },
    {
      month: 'Apr 2025',
      'Kas & Bank': ********,
      'Utang Usaha': -********
    },
    {
      month: 'Mei 2025',
      'Kas & Bank': *********,
      'Utang Usaha': -********
    },
    {
      month: 'Jun 2025',
      'Kas & Bank': ********,
      'Utang Usaha': 1000000
    }
  ]

  return (
    <Card>
      <CardContent className='flex flex-col gap-5 py-8'>
        <div className='flex flex-col items-center justify-center'>
          <Typography variant='h5' color='primary'>
            PT Equalindo Makmur Alam Sejahtera
          </Typography>
          <Typography variant='h4'>Grafik Rasio Liquiditas</Typography>
          <Typography variant='caption' className='font-semibold text-textPrimary'>
            {startMonth}/{startYear} - {endMonth}/{endYear}
          </Typography>
          <Typography className='font-semibold'>Lokasi: {site?.name}</Typography>
        </div>
        <Card className='mx-20'>
          <CardContent className='p-4 mt-4'>
            <div style={{ height: '400px' }}>
              <ResponsiveBar
                data={chartData}
                keys={['Kas & Bank', 'Utang Usaha']}
                indexBy='month'
                margin={{ top: 50, right: 60, bottom: 80, left: 60 }}
                padding={0.3}
                groupMode='grouped'
                valueScale={{ type: 'linear' }}
                indexScale={{ type: 'band', round: true }}
                colors={['#8884d8', '#ff6b6b']}
                axisTop={null}
                axisRight={null}
                axisBottom={{
                  tickSize: 5,
                  tickPadding: 5,
                  tickRotation: 0,
                  legend: 'Bulan',
                  legendPosition: 'middle',
                  legendOffset: 32
                }}
                axisLeft={{
                  tickSize: 5,
                  tickPadding: 5,
                  tickRotation: 0,
                  legend: 'Nilai (Rp)',
                  legendPosition: 'middle',
                  legendOffset: -40,
                  format: (value: number) => toCurrency(value)
                }}
                labelSkipWidth={12}
                labelSkipHeight={12}
                legends={[
                  {
                    dataFrom: 'keys',
                    anchor: 'bottom',
                    direction: 'row',
                    justify: false,
                    translateX: 0,
                    translateY: 50,
                    itemsSpacing: 20,
                    itemWidth: 120,
                    itemHeight: 20,
                    itemDirection: 'left-to-right',
                    itemOpacity: 0.85,
                    symbolSize: 20,
                    effects: [
                      {
                        on: 'hover',
                        style: {
                          itemOpacity: 1
                        }
                      }
                    ]
                  }
                ]}
                animate={true}
                tooltip={({ id, value }: any) => (
                  <div
                    style={{
                      padding: 12,
                      color: 'white',
                      background: '#333',
                      borderRadius: '4px'
                    }}
                  >
                    <strong>{id}:</strong> {toCurrency(value)}
                  </div>
                )}
              />
            </div>
          </CardContent>
        </Card>
      </CardContent>
    </Card>
  )
}

export default LiquidityRatioGraph
