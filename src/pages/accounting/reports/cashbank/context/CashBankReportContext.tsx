import { ChildrenType } from '@/core/types'
import React, { createContext, useState } from 'react'
import { CashBankReportType } from '../config/types'
import BankBookDialog from '../components/dialogs/bank-book-dialog'
import CashBankReportDialog from '../components/dialogs/cashbank-account-dialog'
import SummaryLedgerDialog from '../components/dialogs/ledger-summary-dialog'
import SummaryLedgerDetailDialog from '../components/dialogs/ledger-summary-detailed-dialog'
import RealizedPNLDialog from '../components/dialogs/realized-profit-loss-dialog'
import UnrealizedPNLDialog from '../components/dialogs/unrealized-profit-loss-dialog'
import BalanceSheetDialog from '../components/dialogs/balance-sheet-dialog'
import CashFlowPerAccountDialog from '../components/dialogs/cash-flow-per-account-dialog'
import CashFlowProjectionDialog from '../components/dialogs/cash-flow-projection-dialog'
import ReconciliationDialog from '../components/dialogs/reconsiliation-dialog'
import OutstandingGIRODialog from '../components/dialogs/outstanding-giro-dialog'

type CashBankContextProps = {
  handleReportClick: (type: CashBankReportType) => void
}

const CashBankReportContext = createContext<CashBankContextProps>({} as CashBankContextProps)

export const useCashBankReport = () => {
  const context = React.useContext(CashBankReportContext)
  if (context === undefined) {
    throw new Error('useFinanceReport must be used within a FinanceReportContext')
  }
  return context
}

export const CashBankReportProvider = ({ children }: ChildrenType) => {
  const [
    {
      ACCOUNT_LIST,
      BANK_BOOK,
      CASH_FLOW_PER_ACCOUNT,
      CASH_FLOW_PROJECTION_DETAIL,
      BANK_RECONCILIATION,
      OUTSTANDING_GIRO
    },
    setDialog
  ] = useState<{
    [CashBankReportType.ACCOUNT_LIST]?: boolean
    [CashBankReportType.BANK_BOOK]?: boolean
    [CashBankReportType.CASH_FLOW_PER_ACCOUNT]?: boolean
    [CashBankReportType.CASH_FLOW_PROJECTION_DETAIL]?: boolean
    [CashBankReportType.BANK_RECONCILIATION]?: boolean
    [CashBankReportType.OUTSTANDING_GIRO]?: boolean
  }>({})

  const handleReportClick = (type: CashBankReportType) => {
    setDialog(current => ({ ...current, [type]: !current[type] }))
  }

  const value = {
    handleReportClick
  }
  return (
    <CashBankReportContext.Provider value={value}>
      {[
        {
          key: 'ACCOUNT_LIST',
          Dialog: CashBankReportDialog
        },
        {
          key: 'BANK_BOOK',
          Dialog: BankBookDialog
        },
        {
          key: 'CASH_FLOW_PER_ACCOUNT',
          Dialog: CashFlowPerAccountDialog
        },
        {
          key: 'CASH_FLOW_PROJECTION_DETAIL',
          Dialog: CashFlowProjectionDialog
        },
        {
          key: 'BANK_RECONCILIATION',
          Dialog: ReconciliationDialog
        },
        {
          key: 'OUTSTANDING_GIRO',
          Dialog: OutstandingGIRODialog
        }
        // {
        //   key: 'REALIZED_PROFIT_LOSS',
        //   Dialog: RealizedPNLDialog
        // },
        // {
        //   key: 'UNREALIZED_PROFIT_LOSS',
        //   Dialog: UnrealizedPNLDialog
        // },
        // {
        //   key: 'TRIAL_BALANCE',
        //   Dialog: BalanceSheetDialog
        // }
      ].map(
        ({ key, Dialog }) =>
          !!eval(key) && (
            <Dialog open={eval(key)} onClose={() => setDialog(current => ({ ...current, [key]: false }))} key={key} />
          )
      )}
      {children}
    </CashBankReportContext.Provider>
  )
}
