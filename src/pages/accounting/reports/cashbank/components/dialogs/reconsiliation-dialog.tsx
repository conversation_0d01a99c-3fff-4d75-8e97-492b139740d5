import AppReactDatepicker from '@/components/libs/styles/AppReactDatepicker'
import { DialogProps } from '../../../finance/config/types'
import {
  Autocomplete,
  Button,
  debounce,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography
} from '@mui/material'
import { Controller, useForm, useWatch } from 'react-hook-form'
import { useAuth } from '@/contexts/AuthContext'
import { useRouter } from '@/routes/hooks'
import { format, formatISO, toDate } from 'date-fns'
import LoadingButton from '@mui/lab/LoadingButton'
import { useLocation } from 'react-router-dom'
import { useState } from 'react'
import { AccountType } from '@/types/accountTypes'
import { useQuery } from '@tanstack/react-query'
import AccountsQueryMethods, { ACCOUNT_LIST_QUERY_KEY } from '@/api/services/account/query'
import CompanyQueryMethods, { PROJECT_LIST_QUERY_KEY } from '@/api/services/company/query'
import { defaultListData } from '@/api/queryClient'
import { ProjectType } from '@/types/projectTypes'
import { ListResponse } from '@/types/api'

const ReconciliationDialog = ({ open, onClose }: DialogProps) => {
  const { ownSiteList } = useAuth()
  const { pathname } = useLocation()

  const [itemQuery, setItemQuery] = useState('')
  const [selectedItem, setSelectedItem] = useState<AccountType>()
  const { control, handleSubmit, reset, getValues } = useForm()
  const router = useRouter()

  const startDateWatch = useWatch({
    control,
    name: 'startDate'
  })

  const { data: accountListResponse, remove: removeAccountListResponse } = useQuery({
    // enabled: !!itemQuery,
    queryKey: [ACCOUNT_LIST_QUERY_KEY, itemQuery, 'PAYMENT'],
    queryFn: () =>
      AccountsQueryMethods.getAccountList({
        limit: Number.MAX_SAFE_INTEGER,
        level: 1,
        search: itemQuery,
        accountTypeIds: 'CASH_BANK'
      })
  })

  const { data: projectListResponse } = useQuery({
    queryKey: [PROJECT_LIST_QUERY_KEY],
    queryFn: () => CompanyQueryMethods.getProjectList({ limit: Number.MAX_SAFE_INTEGER }),
    placeholderData: defaultListData as ListResponse<ProjectType>
  })

  const handleClose = () => {
    onClose(false)
  }

  const onSubmitFilter = inputValues => {
    if (inputValues.date) {
      inputValues.date = format(inputValues.date, 'MM-dd-yyyy')
    } else {
      delete inputValues.date
    }
    router.push(`${pathname}/reconciliation?${new URLSearchParams(inputValues).toString()}`)
    handleClose()
  }

  return (
    <Dialog fullWidth maxWidth='sm' scroll='body' open={open} onClose={handleClose}>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-16'>
        Laporan Rekonsiliasi Bank
        <Typography component='span' className='flex flex-col text-center'>
          Pilih parameter yang akan ditampilkan di laporan ini
        </Typography>
      </DialogTitle>
      <form onSubmit={e => e.preventDefault()}>
        <DialogContent className='overflow-visible pbs-0 sm:pbe-6 sm:px-16'>
          <IconButton onClick={handleClose} className='absolute block-start-4 inline-end-4'>
            <i className='ri-close-line text-textSecondary' />
          </IconButton>
          <Grid container spacing={4}>
            <Grid item xs={12}>
              <Controller
                control={control}
                name='date'
                rules={{ required: true }}
                render={({ field: { value, onChange }, fieldState: { error } }) => (
                  <AppReactDatepicker
                    boxProps={{ className: 'is-full' }}
                    selected={value ? toDate(value) : undefined}
                    onChange={(date: Date) => onChange(formatISO(date))}
                    dateFormat='dd/MM/yyyy'
                    customInput={
                      <TextField fullWidth label='Per Tanggal' placeholder='Pilih tanggal' error={!!error} />
                    }
                  />
                )}
              />
            </Grid>
            <Grid item xs={12}>
              <Controller
                control={control}
                name='accountId'
                rules={{ required: true }}
                render={({ field, fieldState: { error } }) => (
                  <>
                    <Autocomplete
                      key={JSON.stringify(selectedItem)}
                      value={selectedItem}
                      onInputChange={debounce((e, newValue, reason) => {
                        if (reason === 'input') {
                          setItemQuery(newValue as string)
                        }
                      }, 700)}
                      options={accountListResponse?.items ?? []}
                      getOptionLabel={(option: AccountType) => `[${option.code}] ${option.name}`}
                      freeSolo={!itemQuery}
                      noOptionsText='Akun tidak ditemukan'
                      onChange={(e, newValue: AccountType) => {
                        if (newValue) {
                          setSelectedItem(newValue)
                          field.onChange(newValue.id)
                          removeAccountListResponse()
                        }
                      }}
                      renderInput={params => (
                        <TextField
                          {...params}
                          InputProps={{
                            ...params.InputProps,
                            onKeyDown: e => {
                              if (e.key === 'Enter') {
                                e.stopPropagation()
                              }
                            }
                          }}
                          error={!!error}
                          placeholder='Cari akun perkiraan'
                          label='Akun Kas/Bank'
                        />
                      )}
                    />
                  </>
                )}
              />
            </Grid>
            <Grid item xs={12}>
              <Controller
                control={control}
                name='projectId'
                rules={{ required: true }}
                render={({ field: { value, onChange }, fieldState: { error } }) => (
                  <FormControl fullWidth>
                    <InputLabel id='site-select'>Proyek</InputLabel>
                    <Select
                      value={value}
                      onChange={onChange}
                      labelId='site-select'
                      id='site-select'
                      label='Proyek'
                      placeholder='Pilih Proyek'
                      error={!!error}
                    >
                      {projectListResponse?.items?.map(project => (
                        <MenuItem key={project.id} value={project.id}>
                          {project.name}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                )}
              />
            </Grid>
          </Grid>
        </DialogContent>
      </form>
      <DialogActions className='justify-center pbs-0 sm:pbe-10 sm:px-16'>
        <Button variant='outlined' onClick={handleClose}>
          Batalkan
        </Button>
        <LoadingButton variant='contained' color='primary' onClick={handleSubmit(onSubmitFilter)}>
          Tampilkan
        </LoadingButton>
      </DialogActions>
    </Dialog>
  )
}

export default ReconciliationDialog
