import { CashBankReportOption, CashBankReportType } from './types'

export const ledgerReportOptions: CashBankReportOption[] = [
  {
    label: 'Daftar Akun Kas & Bank',
    description: 'Lihat laporan daftar akun kas & bank',
    href: '/report/cash-bank/account-list',
    type: CashBankReportType.ACCOUNT_LIST
  },
  {
    label: 'Buku Bank',
    description: 'Lihat laporan pencatatan akun bank',
    href: '/report/cash-bank/bank-book',
    type: CashBankReportType.BANK_BOOK
  },
  {
    label: 'Arus Kas Per Akun',
    description: 'Lihat rincian laporan arus kas pemasukan/pengeluaran per akun',
    href: '/report/cash-bank/cash-flow-per-account',
    type: CashBankReportType.CASH_FLOW_PER_ACCOUNT
  },
  {
    label: 'Rincian Proyeksi Arus Kas',
    description: 'Lihat laporan terperinci proyeksi arus kas',
    href: '/report/cash-bank/cash-flow-projection-detail',
    type: CashBankReportType.CASH_FLOW_PROJECTION_DETAIL
  },
  {
    label: 'Rekonsiliasi Bank',
    description: 'Lihat laporan rekonsiliasi bank berdasarkan tanggal jurnal',
    href: '/report/cash-bank/bank-reconciliation',
    type: CashBankReportType.BANK_RECONCILIATION
  },
  {
    label: 'Giro Mundur – Belum Jatuh Tempo',
    description: 'Lihat laporan cek yang belum jatuh tempo',
    href: '/report/cash-bank/outstanding-giro',
    type: CashBankReportType.OUTSTANDING_GIRO
  }
]
