import { CategoryType } from '@/types/companyTypes'
import { Card, CardContent, Typography } from '@mui/material'

type Props = {
  data: CategoryType
}

const DetailCard = ({ data }: Props) => {
  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Detil Kategori</Typography>
        </div>
        <div className='flex flex-col gap-2'>
          <small>Kode Kategori</small>
          <Typography>{data?.code}</Typography>
        </div>
        <div className='flex flex-col gap-2'>
          <small><PERSON><PERSON></small>
          <Typography>{data?.name}</Typography>
        </div>
        <div className='flex flex-col gap-2'>
          <small>Jumlah Barang dalam Kategori</small>
          <Typography>0 Barang</Typography>
        </div>
      </CardContent>
    </Card>
  )
}

export default DetailCard
