import { Link, useParams } from 'react-router-dom'
import { useCategory } from '@/pages/company-data/category/context/CategoryContext'
import { Breadcrumbs, Button, Grid, Typography } from '@mui/material'
import DetailCard from './components/DetailCard'
import CreatedByCard from './components/CreatedByCard'
import AccountsCard from './components/AccountsCard'

const ServiceCategoryDetailPage = () => {
  const params = useParams()
  const { setSelectedCategoryId, categoryData, accountCategoryData, setAddCategoryOpen, handleRemoveCategory } =
    useCategory()

  const handleEditCategory = () => {
    setSelectedCategoryId(params?.catId)
    setTimeout(() => {
      setAddCategoryOpen(true)
    }, 300)
  }

  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Breadcrumbs>
          <Link to='#' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Material/Barang</Typography>
          </Link>
          <Link to='/company-data/category/item' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Kategori Barang</Typography>
          </Link>
          <Typography>Detil Kategori</Typography>
        </Breadcrumbs>
      </Grid>
      <Grid item xs={12}>
        <div className='flex flex-col md:flex-row justify-between gap-2 items-end sm:flex-row max-sm:items-center is-full sm:is-auto'>
          <div className='flex flex-col items-start max-sm:text-center'>
            <Typography className='text-left md:text-justify' variant='h4'>
              {categoryData?.name}
            </Typography>
            <Typography>Kode Kategori {categoryData?.code}</Typography>
          </div>
          <div className='flex flex-col gap-2 sm:flex-row is-full sm:is-auto'>
            <div className='flex gap-2'>
              {/* <Button
                color='secondary'
                variant='outlined'
                startIcon={<i className='ri-upload-2-line' />}
                className='is-full sm:is-auto'
              >
                Ekspor
              </Button>
              <Button
                color='secondary'
                variant='outlined'
                startIcon={<i className='ic-outline-local-printshop' />}
                className='is-full sm:is-auto'
              >
                Cetak
              </Button> */}
            </div>
            <Button
              color='error'
              variant='outlined'
              className='is-full sm:is-auto'
              onClick={() => handleRemoveCategory(categoryData?.id)}
            >
              Hapus
            </Button>
            <Button variant='contained' className='is-full sm:is-auto' onClick={handleEditCategory}>
              Edit Kategori
            </Button>
          </div>
        </div>
      </Grid>
      <Grid item xs={12} md={6}>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <DetailCard data={categoryData} />
          </Grid>
          <Grid item xs={12}>
            <AccountsCard categoryData={categoryData} />
          </Grid>
        </Grid>
      </Grid>
      <Grid item xs={12} md={6}>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <CreatedByCard />
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  )
}

export default ServiceCategoryDetailPage
