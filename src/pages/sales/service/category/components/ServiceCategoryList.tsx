import { useCallback, useEffect, useRef, useState } from 'react'
import { Box, ClickAwayListener, Fade, IconButton, Paper, Popper, Typography } from '@mui/material'
import Card from '@mui/material/Card'
import Button from '@mui/material/Button'

import {
  getCoreRowModel,
  useReactTable,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFacetedMinMaxValues,
  getPaginationRowModel,
  getSortedRowModel
} from '@tanstack/react-table'

import DebouncedInput from '@/components/DebounceInput'
import Table from '@/components/table'
import { useExportState } from '@/core/hooks/useExportImport'
import { ExportImportScope } from '@/types/exportImportTypes'
import { ListParams } from '@/types/payload'
import ProcessingImportDialog from '@/components/dialogs/export-dialog'
import ImportDialog from '@/components/dialogs/import-dialog'
import MobileDropDown from '@/components/layout/shared/components/MobileDropDown'
import { useRouter } from '@/routes/hooks'
import { useCategory } from '@/pages/company-data/category/context/CategoryContext'
import { tableColumns } from '@/pages/company-data/category/config/table'

const ServiceCategoryList = () => {
  const router = useRouter()
  const {
    categoryListResponse: { items: categoryList, totalItems, totalPages, limit: limitItems, page: pageItems },
    categoryParams: { page, search, limit },
    setPartialCategoryParams,
    setAddCategoryOpen,
    setCategoryParams,
    setSelectedCategoryId,
    isMobile
  } = useCategory()

  // export boilerplate - start
  const [exportDialogOpen, setExportDialogOpen] = useState(false)
  const [actionBtn, setBtn] = useState<boolean>(false)
  const [searchExtend, setSearchExtend] = useState<boolean>(false)
  const onExportSuccess = useCallback((success: boolean) => {
    if (success) {
      setExportDialogOpen(true)
    }
  }, [])
  const { exportFn, isExporting } = useExportState<ListParams>(ExportImportScope.CATEGORY, onExportSuccess)

  // export boilerplate - end
  const [importDialogOpen, setImportDialogOpen] = useState(false)

  const handleImportSubmit = () => {
    setImportDialogOpen(false)
  }

  // TODO: MOVE THIS SHIT
  const table = useReactTable({
    data: categoryList,
    columns: tableColumns({
      edit: id => {
        router.push(`/sales/service/category/${id}`)
        // setSelectedCategoryId(id)
        // setTimeout(() => {
        //   setAddCategoryOpen(true)
        // }, 300)
      }
    }),
    initialState: {
      pagination: {
        pageSize: limit ?? 10,
        pageIndex: page - 1
      }
    },
    state: {
      pagination: {
        pageSize: limitItems ?? 10,
        pageIndex: pageItems - 1
      }
    },
    manualPagination: true,
    rowCount: totalItems,
    pageCount: totalPages,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getFacetedMinMaxValues: getFacetedMinMaxValues()
  })

  useEffect(() => {
    setPartialCategoryParams('type', 'ITEM_SERVICE')
  }, [])

  const actionButton = (
    <>
      <Button
        color='secondary'
        variant='outlined'
        startIcon={<i className='ri-upload-2-line' />}
        className='is-full sm:is-auto'
        onClick={() => exportFn({ search, type: 'ITEM' })}
        disabled={isExporting}
      >
        Ekspor
      </Button>
      <Button
        color='primary'
        variant='outlined'
        startIcon={<i className='mdi-file-document-outline' />}
        className='is-full sm:is-auto'
        onClick={() => setImportDialogOpen(true)}
        disabled={isExporting}
      >
        Impor List
      </Button>
    </>
  )

  return (
    <>
      <ProcessingImportDialog
        open={exportDialogOpen}
        onClose={() => setExportDialogOpen(false)}
        contentScope='Kategori'
      />
      <ImportDialog
        open={importDialogOpen}
        scope={ExportImportScope.CATEGORY}
        onSubmit={handleImportSubmit}
        setOpen={() => setImportDialogOpen(!importDialogOpen)}
        type='ITEM'
      />
      <Card>
        <div className='flex justify-between gap-4 p-5 flex-row items-center'>
          {searchExtend ? (
            <div className='flex gap-4 items-center is-full flex-col sm:flex-row'>
              <DebouncedInput
                value={search}
                onChange={value => setCategoryParams(prev => ({ ...prev, page: 1, search: value as string }))}
                onBlur={() => setSearchExtend(false)}
                placeholder='Cari'
                className='is-full'
              />
            </div>
          ) : !isMobile ? (
            <div className='flex gap-4 items-center is-full sm:is-auto flex-col sm:flex-row'>
              <DebouncedInput
                value={search}
                onChange={value => setCategoryParams(prev => ({ ...prev, page: 1, search: value as string }))}
                placeholder='Cari'
                className='is-full sm:is-auto'
              />
            </div>
          ) : (
            <IconButton onClick={() => setSearchExtend(true)} className='bg-[#4C4E640D]/5 rounded-[8px]'>
              <i className='ri-search-line' />
            </IconButton>
          )}
          {!searchExtend && (
            <div className='flex items-center justify-end md:justify-between gap-x-4 max-sm:gap-y-4 is-full flex-row sm:is-auto'>
              {!isMobile ? (
                actionButton
              ) : (
                <IconButton onClick={() => setBtn(true)} className='bg-[#4C4E640D]/5 rounded-[8px]'>
                  <i className='pepicons-pop--dots-y' />
                </IconButton>
              )}
              <Button
                variant='contained'
                onClick={() => setAddCategoryOpen(true)}
                startIcon={<i className='ic-baseline-add-circle-outline size-5' />}
                className='is-auto sm:is-auto'
              >
                {isMobile ? 'Tambah' : 'Tambah Kategori'}
              </Button>
            </div>
          )}
        </div>
        <Table
          table={table}
          emptyLabel={
            <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
              <Typography> Belum ada Kategori</Typography>
              <Typography className='text-sm text-gray-400'>
                Semua kategori yang telah dibuat akan ditampilkan di sini
              </Typography>
            </td>
          }
          onRowsPerPageChange={pageSize => {
            if (pageSize > totalItems) {
              setCategoryParams(prev => ({ ...prev, limit: totalItems, page: 1 }))
            } else {
              setPartialCategoryParams('limit', pageSize)

              const maxPage = Math.ceil(totalItems / pageSize)
              if (page > maxPage) {
                setCategoryParams(prev => ({ ...prev, page: maxPage }))
              }
            }
          }}
          onPageChange={pageIndex => setPartialCategoryParams('page', pageIndex)}
        />
      </Card>
      <MobileDropDown open={actionBtn} onClose={() => setBtn(false)} onOpen={() => setBtn(true)}>
        <Typography sx={{ marginTop: 2 }} align='center' variant='h5'>
          Action
        </Typography>
        <Box className='flex gap-2 p-4 pb-2 flex-col'>{actionButton}</Box>
      </MobileDropDown>
    </>
  )
}

export default ServiceCategoryList
