import { useEffect, useState } from 'react'
import { IconButton, Typography } from '@mui/material'
import Card from '@mui/material/Card'
import Button from '@mui/material/Button'

import {
  getCoreRowModel,
  useReactTable,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFacetedMinMaxValues,
  getPaginationRowModel,
  getSortedRowModel
} from '@tanstack/react-table'

import DebouncedInput from '@/components/DebounceInput'
import Table from '@/components/table'
import { tableColumns } from '../config/table'
import { useService } from '../context/ServiceContext'
import { useRouter } from '@/routes/hooks'

const ServiceList = () => {
  const router = useRouter()
  const {
    itemListResponse,
    itemsParams,
    setItemsParams,
    setSelectedItemId,
    setPartialItemsParams,
    handleRemoveItem,
    setAddItemOpen,
    isMobile
  } = useService()

  const { items: itemList, totalItems, totalPages, page: pageItems, limit: limitItems } = itemListResponse
  const { page, search, limit } = itemsParams

  const [searchExtend, setSearchExtend] = useState<boolean>(false)

  const table = useReactTable({
    data: itemList,
    columns: tableColumns({
      showDetail: id => {
        setSelectedItemId(id)
        setAddItemOpen(true)
      },
      remove: handleRemoveItem
    }),
    initialState: {
      pagination: {
        pageSize: limit ?? 10,
        pageIndex: page - 1
      }
    },
    state: {
      pagination: {
        pageSize: limitItems,
        pageIndex: pageItems - 1
      }
    },
    manualPagination: true,
    rowCount: totalItems,
    pageCount: totalPages,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getFacetedMinMaxValues: getFacetedMinMaxValues()
  })

  useEffect(() => {
    setItemsParams({
      limit: 10,
      page: 1
    })
    setSelectedItemId(undefined)
  }, [])

  return (
    <>
      <Card>
        <div className='flex justify-between gap-4 p-5 flex-row items-start sm:flex-row sm:items-center'>
          <div className='flex gap-4 items-center is-auto sm:is-auto flex-row sm:flex-row'>
            {searchExtend ? (
              <div className='flex gap-4 items-center is-full flex-col sm:flex-row'>
                <DebouncedInput
                  value={search}
                  onChange={value => setItemsParams(prev => ({ ...prev, page: 1, search: value as string }))}
                  onBlur={() => setSearchExtend(false)}
                  placeholder='Cari'
                  className='is-full'
                />
              </div>
            ) : !isMobile ? (
              <div className='flex gap-4 items-center is-full sm:is-auto flex-col sm:flex-row'>
                <DebouncedInput
                  value={search}
                  onChange={value => setItemsParams(prev => ({ ...prev, page: 1, search: value as string }))}
                  placeholder='Cari'
                  className='is-full sm:is-auto'
                />
              </div>
            ) : (
              <IconButton onClick={() => setSearchExtend(true)} className='bg-[#4C4E640D]/5 rounded-[8px]'>
                <i className='ri-search-line' />
              </IconButton>
            )}
          </div>
          {!searchExtend && (
            <div className='flex items-center justify-end gap-x-4 max-sm:gap-y-4 is-full flex-row sm:is-auto sm:flex-row'>
              <Button
                variant='contained'
                onClick={() => {
                  setSelectedItemId(undefined)
                  setAddItemOpen(true)
                }}
                className='sm:is-auto'
              >
                {isMobile ? 'Tambah' : 'Tambah Jasa'}
              </Button>
            </div>
          )}
        </div>
        <Table
          table={table}
          emptyLabel={
            <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
              <Typography> Belum ada Jasa</Typography>
              <Typography className='text-sm text-gray-400'>
                Semua jasa yang telah dibuat akan ditampilkan di sini
              </Typography>
            </td>
          }
          onRowsPerPageChange={pageSize => {
            if (pageSize > totalItems) {
              setItemsParams(prev => ({ ...prev, limit: totalItems, page: 1 }))
            } else {
              setPartialItemsParams('limit', pageSize)

              const maxPage = Math.ceil(totalItems / pageSize)
              if (page > maxPage) {
                setItemsParams(prev => ({ ...prev, page: maxPage }))
              }
            }
          }}
          onPageChange={pageIndex => setPartialItemsParams('page', pageIndex)}
        />
      </Card>
    </>
  )
}

export default ServiceList
