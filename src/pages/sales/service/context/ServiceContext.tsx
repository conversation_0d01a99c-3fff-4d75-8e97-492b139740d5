import { create<PERSON>ontext, <PERSON>actN<PERSON>, useContext, useState } from 'react'
import { QueryObserverResult, RefetchOptions, RefetchQueryFilters, useQuery } from '@tanstack/react-query'
import { ItemParams, ListParams } from '@/types/payload'
import usePartialState from '@/core/hooks/usePartialState'
import { ListResponse } from '@/types/api'
import { defaultListData } from '@/api/queryClient'
import { ItemType, ItemTypes } from '@/types/companyTypes'
import CompanyQueryMethods, {
  ITEM_LIST_QUERY_KEY,
  ITEM_QUERY_KEY,
  SERVICE_LIST_QUERY_KEY,
  SERVICE_QUERY_KEY
} from '@/api/services/company/query'
import { useMenu } from '@/components/menu/contexts/menuContext'
import { useDeleteItem } from '@/api/services/company/mutation'
import { toast } from 'react-toastify'
import { useParams } from 'react-router-dom'
import { useRouter } from '@/routes/hooks'
import useMobileScreen from '@/components/dialogs/hooks/useMobileScreen'
import AddServiceDialog from '@/components/dialogs/add-service-dialog'

export interface ServiceContextProps {
  showLoading: boolean
  itemListResponse: ListResponse<ItemType>
  itemsParams: ItemParams
  setItemsParams: React.Dispatch<React.SetStateAction<ListParams>>
  fetchItemList: <TPageData>(
    options?: RefetchOptions & RefetchQueryFilters<TPageData>
  ) => Promise<QueryObserverResult<ListResponse<ItemType>, unknown>>
  itemData: ItemType
  clearItemData: () => void
  selectedItemId?: string
  setSelectedItemId: React.Dispatch<React.SetStateAction<string>>
  setPartialItemsParams: (fieldName: string, value: any) => void
  fetchItemData: <TPageData>(
    options?: RefetchOptions & RefetchQueryFilters<TPageData>
  ) => Promise<QueryObserverResult<ItemType, unknown>>
  handleRemoveItem: (id: string) => void
  addItemOpen: boolean
  setAddItemOpen: React.Dispatch<React.SetStateAction<boolean>>
  isMobile: boolean
}

export const ServiceContext = createContext<ServiceContextProps>({} as ServiceContextProps)

interface ServiceContextProviderProps {
  children: ReactNode
}

export const useService = () => {
  return useContext(ServiceContext)
}

export function ServiceContextProvider({ children }: ServiceContextProviderProps) {
  const router = useRouter()
  const { itemId } = useParams()
  const { isMobile } = useMobileScreen()
  const [selectedItemId, setSelectedItemId] = useState<string>()
  const [addItemOpen, setAddItemOpen] = useState(false)

  const [itemsParams, setPartialItemsParams, setItemsParams] = usePartialState<ItemParams>(
    {
      limit: 10,
      page: 1
    },
    'SERVICE_PARAMS'
  )

  const { setConfirmState } = useMenu()

  const { mutate: removeItemMutate } = useDeleteItem()

  const {
    data: itemData,
    refetch: fetchItemData,
    isFetching: fetchItemDataLoading,
    remove: removeItemData
  } = useQuery({
    enabled: !!selectedItemId,
    queryKey: [SERVICE_QUERY_KEY, selectedItemId],
    queryFn: () => CompanyQueryMethods.getItem(selectedItemId)
  })

  const {
    data: itemListResponse,
    refetch: fetchItemList,
    isFetching: fetchItemsLoading
  } = useQuery({
    queryKey: [SERVICE_LIST_QUERY_KEY, JSON.stringify(itemsParams)],
    queryFn: () => {
      const { search, ...params } = itemsParams
      return CompanyQueryMethods.getItemList({
        ...(search && { search }),
        type: ItemTypes.SERVICE,
        ...params
      })
    },
    placeholderData: defaultListData as ListResponse<ItemType>
  })

  const clearItemData = () => {
    setSelectedItemId(undefined)
    removeItemData()
  }

  const handleRemoveItem = (id: string) => {
    setConfirmState({
      open: true,
      title: 'Hapus Jasa',
      content: 'Apakah Anda yakin ingin menghapus jasa ini?',
      confirmText: 'Hapus',
      confirmColor: 'error',
      onConfirm: () => {
        removeItemMutate(id, {
          onSuccess: () => {
            if (itemId) {
              router.replace('/accounting/jasa')
            }
            fetchItemList()
            toast.success('Jasa berhasil dihapus')
          }
        })
      }
    })
  }

  const value: ServiceContextProps = {
    itemListResponse,
    showLoading: fetchItemsLoading || fetchItemDataLoading,
    fetchItemList,
    itemsParams,
    setPartialItemsParams,
    setItemsParams,
    itemData,
    selectedItemId,
    setSelectedItemId,
    clearItemData,
    fetchItemData,
    handleRemoveItem,
    addItemOpen,
    setAddItemOpen,
    isMobile
  }

  return (
    <ServiceContext.Provider value={value}>
      <>
        {children}
        {addItemOpen && <AddServiceDialog {...value} open={addItemOpen} setOpen={setAddItemOpen} />}
      </>
    </ServiceContext.Provider>
  )
}
