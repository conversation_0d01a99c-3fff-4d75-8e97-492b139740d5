// MUI Imports

import Grid from '@mui/material/Grid'

// Type Imports
import { Typography } from '@mui/material'

import ServiceList from './components/ServiceList'
import { ServiceContextProvider } from './context/ServiceContext'

const ServiceListPage = () => {
  return (
    <ServiceContextProvider>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <div className='flex justify-between items-end'>
            <div className='flex flex-col'>
              <Typography variant='h4'>List Jasa</Typography>
              <Typography>Semua jasa yang sudah terdaftar akan ditampilkan di sini</Typography>
            </div>
          </div>
        </Grid>
        <Grid item xs={12}>
          <ServiceList />
        </Grid>
      </Grid>
    </ServiceContextProvider>
  )
}

export default ServiceListPage
