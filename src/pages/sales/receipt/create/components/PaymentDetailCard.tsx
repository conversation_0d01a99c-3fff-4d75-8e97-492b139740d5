import AccountsQueryMethods, { ACCOUNT_LIST_QUERY_KEY } from '@/api/services/account/query'
import AppReactDatepicker from '@/components/libs/styles/AppReactDatepicker'
import { AccountType } from '@/types/accountTypes'
import {
  Autocomplete,
  Card,
  CardContent,
  debounce,
  FormControl,
  FormHelperText,
  Grid,
  InputLabel,
  ListItemText,
  ListSubheader,
  MenuItem,
  Select,
  TextField,
  Typography
} from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { useState } from 'react'
import { Controller, useFormContext, useWatch } from 'react-hook-form'
import { useFilePicker } from 'use-file-picker'
import CurrencyField from '@/components/numeric/CurrencyField'
import { CreateCashReceiptPayload } from '@/pages/cash-bank/receipt/config/types'
import CompanyQueryMethods, { PROJECT_LABEL_LIST_QUERY_KEY } from '@/api/services/company/query'
import { defaultListData } from '@/api/queryClient'
import { ListResponse } from '@/types/api'
import { ProjectLabelType } from '@/types/projectTypes'
import useMobileScreen from '@/components/dialogs/hooks/useMobileScreen'
import { useAuth } from '@/contexts/AuthContext'
import { SalesInvoice } from '@/types/salesInvoiceTypes'

type Props = {
  invoiceData?: SalesInvoice
}

const ReceiptDetailCard = ({ invoiceData }: Props) => {
  const { isMobile } = useMobileScreen()
  const [selectedItem, setSelectedItem] = useState<AccountType>()
  const [itemQuery, setItemQuery] = useState('')
  const { control } = useFormContext<CreateCashReceiptPayload>()
  const { groupedSiteList, departmentList } = useAuth()
  const { openFilePicker, filesContent } = useFilePicker({
    readAs: 'DataURL',
    accept: 'image/jpeg, image/png'
  })

  const siteIdWatch = useWatch({
    control,
    name: 'siteId',
    defaultValue: ''
  })

  const { data: itemsListResponse, remove: removeItemsListResponse } = useQuery({
    // enabled: !!itemQuery,
    queryKey: [ACCOUNT_LIST_QUERY_KEY, itemQuery, 'CASH_RECEIPT'],
    queryFn: () =>
      AccountsQueryMethods.getAccountList({
        limit: Number.MAX_SAFE_INTEGER,
        level: 1,
        search: itemQuery,
        accountTypeIds: 'CASH_BANK'
      })
  })

  const {
    data: { items: projectLabels }
  } = useQuery({
    queryKey: [PROJECT_LABEL_LIST_QUERY_KEY, siteIdWatch],
    enabled: !!siteIdWatch,
    queryFn: async () => {
      return CompanyQueryMethods.getProjectLabelList({ limit: 100, siteId: siteIdWatch })
    },
    placeholderData: defaultListData as ListResponse<ProjectLabelType>
  })

  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-4'>
          <div className='flex justify-between'>
            <Typography variant='h5'>Detil Penerimaan</Typography>
          </div>
          <Grid container spacing={4}>
            <Grid item xs={12} md={6}>
              <Controller
                control={control}
                name='transactionDate'
                rules={{ required: true }}
                render={({ field, fieldState: { error } }) => (
                  <AppReactDatepicker
                    selected={field.value ? new Date(field.value) : null}
                    onChange={(date: Date | null) => field.onChange(date?.toISOString().split('T')[0])}
                    placeholderText='Pilih tanggal'
                    dateFormat='dd/MM/yyyy'
                    customInput={
                      <TextField
                        fullWidth
                        label='Tanggal Bayar'
                        error={!!error}
                        helperText={error?.message}
                        InputProps={{
                          readOnly: true
                        }}
                      />
                    }
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Controller
                control={control}
                name='accountId'
                rules={{ required: true }}
                render={({ field, fieldState: { error } }) => (
                  <Autocomplete
                    key={JSON.stringify(selectedItem)}
                    value={selectedItem}
                    onInputChange={debounce((e, newValue, reason) => {
                      if (reason === 'input') {
                        setItemQuery(newValue as string)
                      }
                    }, 700)}
                    options={itemsListResponse?.items ?? []}
                    getOptionLabel={(option: AccountType) => `[${option.code}] ${option.name}`}
                    freeSolo={!itemQuery}
                    noOptionsText='Akun tidak ditemukan'
                    onChange={(e, newValue: AccountType) => {
                      if (newValue) {
                        setSelectedItem(newValue)
                        field.onChange(newValue.id)
                        removeItemsListResponse()
                      }
                    }}
                    renderInput={params => (
                      <TextField
                        {...params}
                        InputProps={{
                          ...params.InputProps,
                          onKeyDown: e => {
                            if (e.key === 'Enter') {
                              e.stopPropagation()
                            }
                          }
                        }}
                        error={!!error}
                        placeholder='Cari akun perkiraan'
                        label='Diterima di Akun'
                      />
                    )}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Controller
                name='siteId'
                control={control}
                rules={{ required: true }}
                render={({ field: { value, onChange }, fieldState: { error } }) => (
                  <FormControl fullWidth error={!!error} size={isMobile ? 'small' : undefined}>
                    <InputLabel id='role-select'>Lokasi</InputLabel>
                    <Select
                      key={value}
                      fullWidth
                      id='select-siteId'
                      value={value}
                      onChange={e => onChange((e.target as HTMLInputElement).value)}
                      label='Lokasi'
                      labelId='siteId-select'
                      inputProps={{ placeholder: 'Pilih Lokasi' }}
                      defaultValue=''
                    >
                      {groupedSiteList.map(group => {
                        let children = []
                        children.push(
                          <ListSubheader
                            className='bg-green-50 text-primary font-semibold'
                            key={group.projectId ?? 'no_project'}
                          >
                            {group.project?.name || 'Tanpa Proyek'}
                          </ListSubheader>
                        )
                        group.sites.forEach(site => {
                          children.push(
                            <MenuItem key={site.id} value={site.id}>
                              <ListItemText primary={site.name} />
                            </MenuItem>
                          )
                        })
                        return children
                      })}
                    </Select>
                    {error && <FormHelperText>{error.message}</FormHelperText>}
                  </FormControl>
                )}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Controller
                name='departmentId'
                control={control}
                rules={{ required: true }}
                render={({ field: { value, onChange }, fieldState: { error } }) => (
                  <FormControl fullWidth error={!!error} size={isMobile ? 'small' : undefined}>
                    <InputLabel id='role-select'>Departemen</InputLabel>
                    <Select
                      key={value}
                      fullWidth
                      id='select-departmentId'
                      value={value}
                      onChange={e => onChange((e.target as HTMLInputElement).value)}
                      label='Departemen'
                      labelId='departmentId-select'
                      inputProps={{ placeholder: 'Pilih Departemen' }}
                      defaultValue=''
                    >
                      {departmentList?.map(department => (
                        <MenuItem key={department.id} value={department.id}>
                          {department.name}
                        </MenuItem>
                      ))}
                    </Select>
                    {error && <FormHelperText>{error.message}</FormHelperText>}
                  </FormControl>
                )}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Controller
                control={control}
                name='projectLabelId'
                render={({ field: { value, onChange }, fieldState: { error } }) => (
                  <FormControl fullWidth error={!!error}>
                    <InputLabel id='select-projectLabelId'>Label Proyek</InputLabel>
                    <Select
                      key={value}
                      fullWidth
                      id='select-projectLabelId'
                      value={value}
                      disabled={!siteIdWatch}
                      // readOnly={!!searchParams.get('projectLabelId')}
                      onChange={e => onChange((e.target as HTMLInputElement).value)}
                      label='Label Proyek'
                      labelId='select-projectLabelId'
                      inputProps={{ placeholder: 'Pilih Label' }}
                    >
                      {projectLabels?.map(label => (
                        <MenuItem key={label.id} value={label.id}>
                          {label.name}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                )}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                label='No Voucher'
                fullWidth
                helperText='No. Voucher akan terisi otomatis jika tidak diinput'
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Controller
                control={control}
                name='memo'
                render={({ field }) => <TextField {...field} fullWidth label='Memo' />}
              />
            </Grid>
            {!invoiceData && (
              <Grid item xs={12} md={6}>
                <Controller
                  control={control}
                  name='items'
                  render={({ field: { onChange, value } }) => {
                    const totalAmount = value?.reduce((acc, item) => acc + item.amount, 0)
                    return (
                      <TextField
                        label='Nominal Diterima'
                        fullWidth
                        value={totalAmount}
                        InputProps={{
                          inputComponent: CurrencyField as any,
                          inputProps: { prefix: invoiceData?.currency?.symbol }
                        }}
                      />
                    )
                  }}
                />
              </Grid>
            )}
            {/* Currently not used */}
            {/* <Grid item xs={12} md={6}>
              <AppReactDatepicker
                boxProps={{ className: 'is-full' }}
                // selected={value ? toDate(value) : undefined}
                // onChange={(date: Date) => onChange(formatISO(date))}
                dateFormat='eeee dd/MM/yyyy'
                customInput={
                  <TextField
                    fullWidth
                    label='Tanggal Bayar'
                    className='flex-1'
                    InputProps={{
                      readOnly: true
                    }}
                  />
                }
              />
            </Grid> */}
            {/* <Grid item xs={12} md={6}>
              <Controller
                control={control}
                name='useCheckNumber'
                render={({ field }) => (
                  <FormControlLabel
                    label={<Typography>Gunakan Nomor Cek</Typography>}
                    control={<Checkbox {...field} />}
                  />
                )}
              />
            </Grid> */}
            {/* {!!useCheckNumber && (
              <Grid item xs={12} md={6}>
                <Controller
                  control={control}
                  name='checkNumber'
                  rules={{
                    validate: value => {
                      if (isNullOrUndefined(value) && !!useCheckNumber) {
                        return 'Silahkan isi nomor cek'
                      }
                      return true
                    }
                  }}
                  render={({ field, fieldState: { error } }) => (
                    <TextField fullWidth label='Nomor Cek' {...field} error={!!error} />
                  )}
                />
              </Grid>
            )} */}
            {/* Currently not used */}
            {/* <Grid item xs={12} md={6}>
              <div className='flex flex-col gap-2'>
                <Typography variant='subtitle1' marginBottom={2}>
                  Unggah Bukti Transaksi
                </Typography>
                <div className='flex items-center gap-4'>
                  <TextField
                    size='small'
                    fullWidth
                    value={filesContent?.[0]?.name}
                    placeholder='Tidak ada file dipilih'
                    aria-readonly
                    className='flex-1'
                  />
                  <Button variant='contained' onClick={() => openFilePicker()}>
                    Unggah
                  </Button>
                </div>
              </div>
            </Grid> */}
          </Grid>
        </CardContent>
      </Card>
    </>
  )
}

export default ReceiptDetailCard
