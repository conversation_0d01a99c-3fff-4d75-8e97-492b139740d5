import { SalesInvoice } from '@/types/salesInvoiceTypes'
import { toCurrency } from '@/utils/helper'
import { Card, CardContent, Typography } from '@mui/material'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'

type Props = {
  invoiceData: SalesInvoice
}

const InvoiceCard = ({ invoiceData }: Props) => {
  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Penerimaan dari <PERSON>aktur</Typography>
        </div>
        <div className='flex p-4 flex-col gap-3 w-full rounded-md bg-[#4C4E640D]'>
          <Typography variant='h4'>No. Faktur: {invoiceData?.number}</Typography>
          <Typography>
            {invoiceData?.createdAt ? formatDate(invoiceData?.createdAt, 'eeee, dd/MM/yyyy', { locale: id }) : '-'}
          </Typography>
        </div>
        <div className='grid grid-cols-2 md:grid-cols-3 gap-4 w-full'>
          <div className='p-3 flex flex-col gap-2 w-full rounded-md bg-[#4C4E640D]'>
            <small>Sub Total Faktur</small>
            <Typography>{toCurrency(invoiceData?.subTotalAmount, false, invoiceData?.currency?.code)}</Typography>
          </div>
          <div className='p-3 flex flex-col gap-2 w-full rounded-md bg-[#4C4E640D]'>
            <small>Biaya Lain lain</small>
            <Typography>
              {toCurrency(
                invoiceData?.otherExpense?.reduce((acc, current) => acc + current.amount, 0) ?? 0,
                false,
                invoiceData?.currency?.code
              )}
            </Typography>
          </div>
          {/* <div className='p-3 flex flex-col gap-2 w-full rounded-md bg-[#4C4E640D]'>
            <small>Diskon Faktur</small>
            <Typography>{toCurrency(invoiceData?.discountAmount, false, invoiceData?.currency?.code)}</Typography>
          </div> */}
          <div className='p-3 flex flex-col gap-2 w-full rounded-md bg-[#DBF7E8]'>
            <small>Total Faktur</small>
            <Typography variant='h5' color='primary' className='font-semibold'>
              {toCurrency(invoiceData?.totalAmount, false, invoiceData?.currency?.code)}
            </Typography>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default InvoiceCard
