import { useState } from 'react'
import { Link } from 'react-router-dom'

// MUI Imports
import { B<PERSON>c<PERSON>bs, Chip, Typography, Button, Grid } from '@mui/material'

// Third-party Imports
import { format } from 'date-fns'
import { id } from 'date-fns/locale'

// Components Imports
import DialogDetailJournal from '@/components/dialogs/detail-journal-dialog'
import ApprovalDetailCard from '@/pages/material-request/detail/components/ApprovalDetailCard'
import Permission from '@/core/components/Permission'

// Hooks Imports
import { useAuth } from '@/contexts/AuthContext'
import { useRouter } from '@/routes/hooks'

// Context Imports
import { SalesInvoiceContextProvider, useSalesInvoice } from '../context/SalesInvoiceContext'
import SalesInvoiceDetailCard from './components/SalesInvoiceDetailCard'
import DocumentCard from './components/DocumentCard'
import ItemsCard from './components/ItemsCard'
import PaymentSummaryCard from './components/PaymentSummaryCard'
import OtherExpenseCard from './components/OtherExpenseCard'
import { saveAs } from 'file-saver'
import { pdf } from '@react-pdf/renderer'
import InvoicePdfDocument from './components/InvoicePdfDocument'
import { statusChipValue } from '../list/config/utils'
import ActivityLogCard from './components/ActivityLogCard'
import { SalesInvoiceStatuses } from '@/types/salesInvoiceTypes'

const SalesInvoiceDetailPage = () => {
  const { userProfile, ownSiteList } = useAuth()
  const router = useRouter()
  const { salesInvoiceData, isApprovalPage, salesInvoiceLogs } = useSalesInvoice()
  const [selectedJournalId, setSelectedJournalId] = useState<string | null>(null)

  const handleExport = async () => {
    const blob = await pdf(<InvoicePdfDocument salesInvoice={salesInvoiceData} />).toBlob()
    const filename = salesInvoiceData?.number + '.pdf'
    saveAs(blob, filename)
  }

  const handlePrint = async isPreview => {
    const blob = await pdf(
      <InvoicePdfDocument
        salesInvoice={salesInvoiceData}
        isPreview={isPreview}
        // qrCode={qr}
      />
    ).toBlob()
    const url = URL.createObjectURL(blob)
    const printWindow = window.open(url, '_blank')
    printWindow.onload = () => {
      printWindow.print()
      printWindow.onafterprint = () => {
        printWindow.close()
      }
    }
  }

  return (
    <>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <Breadcrumbs aria-label='breadcrumb'>
            <Link to='#' replace>
              <Typography color='var(--mui-palette-text-disabled)'>Faktur Penjualan</Typography>
            </Link>
            <Link to='/sales/invoice/list' replace>
              <Typography color='var(--mui-palette-text-disabled)'>Terbuat</Typography>
            </Link>
            <Typography>Detil Faktur</Typography>
          </Breadcrumbs>
        </Grid>
        <Grid item xs={12}>
          <div className='flex justify-between items-end flex-col sm:flex-row max-sm:items-center gap-2'>
            <div className='flex flex-col'>
              <div className='flex items-center gap-2 flex-col sm:flex-row max-sm:text-center'>
                <Typography variant='h4'>No. Faktur: {salesInvoiceData?.number}</Typography>
                <Chip
                  label={statusChipValue[salesInvoiceData?.status]?.label}
                  color={statusChipValue[salesInvoiceData?.status]?.color}
                  variant='tonal'
                  size='small'
                />
              </div>
              <Typography className='max-sm:text-center max-sm:mt-2'>
                {format(salesInvoiceData?.createdAt ?? Date.now(), 'eeee dd/MM/yyyy', { locale: id })}
              </Typography>
            </div>
            <div className='flex gap-2 is-full sm:is-auto'>
              {/* <Button color='error' onClick={() => router.back()} variant='outlined'>
                Hapus Faktur
              </Button> */}
              {salesInvoiceData?.status === SalesInvoiceStatuses.APPROVED ? (
                <>
                  <Button
                    color='secondary'
                    variant='outlined'
                    startIcon={<i className='ri-upload-2-line' />}
                    className='is-full sm:is-auto'
                    onClick={handleExport}
                  >
                    Ekspor
                  </Button>
                  <Button
                    color='secondary'
                    variant='outlined'
                    startIcon={<i className='ri-printer-line' />}
                    className='is-full sm:is-auto'
                    onClick={handlePrint}
                  >
                    Cetak
                  </Button>
                  <Button
                    variant='contained'
                    className='is-full sm:is-auto'
                    onClick={() => router.push(`/cash-bank/receipt/create?invoiceIds=${salesInvoiceData?.id}`)}
                  >
                    Buat Penerimaan
                  </Button>
                </>
              ) : (
                <Button
                  color='secondary'
                  variant='outlined'
                  startIcon={<i className='ri-printer-line' />}
                  className='is-full sm:is-auto'
                  onClick={() => handlePrint(true)}
                >
                  Preview Cetak
                </Button>
              )}

              {salesInvoiceData?.journalId && (
                <Permission permission={['journal.create']}>
                  <Button
                    color='secondary'
                    variant='outlined'
                    startIcon={<i className='ri-eye-line' />}
                    className='is-full sm:is-auto'
                    onClick={() => setSelectedJournalId(salesInvoiceData?.journalId)}
                  >
                    Cek Jurnal
                  </Button>
                </Permission>
              )}
            </div>
          </div>
        </Grid>

        {/* Left Column */}
        <Grid item xs={12} md={6}>
          <Grid container spacing={4}>
            {/* Sales Invoice Detail Card */}
            <Grid item xs={12}>
              <SalesInvoiceDetailCard />
            </Grid>
          </Grid>
        </Grid>
        {/* Right Column */}
        <Grid item xs={12} md={6}>
          <Grid container spacing={4}>
            {/* Document Card */}
            <Grid item xs={12}>
              <DocumentCard />
            </Grid>

            {/* Approvals Card */}
            {(salesInvoiceData?.approvals?.length ?? 0) > 0 && (
              <Grid item xs={12}>
                <ApprovalDetailCard approvalList={salesInvoiceData?.approvals ?? []} />
              </Grid>
            )}
          </Grid>
        </Grid>

        <Grid item xs={12}>
          <Grid container spacing={4}>
            {/* Items Card */}
            <Grid item xs={12}>
              <ItemsCard />
            </Grid>

            {/* Other Expense Card */}
            <Grid item xs={12}>
              <OtherExpenseCard />
            </Grid>

            {/* Payment Summary Card */}
            <Grid item xs={12}>
              <PaymentSummaryCard />
            </Grid>

            {/* Activity Log Card */}
            <Grid item xs={12}>
              <ActivityLogCard logList={salesInvoiceLogs} />
            </Grid>
          </Grid>
        </Grid>
      </Grid>
      {!!selectedJournalId && (
        <DialogDetailJournal
          open={!!selectedJournalId}
          setOpen={open => setSelectedJournalId(!open && null)}
          journalId={selectedJournalId}
        />
      )}
    </>
  )
}

const SalesInvoiceDetailPageWithProvider = () => {
  return (
    <SalesInvoiceContextProvider>
      <SalesInvoiceDetailPage />
    </SalesInvoiceContextProvider>
  )
}

export default SalesInvoiceDetailPageWithProvider
