import { useMemo } from 'react'

// MUI Imports
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'
import Divider from '@mui/material/Divider'
import { createColumnHelper, getCoreRowModel, useReactTable } from '@tanstack/react-table'

// Components Imports
import Table from '@/components/table'

// Context Imports
import { useSalesInvoice } from '../../context/SalesInvoiceContext'

// Types Imports
import { SalesInvoiceItem, SalesInvoiceTaxTypes } from '@/types/salesInvoiceTypes'
import { toCurrency } from '@/utils/helper'

const columnHelper = createColumnHelper<SalesInvoiceItem>()

const ItemsCard = () => {
  const { salesInvoiceData } = useSalesInvoice()

  const columns = useMemo(
    () => [
      columnHelper.accessor('item.name', {
        header: 'Jasa',
        cell: info => (
          <div className='flex flex-col'>
            <Typography color='text.primary' className='font-medium'>
              {info.getValue()}
            </Typography>
            <Typography variant='body2'>{info.row.original.item?.number}</Typography>
            {info.row.original.note && <Typography variant='caption'>Note: {info.row.original.note}</Typography>}
          </div>
        )
      }),
      columnHelper.accessor('quantity', {
        header: 'Kuantitas',
        cell: info => (
          <Typography>
            {info.getValue()} {info.row.original.quantityUnit}
          </Typography>
        )
      }),
      columnHelper.accessor('pricePerUnit', {
        header: 'Harga Satuan',
        cell: info => <Typography>{toCurrency(info.getValue(), false, salesInvoiceData?.currency?.code)}</Typography>
      }),
      columnHelper.accessor('subTotalDiscount', {
        header: 'Diskon',
        cell: info => (
          <Typography>( {toCurrency(info.getValue(), false, salesInvoiceData?.currency?.code)} )</Typography>
        )
      }),
      columnHelper.accessor('taxAmount', {
        header: 'Pajak',
        cell: info => (
          <div>
            <Typography
              className={`${info.row?.original?.taxType === SalesInvoiceTaxTypes.INCLUDE_TAX ? 'text-gray-400' : ''}`}
            >
              {toCurrency(info.getValue(), false, salesInvoiceData?.currency?.code)}
            </Typography>
            {info.row?.original?.taxType === SalesInvoiceTaxTypes.INCLUDE_TAX && (
              <Typography variant='caption' className='text-xxs'>
                *Harga Item Termasuk Pajak
              </Typography>
            )}
          </div>
        )
      }),
      columnHelper.accessor('totalAmount', {
        header: 'TOTAL HARGA',
        cell: info => (
          <Typography className='text-primary'>
            {toCurrency(info.getValue(), false, salesInvoiceData?.currency?.code)}
          </Typography>
        )
      })
    ],
    [salesInvoiceData?.currency?.code]
  )

  const table = useReactTable({
    data: salesInvoiceData?.items ?? [],
    columns,
    getCoreRowModel: getCoreRowModel(),
    initialState: {
      pagination: {
        pageSize: 100
      }
    }
  })

  return (
    <Card>
      <CardContent>
        <Typography variant='h5' className='font-medium mb-6'>
          Detil Jasa
        </Typography>
        <Table table={table} headerColor='green' />
      </CardContent>
    </Card>
  )
}

export default ItemsCard
