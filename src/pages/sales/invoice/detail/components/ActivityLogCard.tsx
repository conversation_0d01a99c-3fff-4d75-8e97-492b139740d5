// MUI Imports
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import TimelineDot from '@mui/lab/TimelineDot'
import TimelineItem from '@mui/lab/TimelineItem'
import TimelineContent from '@mui/lab/TimelineContent'
import TimelineSeparator from '@mui/lab/TimelineSeparator'
import TimelineConnector from '@mui/lab/TimelineConnector'
import Typography from '@mui/material/Typography'

import { Timeline } from '@/components/Timeline'
import { Avatar } from '@mui/material'
import { formatDistanceToNow } from 'date-fns'
import { id } from 'date-fns/locale'
import { SalesInvoiceLog, SalesInvoiceLogStatuses, SalesInvoiceStatuses } from '@/types/salesInvoiceTypes'

type Props = {
  logList?: SalesInvoiceLog[]
}

const ActivityLogCard = ({ logList = [] }: Props) => {
  return (
    <Card>
      <CardHeader title='Log Aktivitas' />
      <CardContent>
        <Timeline>
          {logList?.map(log => {
            let dotColor: 'primary' | 'secondary' | 'error' | 'warning' | 'info' | 'success' | 'inherit' | 'grey' =
              'primary'
            let title = ''

            let changes: string[] = []
            if (log.changes) {
              try {
                const parsed = JSON.parse(log.changes) as string[]
                if (Array.isArray(parsed)) {
                  changes = parsed.map(change =>
                    change.replaceAll('"', '').replaceAll('{changed}', '→').replaceAll('{added}', '')
                  )
                }
              } catch {
                // ignore parse errors
              }
            }

            switch (log.status) {
              case SalesInvoiceLogStatuses.CREATED:
                title = 'Faktur Dibuat'
                break
              case SalesInvoiceLogStatuses.UPDATED:
                title = 'Detil Faktur di-edit'
                break
              case SalesInvoiceLogStatuses.APPROVED:
                title = ''
                break
              case SalesInvoiceLogStatuses.REJECTED:
                title = 'Faktur Ditolak'
                dotColor = 'error'
                break
              case SalesInvoiceLogStatuses.CLOSED:
                title = 'Faktur Ditutup'
                dotColor = 'error'
                break
              case SalesInvoiceLogStatuses.APPROVAL_UPDATED:
                title = 'Penerima Pengajuan diganti'
                break
              case SalesInvoiceLogStatuses.APPROVAL_APPROVED:
                title = 'Persetujuan Disetujui'
                break
              case SalesInvoiceLogStatuses.APPROVAL_REJECTED:
                title = 'Persetujuan Ditolak'
                dotColor = 'error'
                break
              case SalesInvoiceStatuses.WAITING_RECEIPT:
                title = 'Penerimaan Diproses'
                dotColor = 'warning'
                break
              case SalesInvoiceStatuses.PAID:
                title = 'Penjualan Diterima'
                dotColor = 'info'
                break
              default:
                break
            }

            return title ? (
              <TimelineItem key={log.id} className='pt-2'>
                <TimelineSeparator>
                  <TimelineDot color={dotColor} />
                  <TimelineConnector />
                </TimelineSeparator>
                <TimelineContent>
                  <div className='flex flex-wrap items-center justify-between gap-x-2 mbe-1'>
                    <Typography color='text.primary' className='font-medium text-base'>
                      {title}
                    </Typography>
                    <Typography variant='caption'>
                      {formatDistanceToNow(new Date(log.createdAt), {
                        locale: id,
                        addSuffix: true
                      })
                        .replace('sekitar ', '')
                        .replace('kurang dari ', '')}
                    </Typography>
                  </div>
                  {changes.map(change => (
                    <Typography key={change} className='mbe-2 text-sm'>
                      {change}
                    </Typography>
                  ))}
                  {log.user ? (
                    <div className='flex items-center gap-3'>
                      <Avatar src={log.user?.profilePictureUrl || undefined} />
                      <div className='flex flex-col'>
                        <Typography color='text.primary' className='font-medium'>
                          {log.user?.fullName}
                        </Typography>
                        <Typography variant='body2'>{log.user?.title}</Typography>
                      </div>
                    </div>
                  ) : null}
                </TimelineContent>
              </TimelineItem>
            ) : null
          })}
        </Timeline>
      </CardContent>
    </Card>
  )
}

export default ActivityLogCard
