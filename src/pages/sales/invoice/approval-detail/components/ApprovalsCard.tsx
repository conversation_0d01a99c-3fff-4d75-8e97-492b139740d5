// MUI Imports
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'

// Type Imports
import { Button, Chip } from '@mui/material'
import { ThemeColor } from '@/core/types'
import { useMenu } from '@/components/menu/contexts/menuContext'
import { toast } from 'react-toastify'
import { useAuth } from '@/contexts/AuthContext'
import { useState } from 'react'
import { ApproverType, UserType } from '@/types/userTypes'
import EditApproverDialog, { EditApproverInput } from '@/components/dialogs/edit-approver-dialog'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { DefaultApprovalScope } from '@/pages/user/default-approval/config/enum'
import { useSalesInvoice } from '../../context/SalesInvoiceContext'
import {
  useUpdateSalesInvoiceApproval,
  useUpdateSalesInvoiceApprovalStatus
} from '@/api/services/sales-invoice/mutation'
import { SalesInvoiceApprovalStatuses } from '@/types/salesInvoiceTypes'

type StatusChipType = {
  label: string
  color: ThemeColor
}

// Vars
export const statusChipValue: { [key: string]: StatusChipType } = {
  WAITING: { label: 'Menunggu', color: 'secondary' },
  PENDING: { label: 'Menunggu', color: 'secondary' },
  APPROVED: { label: 'Disetujui', color: 'success' },
  REJECTED: { label: 'Ditolak', color: 'error' }
}

const ApprovalsCard = ({ isCancelation }: { isCancelation?: boolean }) => {
  const { userProfile } = useAuth()
  const { setConfirmState } = useMenu()
  const { salesInvoiceData, fetchSalesInvoiceData, fetchSalesInvoiceList, canUpdate } = useSalesInvoice()
  const { mutate: updateStatusMutate } = useUpdateSalesInvoiceApprovalStatus()
  const { mutate: updateApproverMutate, isLoading: updateApproverLoading } = useUpdateSalesInvoiceApproval()

  const [{ open: editApproverOpen, selectedApproval }, setEditApproverModalState] = useState<{
    open: boolean
    selectedApproval?: ApproverType
  }>({
    open: false
  })

  const approvalList = salesInvoiceData?.approvals ?? []

  const ownApproval = salesInvoiceData?.approvals?.find(approval => {
    return approval.userId === userProfile?.id
  })

  const handleApprove = (id: number) => {
    setConfirmState({
      open: true,
      title: isCancelation ? 'Setujui Pembatalan Faktur' : 'Setujui Faktur',
      content: `Apakah kamu yakin akan menyetujui ${isCancelation ? 'Pengajuan Pembatalan Faktur' : 'Faktur'} ini? Action ini tidak bisa diubah`,
      confirmText: 'Setujui',
      onConfirm: () => {
        updateStatusMutate(
          {
            id: salesInvoiceData?.id,
            approvalId: id,
            payload: {
              status: SalesInvoiceApprovalStatuses.APPROVED
            }
          },
          {
            onSuccess: () => {
              toast.success(
                isCancelation ? 'Pengajuan Pembatalan Faktur berhasil disetujui' : 'Faktur berhasil disetujui'
              )
              fetchSalesInvoiceData()
              fetchSalesInvoiceList()
            }
          }
        )
      }
    })
  }

  const handleReject = (id: number) => {
    setConfirmState({
      open: true,
      title: isCancelation ? 'Tolak Pembatalan Faktur' : 'Tolak Faktur',
      content: `Apakah kamu yakin akan menolak ${isCancelation ? 'Pengajuan Pembatalan Faktur' : 'Faktur'} ini? Action ini tidak bisa diubah`,
      confirmText: 'Tolak',
      onConfirm: () => {
        updateStatusMutate(
          {
            id: salesInvoiceData?.id,
            approvalId: id,
            payload: {
              status: SalesInvoiceApprovalStatuses.REJECTED
            }
          },
          {
            onSuccess: () => {
              toast.success(isCancelation ? 'Pengajuan Pembatalan Faktur berhasil ditolak' : 'Faktur berhasil ditolak')
              fetchSalesInvoiceData()
              fetchSalesInvoiceList()
            }
          }
        )
      },
      confirmColor: 'error'
    })
  }

  const handleUpdateApprover = (formData: EditApproverInput) => {
    updateApproverMutate(
      {
        siId: salesInvoiceData?.id,
        approvalId: selectedApproval?.id,
        payload: formData
      },
      {
        onSuccess: () => {
          toast.success('Penerima Pengajuan berhasil diganti')
          fetchSalesInvoiceData()
          setEditApproverModalState({ open: false })
        }
      }
    )
  }

  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-6'>
          <div className='flex justify-between items-center'>
            <Typography variant='h5'>{isCancelation ? 'Persetujuan Pembatalan' : 'Pengajuan Persetujuan'}</Typography>
          </div>
          <div className='flex flex-col gap-4'>
            {approvalList.map((approval, index) => {
              const statusValue = statusChipValue[approval.status]
              return (
                <div
                  key={approval.id}
                  className='rounded-lg border border-[#4c4e64]/22 p-4 flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'
                >
                  <div className='flex justify-between items-start self-stretch relative w-full bg-transparent'>
                    <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                      {approval.user?.fullName}
                    </p>
                    {ownApproval?.status === SalesInvoiceApprovalStatuses.WAITING &&
                    approval.status === SalesInvoiceApprovalStatuses.WAITING &&
                    approval.userId === userProfile?.id ? (
                      <div className='flex gap-2 items-center self-center'>
                        <Button
                          variant='contained'
                          size='small'
                          color='error'
                          onClick={() => handleReject(approval.id)}
                        >
                          Tolak
                        </Button>
                        <Button variant='contained' size='small' onClick={() => handleApprove(approval.id)}>
                          Setujui
                        </Button>
                      </div>
                    ) : (
                      <Chip label={statusValue?.label} color={statusValue?.color} variant='tonal' size='small' />
                    )}
                  </div>
                  <div className='flex justify-between items-start w-full'>
                    <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                      <small className='text-sm text-[#4c4e64]/60 dark:text-inherit'>{approval.user?.title}</small>
                    </label>
                    {approval.approvedAt || approval.rejectedAt || approval.respondedAt ? (
                      <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                        <small className='text-sm text-[#4c4e64]/60 dark:text-inherit'>
                          {formatDate(
                            approval.approvedAt || approval.rejectedAt || approval.respondedAt || '',
                            'eeee, dd/MM/yyyy, HH:mm',
                            {
                              locale: id
                            }
                          )}
                        </small>
                      </label>
                    ) : null}
                  </div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>
      {editApproverOpen ? (
        <EditApproverDialog
          open={editApproverOpen}
          setOpen={open =>
            setEditApproverModalState(current => ({
              open: open,
              selectedApproval: open ? current.selectedApproval : undefined
            }))
          }
          selectedApproval={selectedApproval}
          scope={DefaultApprovalScope.MaterialRequest}
          onSubmit={handleUpdateApprover}
          isLoading={updateApproverLoading}
          approvalList={approvalList as any}
        />
      ) : null}
    </>
  )
}

export default ApprovalsCard
