import { StatusChipColorType } from '@/types/appTypes'
import { MrStatus } from '@/types/mrTypes'
import { SalesInvoiceStatuses } from '@/types/salesInvoiceTypes'

export const mrStatusOptions = [
  {
    value: MrStatus.PROCESSED,
    label: 'Diproses'
  },
  {
    value: MrStatus.APPROVED,
    label: 'Disetujui'
  },
  {
    value: MrStatus.REJECTED,
    label: 'Ditolak'
  },
  {
    value: MrStatus.CANCELED,
    label: '<PERSON><PERSON>alkan'
  },
  {
    value: MrStatus.CLOSED,
    label: 'Seles<PERSON>'
  }
]

export const statusChipColor: { [key: string]: StatusChipColorType } = {
  [SalesInvoiceStatuses.PROCESSED]: { color: 'default' },
  [SalesInvoiceStatuses.APPROVED]: { color: 'success' },
  [SalesInvoiceStatuses.REJECTED]: { color: 'error' },
  [SalesInvoiceStatuses.WAITING_RECEIPT]: { color: 'warning' },
  [SalesInvoiceStatuses.PAID]: { color: 'info' }
}

export const statusChipValue: { [key: string]: { label: string; color: StatusChipColorType['color'] } } = {
  [SalesInvoiceStatuses.PROCESSED]: { label: 'Diproses', color: 'default' },
  [SalesInvoiceStatuses.APPROVED]: { label: 'Disetujui', color: 'success' },
  [SalesInvoiceStatuses.REJECTED]: { label: 'Ditolak', color: 'error' },
  [SalesInvoiceStatuses.WAITING_RECEIPT]: { label: 'Proses Penerimaan', color: 'warning' },
  [SalesInvoiceStatuses.PAID]: { label: 'Sudah Diterima', color: 'info' }
}
