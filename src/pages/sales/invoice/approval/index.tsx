// MUI Imports
import Grid from '@mui/material/Grid'

// Type Imports
import { Typography } from '@mui/material'

import { SalesInvoiceContextProvider } from '../context/SalesInvoiceContext'
import SalesInvoiceList from './components/SalesInvoiceList'

const SalesInvoiceApprovalListPage = () => {
  return (
    <SalesInvoiceContextProvider>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <div className='flex justify-between items-end'>
            <div className='flex flex-col'>
              <Typography variant='h4'>Persetujuan Faktur Penjualan</Typography>
              <Typography>Semua Faktur yang harus kamu setujui akan ditampilkan di sini</Typography>
            </div>
          </div>
        </Grid>

        <Grid item xs={12}>
          <SalesInvoiceList />
        </Grid>
      </Grid>
    </SalesInvoiceContextProvider>
  )
}

export default SalesInvoiceApprovalListPage
