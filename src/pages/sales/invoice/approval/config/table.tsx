import { StatusChipColorType } from '@/types/appTypes'
import { Chip, colors, IconButton, Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { toCurrency } from '@/utils/helper'
import { SalesInvoice, SalesInvoiceApprovalStatuses } from '@/types/salesInvoiceTypes'

export const statusChipColor: { [key: string]: StatusChipColorType } = {
  [SalesInvoiceApprovalStatuses.PENDING]: { color: 'warning' },
  [SalesInvoiceApprovalStatuses.WAITING]: { color: 'warning' },
  [SalesInvoiceApprovalStatuses.APPROVED]: { color: 'success' },
  [SalesInvoiceApprovalStatuses.REJECTED]: { color: 'error' }
}

export const statusChipValue: { [key: string]: { label: string; color: StatusChipColorType['color'] } } = {
  [SalesInvoiceApprovalStatuses.PENDING]: { label: 'Menunggu', color: 'warning' },
  [SalesInvoiceApprovalStatuses.WAITING]: { label: 'Menunggu', color: 'warning' },
  [SalesInvoiceApprovalStatuses.APPROVED]: { label: 'Disetujui', color: 'success' },
  [SalesInvoiceApprovalStatuses.REJECTED]: { label: 'Ditolak', color: 'error' }
}

type SalesInvoiceWithAction = SalesInvoice & {
  action?: string
}

type RowActionType = {
  showDetail: (id: string) => void
}

// Column Definitions
const columnHelper = createColumnHelper<SalesInvoiceWithAction>()

export const tableColumns = (rowAction: RowActionType, userId?: string) => [
  columnHelper.accessor('number', {
    header: 'NO. FAKTUR',
    cell: ({ row }) => (
      <Typography
        color={colors.green.A400}
        className='cursor-pointer'
        onClick={() => rowAction.showDetail(row.original.id)}
      >
        {row.original.number}
      </Typography>
    )
  }),
  columnHelper.accessor('status', {
    header: 'STATUS',
    cell: ({ row }) => {
      const ownApproval = row.original.approvals?.find(approval => approval.userId === userId)
      const approvalStatus = ownApproval?.status || row.original?.status

      return (
        <Chip
          label={statusChipValue[approvalStatus]?.label || 'Menunggu'}
          color={statusChipValue[approvalStatus]?.color || 'warning'}
          variant='tonal'
          size='small'
        />
      )
    }
  }),
  columnHelper.accessor('taxInvoiceNumber', {
    header: 'NO FAKTUR PAJAK',
    cell: ({ row }) => <Typography>{row.original.taxInvoiceNumber ?? '-'}</Typography>
  }),
  columnHelper.accessor('invoiceDate', {
    header: 'TANGGAL FAKTUR',
    cell: ({ row }) => (
      <Typography>
        {row.original.invoiceDate
          ? formatDate(row.original.invoiceDate ?? Date.now(), 'dd/MM/yyyy', { locale: id })
          : '-'}
      </Typography>
    )
  }),
  columnHelper.accessor('customer.name', {
    header: 'CUSTOMER',
    cell: ({ row }) => <Typography>{row.original.customer?.name || 'N/A'}</Typography>
  }),
  columnHelper.accessor('totalAmount', {
    header: 'TOTAL FAKTUR',
    cell: ({ row }) => (
      <Typography color={colors.green.A400}>
        {toCurrency(row.original.totalAmount, false, row.original?.currency?.code)}
      </Typography>
    )
  }),
  columnHelper.accessor('createdByUser', {
    header: 'DIBUAT OLEH',
    cell: ({ row }) => (
      <div className='flex flex-col'>
        <Typography>{row.original.createdByUser?.fullName}</Typography>
        <Typography variant='caption'>{row.original.createdByUser?.title}</Typography>
      </div>
    )
  }),
  columnHelper.accessor('action', {
    header: 'ACTION',
    cell: ({ row }) => (
      <div className='flex items-center gap-0.5'>
        <IconButton size='small' onClick={() => rowAction.showDetail(row.original.id)}>
          <i className='ri-eye-line text-textSecondary' />
        </IconButton>
      </div>
    ),
    enableSorting: false
  })
]
