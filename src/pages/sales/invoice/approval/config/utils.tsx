import { StatusChipColorType } from '@/types/appTypes'
import { MrStatus } from '@/types/mrTypes'
import { SalesInvoiceStatuses } from '@/types/salesInvoiceTypes'

export const mrStatusOptions = [
  {
    value: MrStatus.PROCESSED,
    label: 'Diproses'
  },
  {
    value: MrStatus.APPROVED,
    label: 'Disetujui'
  },
  {
    value: MrStatus.REJECTED,
    label: '<PERSON><PERSON><PERSON>'
  },
  {
    value: MrStatus.CANCELED,
    label: '<PERSON><PERSON><PERSON><PERSON>'
  },
  {
    value: MrStatus.CLOSED,
    label: '<PERSON><PERSON><PERSON>'
  }
]
