import { useEffect, useState } from 'react'
import { Typography } from '@mui/material'
import Card from '@mui/material/Card'

import {
  getCoreRowModel,
  useReactTable,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFacetedMinMaxValues,
  getPaginationRowModel,
  getSortedRowModel
} from '@tanstack/react-table'

import DebouncedInput from '@/components/DebounceInput'
import Table from '@/components/table'
import { tableColumns } from '../config/table'
import { useRouter } from '@/routes/hooks'
import { useSalesInvoice } from '../../context/SalesInvoiceContext'
import { useAuth } from '@/contexts/AuthContext'
import FilterGroupDialog, { FilterGroupConfig, FilterValues } from '@/components/layout/shared/filter/FilterGroup'
import { SalesInvoiceStatuses } from '@/types/salesInvoiceTypes'

const SalesInvoiceList = () => {
  const router = useRouter()
  const {
    salesInvoiceListResponse: { items: salesInvoiceList, totalItems, totalPages, limit: limitItems, page: pageItems },
    salesInvoiceParams,
    setSalesInvoiceParams,
    setSelectedSalesInvoice,
    setPartialSalesInvoiceParams
  } = useSalesInvoice()

  const { userProfile, departmentList, ownSiteList } = useAuth()

  const { page, search, startDate, endDate } = salesInvoiceParams

  const [filterGroupConfig, setFilterGroupConfig] = useState<FilterGroupConfig>({})

  const table = useReactTable({
    data: salesInvoiceList.map(invoice => ({ ...invoice, isRead: invoice.approvals?.[0]?.isRead })),
    columns: tableColumns(
      {
        showDetail: id => {
          setSelectedSalesInvoice({ salesInvoiceId: id })
          router.push(`/sales/invoice/approval/${id}`)
        }
      },
      userProfile?.id
    ) as any,
    initialState: {
      pagination: {
        pageSize: salesInvoiceParams.limit ?? 10,
        pageIndex: page - 1
      }
    },
    state: {
      pagination: {
        pageSize: limitItems,
        pageIndex: pageItems - 1
      }
    },
    manualPagination: true,
    rowCount: totalItems,
    pageCount: totalPages,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getFacetedMinMaxValues: getFacetedMinMaxValues()
  })

  const onFilterChanged = ({ date }: FilterValues) => {
    setPartialSalesInvoiceParams('page', 1)
    if (date[0]) setPartialSalesInvoiceParams('startDate', date[0])
    if (date[1]) setPartialSalesInvoiceParams('endDate', date[1])
    // Add other filters as needed
  }

  useEffect(() => {
    setFilterGroupConfig({
      date: {
        options: [],
        values: [startDate, endDate]
      },
      site: {
        options: ownSiteList.map(site => {
          return { value: site.id, label: site.name }
        }),
        values: []
      },
      department: {
        options: departmentList?.map(department => ({ value: department.id, label: department.name })) ?? [],
        values: []
      }
    })
  }, [salesInvoiceParams, ownSiteList, departmentList, startDate, endDate])

  useEffect(() => {
    setSalesInvoiceParams({
      limit: 10,
      page: 1,
      search: '',
      status: SalesInvoiceStatuses.PROCESSED
    })
  }, [])

  return (
    <Card>
      <div className='flex justify-between gap-4 p-5 flex-col items-start sm:flex-row sm:items-center'>
        <div className='flex gap-4 items-center flex-col sm:flex-row is-full sm:is-auto'>
          <DebouncedInput
            value={search}
            onChange={value => {
              setPartialSalesInvoiceParams('page', 1)
              setPartialSalesInvoiceParams('search', value as string)
            }}
            placeholder='Cari'
            className='is-full sm:is-auto'
          />
          <FilterGroupDialog config={filterGroupConfig} onFilterApplied={onFilterChanged} />
        </div>
        <div className='flex items-center gap-x-4 max-sm:gap-y-4 is-full flex-col sm:is-auto sm:flex-row'>
          {/* <Button
            color='secondary'
            variant='outlined'
            startIcon={<i className='ri-upload-2-line' />}
            className='is-full sm:is-auto'
          >
            Ekspor
          </Button> */}
        </div>
      </div>
      <Table
        table={table}
        emptyLabel={
          <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
            <Typography>Belum ada Faktur Penjualan</Typography>
            <Typography className='text-sm text-gray-400'>
              Semua Faktur Penjualan yang harus kamu setujui akan ditampilkan di sini
            </Typography>
          </td>
        }
        onRowsPerPageChange={pageSize => {
          if (pageSize > totalItems) {
            setPartialSalesInvoiceParams('limit', totalItems)
            setPartialSalesInvoiceParams('page', 1)
          } else {
            setPartialSalesInvoiceParams('limit', pageSize)

            const maxPage = Math.ceil(totalItems / pageSize)
            if (page > maxPage) {
              setPartialSalesInvoiceParams('page', maxPage)
            }
          }
        }}
        onPageChange={pageIndex => setPartialSalesInvoiceParams('page', pageIndex)}
      />
    </Card>
  )
}

export default SalesInvoiceList
