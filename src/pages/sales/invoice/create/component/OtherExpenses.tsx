import { <PERSON><PERSON>, <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>, IconButton, FormHelperText } from '@mui/material'
import { useFormContext, useFieldArray, useWatch } from 'react-hook-form'
import { useState } from 'react'

import AddExpenseDialog from '@/components/dialogs/add-expense-dialog'
import { formatThousandSeparator, thousandSeparator, toCurrency } from '@/utils/helper'
import AccountsQueryMethods from '@/api/services/account/query'
import { useEffect } from 'react'
import { SalesInvoiceDtoType } from '../config/schema'
import { SalesInvoiceOtherExpense } from '@/types/salesInvoiceTypes'

const OtherExpenses = () => {
  const {
    control,
    setValue,
    formState: { errors }
  } = useFormContext<SalesInvoiceDtoType>()
  const [dialogOpen, setDialogOpen] = useState(false)
  const [selectedExpenses, setSelectedExpenses] = useState<SalesInvoiceOtherExpense[]>([])
  const [editingExpense, setEditingExpense] = useState<{ expense: SalesInvoiceOtherExpense; index: number } | null>(
    null
  )

  // Use useFieldArray to manage the otherExpenses array in the form
  const { append, remove, update } = useFieldArray({
    control,
    name: 'otherExpenses'
  })

  // Watch form otherExpenses to rehydrate from draft
  const otherExpensesWatch = useWatch({ control, name: 'otherExpenses', defaultValue: [] })

  useEffect(() => {
    const hydrate = async () => {
      const formExpenses = otherExpensesWatch || []
      if ((formExpenses?.length ?? 0) === 0) {
        setSelectedExpenses([])
        return
      }

      try {
        const uniqueIds = Array.from(new Set(formExpenses.map((e: any) => e.accountId).filter(Boolean))) as string[]
        const accounts = await Promise.all(uniqueIds.map(id => AccountsQueryMethods.getAccount(id)))
        const accountMap = new Map(accounts.map(acc => [acc.id, { id: acc.id, code: acc.code, name: acc.name }]))

        const enriched = formExpenses.map((e: any) => ({
          accountId: e.accountId,
          amount: e.amount,
          note: e.note,
          account: accountMap.get(e.accountId),
          department: e.department,
          site: e.site,
          departmentId: e.departmentId,
          siteId: e.siteId
        })) as SalesInvoiceOtherExpense[]

        setSelectedExpenses(enriched)
      } catch (err) {
        // If lookup fails, still render minimal info
        const fallback = (formExpenses || []).map((e: any) => ({
          accountId: e.accountId,
          amount: e.amount,
          note: e.note,
          departmentId: e.departmentId,
          siteId: e.siteId
        }))
        setSelectedExpenses(fallback as SalesInvoiceOtherExpense[])
      }
    }

    hydrate()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [JSON.stringify(otherExpensesWatch)])

  const handleDeleteExpense = (index: number) => {
    // Remove from local state
    setSelectedExpenses(current => {
      const filteredExpenses = current.filter((_, i) => i !== index)
      setValue('otherExpenses', filteredExpenses)
      return filteredExpenses
    })
    // Remove from form state
    remove(index)
  }

  const handleEditClick = (expense: SalesInvoiceOtherExpense, index: number) => {
    setEditingExpense({ expense, index })
    setDialogOpen(true)
  }

  const handleDialogSubmit = (expense: SalesInvoiceOtherExpense) => {
    if (editingExpense) {
      // Update existing expense
      const updatedExpenses = [...selectedExpenses]
      updatedExpenses[editingExpense.index] = expense
      setSelectedExpenses(updatedExpenses)
      update(editingExpense.index, {
        accountId: expense.accountId,
        amount: expense.amount,
        note: expense.note,
        departmentId: expense.departmentId,
        siteId: expense.siteId,
        department: expense.department,
        site: expense.site
      })
    } else {
      // Add new expense
      setSelectedExpenses(current => [...current, expense])
      append({
        accountId: expense.accountId,
        amount: expense.amount,
        note: expense.note,
        departmentId: expense.departmentId,
        siteId: expense.siteId,
        department: expense.department,
        site: expense.site
      })
    }
    setEditingExpense(null)
    setDialogOpen(false)
  }

  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-6'>
          <div className='flex justify-between items-start'>
            <Typography variant='h5'>Biaya Lain-Lain (Opsional)</Typography>
            <Button variant='contained' onClick={() => setDialogOpen(true)}>
              Tambah
            </Button>
          </div>

          {selectedExpenses.length === 0 ? (
            <div className='text-center py-8'>
              <Typography color='textSecondary'>Belum ada Biaya Lain-Lain yang dipilih</Typography>
              <Typography variant='body2' color='textSecondary' className='mt-2'>
                Klik "Tambah" untuk memilih Biaya Lain-Lain
              </Typography>
            </div>
          ) : (
            <div className='overflow-x-auto'>
              <table className='w-full border-collapse'>
                <thead>
                  <tr className='bg-[#DBF7E8]'>
                    <th className='text-left px-5 py-4 text-xs font-medium text-[#4C4E64] uppercase tracking-wider border-b border-[#4C4E641F]'>
                      AKUN PERKIRAAN
                    </th>
                    <th className='text-left px-4 py-4 text-xs font-medium text-[#4C4E64] uppercase tracking-wider border-b border-[#4C4E641F]'>
                      NOMINAL
                    </th>
                    <th className='text-left px-4 py-4 text-xs font-medium text-[#4C4E64] uppercase tracking-wider border-b border-[#4C4E641F]'>
                      DEPARTEMEN
                    </th>
                    <th className='text-left px-4 py-4 text-xs font-medium text-[#4C4E64] uppercase tracking-wider border-b border-[#4C4E641F]'>
                      LOKASI
                    </th>
                    <th className='text-left px-4 py-4 text-xs font-medium text-[#4C4E64] uppercase tracking-wider border-b border-[#4C4E641F]'>
                      MEMO
                    </th>
                    <th className='text-center px-4 py-4 text-xs font-medium text-[#4C4E64] uppercase tracking-wider border-b border-[#4C4E641F] w-24'>
                      ACTION
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {selectedExpenses.map((expense, index) => (
                    <tr key={index} className='border-b border-[#4C4E641F]'>
                      <td className='px-5 py-4 text-sm text-[#4C4E64]'>
                        [{expense.account?.code}] {expense.account?.name}
                      </td>
                      <td className='px-4 py-4 text-sm text-[#4C4E64]'>{formatThousandSeparator(expense.amount)}</td>
                      <td className='px-4 py-4 text-sm text-[#4C4E64]'>{expense?.site?.name ?? '-'}</td>
                      <td className='px-4 py-4 text-sm text-[#4C4E64]'>{expense?.department?.name ?? '-'}</td>
                      <td className='px-4 py-4 text-sm text-[#4C4E64]'>{expense.note}</td>
                      <td className='px-4 py-4 text-center'>
                        <div className='flex gap-1'>
                          <IconButton
                            size='small'
                            onClick={() => handleEditClick(expense, index)}
                            className='text-[#4C4E648A] hover:text-[#4C4E64]'
                          >
                            <i className='ri-pencil-line text-lg' />
                          </IconButton>
                          <IconButton
                            size='small'
                            onClick={() => handleDeleteExpense(index)}
                            className='text-[#4C4E648A] hover:text-[#4C4E64]'
                          >
                            <i className='ri-delete-bin-line text-lg' />
                          </IconButton>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
          {(errors?.otherExpenses?.length ?? 0) > 0 && errors?.otherExpenses?.[0]?.accountId && (
            <FormHelperText error>{errors?.otherExpenses?.[0]?.accountId?.message}</FormHelperText>
          )}
        </CardContent>
      </Card>
      {dialogOpen && (
        <AddExpenseDialog
          open={dialogOpen}
          setOpen={open => {
            if (!open) setEditingExpense(null)
            setDialogOpen(open)
          }}
          onSubmit={handleDialogSubmit}
          initialData={editingExpense?.expense}
        />
      )}
    </>
  )
}

export default OtherExpenses
