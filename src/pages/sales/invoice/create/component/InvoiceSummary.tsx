import { Card, CardContent, Grid, Typography } from '@mui/material'
import { useFormContext, useWatch } from 'react-hook-form'
import { SalesInvoiceDtoType } from '../config/schema'
import { useEffect, useMemo } from 'react'
import { SalesInvoiceDiscountTypes, SalesInvoiceTaxTypes } from '@/types/salesInvoiceTypes'
import { toCurrency } from '@/utils/helper'
import { calculateTax } from '@/utils/calculate-tax'

const InvoiceSummary = () => {
  const { control, setValue } = useFormContext<SalesInvoiceDtoType>()

  const [items, otherExpenses, discountType, discountValue] = useWatch({
    control,
    name: ['items', 'otherExpenses', 'discountType', 'discountValue']
  })

  useEffect(() => {
    let subtotal = 0
    let totalTax = 0

    items?.forEach(item => {
      const itemSubtotal = (item.pricePerUnit || 0) * (item.quantity || 0) - (item.totalItemDiscount ?? 0)
      subtotal += itemSubtotal

      if (item?.taxType) {
        const tax = calculateTax(itemSubtotal, item.taxType, item.taxPercentage)
        totalTax += tax
      }
    })

    let otherAmount = 0
    otherExpenses?.forEach(expense => {
      otherAmount += expense.amount || 0
    })

    let invoiceDiscount = 0
    if (discountType === SalesInvoiceDiscountTypes.PERCENTAGE) {
      invoiceDiscount = subtotal * ((discountValue || 0) / 100)
    } else if (discountType === SalesInvoiceDiscountTypes.FLAT) {
      invoiceDiscount = discountValue || 0
    }

    const total = subtotal + totalTax + otherAmount - invoiceDiscount

    setValue('subTotalAmount', subtotal)
    setValue('taxAmount', totalTax)
    setValue('otherAmount', otherAmount)
    setValue('discountAmount', invoiceDiscount)
    setValue('totalAmount', total)
  }, [items, otherExpenses, discountType, discountValue, setValue])

  const subTotalAmount = useWatch({ control, name: 'subTotalAmount', defaultValue: 0 })
  const taxAmount = useWatch({ control, name: 'taxAmount', defaultValue: 0 })
  const otherAmount = useWatch({ control, name: 'otherAmount', defaultValue: 0 })
  const discountAmount = useWatch({ control, name: 'discountAmount', defaultValue: 0 })
  const totalAmount = useWatch({ control, name: 'totalAmount', defaultValue: 0 })

  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <div className='flex justify-between items-start'>
          <Typography variant='h5'>Ringkasan Penjualan</Typography>
        </div>

        <Grid container spacing={2}>
          {/* Sub Total Faktur */}
          <Grid item xs={12} sm={6} md={3}>
            <div className='bg-[#F5F5F5] rounded-lg p-4'>
              <Typography variant='body2' className='text-[#4C4E648A] mb-2'>
                Sub Total Faktur
              </Typography>
              <Typography variant='h6' className='font-bold text-[#4C4E64]'>
                {toCurrency(subTotalAmount)}
              </Typography>
            </div>
          </Grid>

          {/* Total Pajak */}
          <Grid item xs={12} sm={6} md={3}>
            <div className='bg-[#F5F5F5] rounded-lg p-4'>
              <Typography variant='body2' className='text-[#4C4E648A] mb-2'>
                Total Pajak
              </Typography>
              <Typography variant='h6' className='font-bold text-[#4C4E64]'>
                {toCurrency(taxAmount)}
              </Typography>
            </div>
          </Grid>

          {/* Biaya Lain-Lain */}
          <Grid item xs={12} sm={6} md={3}>
            <div className='bg-[#F5F5F5] rounded-lg p-4'>
              <Typography variant='body2' className='text-[#4C4E648A] mb-2'>
                Biaya Lain-lain
              </Typography>
              <Typography variant='h6' className='font-bold text-[#4C4E64]'>
                {toCurrency(otherAmount)}
              </Typography>
            </div>
          </Grid>
          {/* Diskon Faktur */}
          {/* <Grid item xs={12} sm={6} md={3}>
            <div className='bg-[#F5F5F5] rounded-lg p-4'>
              <Typography variant='body2' className='text-[#4C4E648A] mb-2'>
                Diskon Faktur
              </Typography>
              <Typography variant='h6' className='font-bold text-[#4C4E64]'>
                {toCurrency(discountAmount)}
              </Typography>
            </div>
          </Grid> */}

          {/* Total Faktur */}
          <Grid item xs={12} sm={6} md={3}>
            <div className='bg-[#DBF7E8] rounded-lg p-4'>
              <Typography variant='body2' className='text-[#4BD88B] mb-2'>
                Total Faktur
              </Typography>
              <Typography variant='h6' className='font-bold text-[#4BD88B]'>
                {toCurrency(totalAmount)}
              </Typography>
            </div>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  )
}

export default InvoiceSummary
