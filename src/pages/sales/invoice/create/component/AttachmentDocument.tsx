import { <PERSON><PERSON><PERSON>, Card, CardContent, Typo<PERSON>, Button } from '@mui/material'
import { useFormContext, useWatch } from 'react-hook-form'

import { useFilePicker } from 'use-file-picker'
import { useUpdateEffect } from 'react-use'
import { SalesInvoiceDtoType } from '../config/schema'

const Document = () => {
  const { control, setValue } = useFormContext<SalesInvoiceDtoType>()
  const documentName = useWatch({ control, name: 'documentName' })

  const { openFilePicker, filesContent, clear } = useFilePicker({
    multiple: false,
    accept: ['.pdf', '.docx', '.doc', 'xls', 'xlsx', 'image/*'],
    readAs: 'DataURL'
  })

  useUpdateEffect(() => {
    if ((filesContent?.length ?? 0) > 0) {
      setValue('documentContent', filesContent[0]?.content)
      setValue('documentName', filesContent[0]?.name)
    }
  }, [filesContent])

  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <Typography variant='h5'>Dokumen Penyerta (Opsional)</Typography>
        <Typography className='font-medium' variant='body1'>
          Unggah Dokumen
        </Typography>
        <div className='flex items-center gap-4'>
          <TextField
            key={JSON.stringify(filesContent)}
            size='small'
            label='Pilih File'
            fullWidth
            value={filesContent?.[0]?.name || documentName || ''}
            placeholder='Belum ada file dipilih'
            className='flex-1'
            InputProps={{
              readOnly: true
            }}
          />
          <Button variant='contained' onClick={() => openFilePicker()}>
            Pilih File
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

export default Document
