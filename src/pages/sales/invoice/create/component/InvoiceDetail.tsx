import {
  Grid,
  TextField,
  Card,
  CardContent,
  Typography,
  Autocomplete,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  ListSubheader,
  ListItemText,
  InputAdornment,
  IconButton
} from '@mui/material'
import { Controller, useFormContext, useWatch } from 'react-hook-form'
import { useState, useCallback, useEffect } from 'react'
import { useQuery } from '@tanstack/react-query'

import debounce from '@mui/material/utils/debounce'
import AppReactDatepicker from '@/components/libs/styles/AppReactDatepicker'
import CompanyQueryMethods, {
  CUSTOMER_LIST_QUERY_KEY,
  PROJECT_LABEL_LIST_QUERY_KEY
} from '@/api/services/company/query'
import { defaultListData } from '@/api/queryClient'
import { ListResponse } from '@/types/api'
import { addDays, formatISO, startOfDay, toDate } from 'date-fns'
import { ProjectLabelType } from '@/types/projectTypes'
import { useAuth } from '@/contexts/AuthContext'
import { useSearchParams } from 'react-router-dom'
import NumberField from '@/components/numeric/NumberField'
import CurrencyField from '@/components/numeric/CurrencyField'
import { SalesInvoiceDtoType } from '../config/schema'
import { CustomerType } from '@/types/customerTypes'
import { paymentMethodOptions } from '@/pages/purchase-order/config/options'
import { SalesInvoicePaymentTerms } from '@/types/salesInvoiceTypes'

const InvoiceDetail = () => {
  const { control, setValue } = useFormContext<SalesInvoiceDtoType>()
  const { groupedSiteList, departmentList, currenciesList } = useAuth()
  const [searchParams] = useSearchParams()
  const [customerSearchQuery, setCustomerSearchQuery] = useState('')
  const [selectedCustomer, setSelectedCustomer] = useState<CustomerType | null>(null)

  // Watch form values
  const paymentMethodWatch = useWatch({
    control,
    name: 'paymentTerms'
  })

  const invoiceDateWatch = useWatch({
    control,
    name: 'invoiceDate'
  })

  const paymentDueDaysWatch = useWatch({
    control,
    name: 'paymentDueDays'
  })

  const currencyIdWatch = useWatch({
    control,
    name: 'currencyId'
  })

  const siteIdWatch = useWatch({
    control,
    name: 'siteId',
    defaultValue: ''
  })

  // Fetch customer list
  const {
    data: customerListResponse = defaultListData as ListResponse<CustomerType>,
    isFetching: fetchCustomerLoading
  } = useQuery({
    queryKey: [CUSTOMER_LIST_QUERY_KEY, customerSearchQuery],
    queryFn: () => {
      return CompanyQueryMethods.getCustomerList({
        ...(customerSearchQuery && { search: customerSearchQuery }),
        limit: 30
      })
    },
    placeholderData: defaultListData as ListResponse<CustomerType>
  })

  const customerList = customerListResponse.items || []

  // Debounced search handler
  const handleCustomerInputChange = useCallback(
    debounce((_event: any, newValue: string, reason: string) => {
      if (reason === 'input') {
        setCustomerSearchQuery(newValue)
      }
    }, 700),
    []
  )

  const {
    data: { items: projectLabels }
  } = useQuery({
    queryKey: [PROJECT_LABEL_LIST_QUERY_KEY, siteIdWatch],
    enabled: !!siteIdWatch,
    queryFn: async () => {
      return CompanyQueryMethods.getProjectLabelList({ limit: 100, siteId: siteIdWatch })
    },
    placeholderData: defaultListData as ListResponse<ProjectLabelType>
  })

  const currentCurrency = currenciesList?.find(c => c.id === currencyIdWatch)

  useEffect(() => {
    if (currencyIdWatch && currenciesList?.find(c => c.id === currencyIdWatch)?.isDefault) {
      setValue('exchangeRate', 1)
    }
  }, [currencyIdWatch, currenciesList])

  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <Typography variant='h5'>Detil Faktur</Typography>

        <Grid container spacing={4}>
          {/* Customer Selection */}

          <Grid item xs={12} md={6}>
            <Controller
              control={control}
              name='customerId'
              render={({ field: { onChange, value }, fieldState: { error } }) => (
                <Autocomplete
                  key={JSON.stringify(selectedCustomer)}
                  filterOptions={x => x}
                  isOptionEqualToValue={(option, value) => option.id === value.id}
                  onInputChange={handleCustomerInputChange}
                  options={customerList}
                  freeSolo
                  onChange={(_e, newValue: CustomerType | null) => {
                    if (newValue) {
                      setSelectedCustomer(newValue)
                      onChange(newValue.id)
                    } else {
                      setSelectedCustomer(null)
                      onChange('')
                    }
                  }}
                  value={selectedCustomer || customerList.find(v => v.id === value) || null}
                  noOptionsText='Customer tidak ditemukan'
                  loading={fetchCustomerLoading}
                  renderInput={params => (
                    <TextField
                      {...params}
                      label='Customer'
                      placeholder='Cari Customer'
                      variant='outlined'
                      error={!!error}
                      helperText={error?.message}
                      InputProps={{
                        ...params.InputProps,
                        endAdornment: (
                          <>
                            {fetchCustomerLoading ? <CircularProgress size={20} /> : null}
                            {params.InputProps.endAdornment}
                          </>
                        ),
                        onKeyDown: e => {
                          if (e.key === 'Enter') {
                            e.stopPropagation()
                          }
                        }
                      }}
                    />
                  )}
                  getOptionLabel={(option: CustomerType) => option?.name || ''}
                  renderOption={(props, option) => {
                    const { key, ...optionProps } = props
                    return (
                      <li key={key} {...optionProps}>
                        <Typography>{option.name}</Typography>
                      </li>
                    )
                  }}
                />
              )}
            />
          </Grid>

          {/* Invoice Date */}
          <Grid item xs={12} md={6}>
            <Controller
              control={control}
              name='invoiceDate'
              rules={{ required: 'Tanggal Faktur wajib diisi' }}
              render={({ field: { value, onChange }, fieldState: { error } }) => (
                <AppReactDatepicker
                  boxProps={{ className: 'is-full' }}
                  selected={value ? toDate(value) : undefined}
                  onChange={(date: Date) => onChange(formatISO(date))}
                  dateFormat='dd/MM/yyyy'
                  customInput={
                    <TextField
                      fullWidth
                      label='Tanggal Faktur'
                      placeholder='19/05/2025'
                      error={!!error}
                      helperText={error?.message}
                      InputProps={{
                        readOnly: true
                      }}
                    />
                  }
                />
              )}
            />
          </Grid>

          {/* Payment Method */}
          <Grid item xs={12} md={6}>
            <Controller
              control={control}
              name='paymentTerms'
              rules={{ required: 'Metode Bayar wajib dipilih' }}
              render={({ field: { value, onChange }, fieldState: { error } }) => (
                <FormControl fullWidth error={!!error}>
                  <InputLabel>Metode Bayar</InputLabel>
                  <Select key={value} value={value} onChange={onChange} label='Metode Bayar'>
                    {paymentMethodOptions.map(option => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </Select>
                  {error && <FormHelperText>{error.message}</FormHelperText>}
                </FormControl>
              )}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <Controller
              name='paymentDueDays'
              control={control}
              render={({ field: { value, onChange }, fieldState: { error } }) =>
                paymentMethodWatch === SalesInvoicePaymentTerms.NET ? (
                  <TextField
                    {...{ value, onChange }}
                    label='Hari'
                    InputProps={{ inputComponent: NumberField as any, endAdornment: 'Hari' }}
                    className='w-full'
                  />
                ) : (
                  <div />
                )
              }
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <Controller
              control={control}
              name='currencyId'
              render={({ field, fieldState: { error } }) => (
                <FormControl error={!!error} fullWidth>
                  <InputLabel id='mata-uang-field'>Mata Uang</InputLabel>
                  <Select {...field} fullWidth label='Mata Uang' placeholder='Mata Uang' className='bg-white'>
                    {currenciesList?.map(curr => (
                      <MenuItem key={curr.id} value={curr.id}>
                        {curr.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              )}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <Controller
              control={control}
              name='exchangeRate'
              render={({ field, fieldState: { error } }) => (
                <TextField
                  {...field}
                  fullWidth
                  aria-readonly
                  disabled={!!currentCurrency?.isDefault}
                  label='Konversi ke Rupiah'
                  InputLabelProps={{
                    shrink: !!field.value
                  }}
                  error={!!error}
                  InputProps={{
                    inputComponent: CurrencyField as any,
                    inputProps: {
                      prefix: 'Rp',
                      name: 'exchangeRate',
                      value: field.value,
                      allowLeadingZeros: false
                    }
                  }}
                />
              )}
            />
          </Grid>
          {/* Tax Invoice Number */}
          <Grid item xs={12} md={6}>
            <Controller
              control={control}
              name='taxInvoiceNumber'
              render={({ field }) => (
                <TextField
                  {...field}
                  fullWidth
                  label='No Faktur Pajak'
                  variant='outlined'
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position='end'>
                        <IconButton
                          aria-label='clear'
                          onClick={() => {
                            field.onChange('')
                          }}
                        >
                          <i className='fa fa-times-circle' />
                        </IconButton>
                      </InputAdornment>
                    )
                  }}
                />
              )}
            />
          </Grid>
          {/* Tax Invoice Date */}
          <Grid item xs={12} md={6}>
            <Controller
              control={control}
              name='taxInvoiceDate'
              render={({ field: { value, onChange }, fieldState: { error } }) => (
                <AppReactDatepicker
                  boxProps={{ className: 'is-full' }}
                  selected={value ? toDate(value) : undefined}
                  onChange={(date: Date) => onChange(formatISO(date))}
                  dateFormat='dd/MM/yyyy'
                  customInput={
                    <TextField fullWidth label='Tanggal Faktur Pajak' placeholder='Pilih tanggal' error={!!error} />
                  }
                />
              )}
            />
          </Grid>

          {/* Memo */}
          <Grid item xs={12}>
            <Controller
              control={control}
              name='note'
              render={({ field }) => (
                <TextField
                  {...field}
                  fullWidth
                  label='Memo'
                  placeholder='Tambahkan catatan (opsional)'
                  multiline
                  rows={2}
                  InputLabelProps={{
                    shrink: !!field.value
                  }}
                />
              )}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <Controller
              name='siteId'
              control={control}
              render={({ field: { value, onChange }, fieldState: { error } }) => (
                <FormControl fullWidth error={!!error}>
                  <InputLabel id='role-select'>Lokasi</InputLabel>
                  <Select
                    key={value}
                    fullWidth
                    id='select-siteId'
                    value={value}
                    onChange={e => {
                      onChange((e.target as HTMLInputElement).value)
                      setValue('projectLabelId', null)
                    }}
                    label='Lokasi'
                    size='medium'
                    labelId='siteId-select'
                    inputProps={{ placeholder: 'Pilih Lokasi' }}
                    defaultValue=''
                  >
                    {groupedSiteList.map(group => {
                      let children = []
                      children.push(
                        <ListSubheader
                          className='bg-green-50 text-primary font-semibold'
                          key={group.projectId ?? 'no_project'}
                        >
                          {group.project?.name || 'Tanpa Proyek'}
                        </ListSubheader>
                      )
                      group.sites.forEach(site => {
                        children.push(
                          <MenuItem key={site.id} value={site.id}>
                            <ListItemText primary={site.name} />
                          </MenuItem>
                        )
                      })
                      return children
                    })}
                  </Select>
                  {error && <FormHelperText>{error.message}</FormHelperText>}
                </FormControl>
              )}
            />
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  )
}

export default InvoiceDetail
