import {
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Typography,
  IconButton,
  TextField,
  FormHelperText
} from '@mui/material'
import { useFieldArray, useFormContext, useWatch, Controller } from 'react-hook-form'
import { useEffect, useMemo, useState } from 'react'
import AddServiceDialog from './AddServiceDialog'
import { isNullOrUndefined, toCurrency } from '@/utils/helper'
import { SalesInvoiceDtoType, SalesInvoiceItemDtoType } from '../config/schema'
import { calculateTax } from '@/utils/calculate-tax'
import { SalesInvoiceDiscountTypes, SalesInvoiceTaxTypes } from '@/types/salesInvoiceTypes'
import CurrencyField from '@/components/numeric/CurrencyField'
import { useAuth } from '@/contexts/AuthContext'
import { CurrenciesType } from '@/types/currenciesTypes'

type RowProps = {
  field: SalesInvoiceItemDtoType
  index: number
  remove: (index: number) => void
  handleEdit: (index: number) => void
  currentCurrency: CurrenciesType
}

const useCalculation = (index: number) => {
  const { control, getValues } = useFormContext<SalesInvoiceDtoType>()

  const [quantity, pricePerUnit, taxType, taxPercentage] = useWatch({
    control,
    name: [
      `items.${index}.quantity`,
      `items.${index}.pricePerUnit`,
      `items.${index}.taxType`,
      `items.${index}.taxPercentage`
    ],
    defaultValue: {}
  })

  const [discountType, discountValue] = useWatch({
    control,
    name: [`items.${index}.discountType`, `items.${index}.discountValue`],
    defaultValue: {
      discountValue: 0,
      discountType: SalesInvoiceDiscountTypes.PERCENTAGE
    }
  })

  const { totalItemDiscount, taxAmount, subtotalPriceIncludeTax } = useMemo(() => {
    let subtotalPriceIncludeTax = 0
    let totalItemDiscount = 0
    let taxAmount = 0

    if (pricePerUnit) {
      let subTotalPrice = pricePerUnit * quantity
      if (discountType && discountValue) {
        if (discountType === SalesInvoiceDiscountTypes.PERCENTAGE) {
          totalItemDiscount = Math.round(subTotalPrice * discountValue) / 100
        } else if (discountType === SalesInvoiceDiscountTypes.FLAT) {
          totalItemDiscount = discountValue
        }
        subTotalPrice = Math.round((subTotalPrice - totalItemDiscount) * 100) / 100
      }

      taxAmount = calculateTax(subTotalPrice, taxType, taxPercentage ?? 0)

      subtotalPriceIncludeTax = subTotalPrice

      if (taxType === SalesInvoiceTaxTypes.EXCLUDE_TAX) {
        subtotalPriceIncludeTax += taxAmount
      }
    }

    return { totalItemDiscount, taxAmount, subtotalPriceIncludeTax, taxType }
  }, [pricePerUnit, quantity, taxType, taxPercentage, discountType, discountValue])

  return { totalItemDiscount, taxAmount, subtotalPriceIncludeTax, taxType }
}

const Row = ({ field, index, remove, handleEdit, currentCurrency }: RowProps) => {
  const { control } = useFormContext<SalesInvoiceDtoType>()

  const { totalItemDiscount, taxAmount, subtotalPriceIncludeTax, taxType } = useCalculation(index)

  return (
    <TableRow key={field.itemId} sx={{ borderBottom: '1px solid rgba(76, 78, 100, 0.12)' }}>
      <TableCell sx={{ padding: '15px 16px 15px 20px', maxWidth: 160 }}>
        <div className='flex-col gap-1'>
          <Typography>
            {field.item?.number} | {field.item?.name}
          </Typography>
          {field.note && <Typography variant='caption'>Note: {field.note}</Typography>}
        </div>
      </TableCell>
      <TableCell sx={{ padding: '15px 16px 15px 20px', maxWidth: 160 }}>
        <Controller
          name={`items.${index}.pricePerUnit`}
          control={control}
          render={({ field }) => (
            <TextField
              {...field}
              size='small'
              variant='outlined'
              fullWidth
              onChange={e => {
                const value = parseFloat(e.target.value) || 0
                field.onChange(value)
              }}
              InputProps={{
                inputComponent: CurrencyField as any,
                inputProps: {
                  prefix: currentCurrency?.symbol ?? 'Rp',
                  name: `items.${index}.pricePerUnit`,
                  onChange: (e: any) => {
                    field.onChange(e.target.value)
                  },
                  value: field.value,
                  allowLeadingZeros: false,
                  isAllowed: ({ floatValue }) => floatValue >= 0 || floatValue === undefined
                },
                className: 'bg-white'
              }}
            />
          )}
        />
      </TableCell>
      <TableCell sx={{ padding: '15px 16px 15px 20px', width: 100 }}>
        <Controller
          name={`items.${index}.quantity`}
          control={control}
          render={({ field: controllerField }) => (
            <TextField
              {...controllerField}
              type='number'
              fullWidth
              size='small'
              variant='outlined'
              onChange={e => {
                controllerField.onChange(Number(e.target.value))
              }}
            />
          )}
        />
      </TableCell>
      <TableCell sx={{ padding: '15px 16px 15px 20px' }}>
        <Typography>{toCurrency(totalItemDiscount ?? 0, false, currentCurrency?.code)}</Typography>
      </TableCell>
      <TableCell sx={{ padding: '15px 16px 15px 20px' }}>
        <div className='flex flex-col'>
          <Typography className={`${taxType === SalesInvoiceTaxTypes.INCLUDE_TAX ? 'text-gray-400' : ''}`}>
            {toCurrency(taxAmount ?? 0, false, currentCurrency?.code)}
          </Typography>
          {taxType === SalesInvoiceTaxTypes.INCLUDE_TAX && (
            <Typography variant='caption' className='text-xxs'>
              *Harga Item Termasuk Pajak
            </Typography>
          )}
        </div>
      </TableCell>
      <TableCell sx={{ padding: '15px 16px 15px 20px' }}>
        <Typography className='text-primary'>
          {toCurrency(subtotalPriceIncludeTax ?? 0, false, currentCurrency?.code)}
        </Typography>
      </TableCell>
      <TableCell sx={{ padding: '15px 16px 15px 20px', maxWidth: 100 }}>
        <IconButton onClick={() => handleEdit(index)}>
          <i className='ri-edit-2-line text-textSecondary' />
        </IconButton>
        <IconButton onClick={() => remove(index)}>
          <i className='ri-delete-bin-line text-textSecondary' />
        </IconButton>
      </TableCell>
    </TableRow>
  )
}

const ItemsList = () => {
  const {
    control,
    getValues,
    formState: { errors }
  } = useFormContext<SalesInvoiceDtoType>()
  const { fields, append, remove, update } = useFieldArray({
    control,
    name: 'items'
  })

  const [addItemDialogOpen, setAddItemDialogOpen] = useState(false)
  const [editIndex, setEditIndex] = useState<number | null>(null)

  const { currenciesList } = useAuth()

  const currencyIdWatch = useWatch({
    control,
    name: 'currencyId'
  })

  const currentCurrency = currenciesList?.find(currency => currency.id === currencyIdWatch)

  const handleAddItem = (item: SalesInvoiceItemDtoType) => {
    if (editIndex !== null) {
      update(editIndex, item)
      setEditIndex(null)
    } else {
      append(item)
    }
  }

  const handleEdit = (index: number) => {
    setEditIndex(index)
    setAddItemDialogOpen(true)
  }

  return (
    <Card>
      <CardContent>
        <div className='flex justify-between items-center mb-4'>
          <Typography variant='h5'>Detil Jasa</Typography>
          <Button variant='contained' onClick={() => setAddItemDialogOpen(true)}>
            Tambah Jasa
          </Button>
        </div>
        <Box
          sx={{
            backgroundColor: 'white',
            borderRadius: '10px',
            boxShadow: '0px 2px 10px 0px rgba(76, 78, 100, 0.22)',
            overflowX: 'auto'
          }}
        >
          <Table sx={{ minWidth: '1000px' }}>
            <TableHead>
              <TableRow sx={{ backgroundColor: '#DBF7E8' }}>
                <TableCell
                  sx={{
                    padding: '16px 16px 16px 20px',
                    borderBottom: '1px solid rgba(76, 78, 100, 0.12)'
                  }}
                >
                  <Typography
                    sx={{
                      fontWeight: 500,
                      fontSize: '12px',
                      lineHeight: '2em',
                      letterSpacing: '1.42%',
                      textTransform: 'uppercase',
                      color: 'rgba(76, 78, 100, 0.87)',
                      whiteSpace: 'nowrap'
                    }}
                  >
                    Jasa
                  </Typography>
                </TableCell>
                <TableCell
                  sx={{
                    padding: '16px 16px 16px 20px',
                    borderBottom: '1px solid rgba(76, 78, 100, 0.12)'
                  }}
                >
                  <Typography
                    sx={{
                      fontWeight: 500,
                      fontSize: '12px',
                      lineHeight: '2em',
                      letterSpacing: '1.42%',
                      textTransform: 'uppercase',
                      color: 'rgba(76, 78, 100, 0.87)',
                      whiteSpace: 'nowrap'
                    }}
                  >
                    Harga Satuan
                  </Typography>
                </TableCell>
                <TableCell
                  sx={{
                    padding: '16px 16px 16px 20px',
                    borderBottom: '1px solid rgba(76, 78, 100, 0.12)',
                    width: 100
                  }}
                >
                  <Typography
                    sx={{
                      fontWeight: 500,
                      fontSize: '12px',
                      lineHeight: '2em',
                      letterSpacing: '1.42%',
                      textTransform: 'uppercase',
                      color: 'rgba(76, 78, 100, 0.87)',
                      whiteSpace: 'nowrap'
                    }}
                  >
                    Qty
                  </Typography>
                </TableCell>
                <TableCell
                  sx={{
                    padding: '16px 16px 16px 20px',
                    borderBottom: '1px solid rgba(76, 78, 100, 0.12)'
                  }}
                >
                  <Typography
                    sx={{
                      fontWeight: 500,
                      fontSize: '12px',
                      lineHeight: '2em',
                      letterSpacing: '1.42%',
                      textTransform: 'uppercase',
                      color: 'rgba(76, 78, 100, 0.87)',
                      whiteSpace: 'nowrap'
                    }}
                  >
                    Diskon
                  </Typography>
                </TableCell>
                <TableCell
                  sx={{
                    padding: '16px 16px 16px 20px',
                    borderBottom: '1px solid rgba(76, 78, 100, 0.12)'
                  }}
                >
                  <Typography
                    sx={{
                      fontWeight: 500,
                      fontSize: '12px',
                      lineHeight: '2em',
                      letterSpacing: '1.42%',
                      textTransform: 'uppercase',
                      color: 'rgba(76, 78, 100, 0.87)',
                      whiteSpace: 'nowrap'
                    }}
                  >
                    Pajak
                  </Typography>
                </TableCell>
                <TableCell
                  sx={{
                    padding: '16px 16px 16px 20px',
                    borderBottom: '1px solid rgba(76, 78, 100, 0.12)'
                  }}
                >
                  <Typography
                    sx={{
                      fontWeight: 500,
                      fontSize: '12px',
                      lineHeight: '2em',
                      letterSpacing: '1.42%',
                      textTransform: 'uppercase',
                      color: 'rgba(76, 78, 100, 0.87)',
                      whiteSpace: 'nowrap'
                    }}
                  >
                    Total Harga
                  </Typography>
                </TableCell>
                <TableCell
                  sx={{
                    padding: '16px 16px 16px 20px',
                    borderBottom: '1px solid rgba(76, 78, 100, 0.12)',
                    maxWidth: 100
                  }}
                >
                  <Typography
                    sx={{
                      fontWeight: 500,
                      fontSize: '12px',
                      lineHeight: '2em',
                      letterSpacing: '1.42%',
                      textTransform: 'uppercase',
                      color: 'rgba(76, 78, 100, 0.87)',
                      whiteSpace: 'nowrap'
                    }}
                  >
                    Action
                  </Typography>
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {fields.map((field, index) => (
                <Row
                  key={field.id}
                  field={field}
                  index={index}
                  remove={remove}
                  handleEdit={handleEdit}
                  currentCurrency={currentCurrency}
                />
              ))}
              {fields.length === 0 && (
                <TableRow>
                  <TableCell colSpan={8} align='center' sx={{ padding: '20px' }}>
                    <Typography color='textSecondary'>Belum ada Jasa</Typography>
                    <Typography variant='caption'>Semua Jasa yang telah dibuat akan ditampilkan di sini</Typography>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </Box>
        {errors?.items && <FormHelperText error>{errors?.items?.message}</FormHelperText>}
      </CardContent>
      {addItemDialogOpen && (
        <AddServiceDialog
          open={addItemDialogOpen}
          setOpen={setAddItemDialogOpen}
          onSubmit={handleAddItem}
          selectedService={!isNullOrUndefined(editIndex) ? getValues(`items.${editIndex}`) : undefined}
        />
      )}
    </Card>
  )
}

export default ItemsList
