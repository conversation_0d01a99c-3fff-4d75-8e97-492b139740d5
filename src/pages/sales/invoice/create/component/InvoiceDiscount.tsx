import {
  Card,
  CardContent,
  Typography,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  FormHelperText
} from '@mui/material'
import { useFormContext, Controller, useWatch } from 'react-hook-form'
import { useMemo } from 'react'

import { SalesInvoiceDiscountTypes } from '@/types/salesInvoiceTypes'
import CurrencyField from '@/components/numeric/CurrencyField'
import NumberField from '@/components/numeric/NumberField'
import { toCurrency } from '@/utils/helper'
import { useAuth } from '@/contexts/AuthContext'
import { SalesInvoiceDtoType } from '../config/schema'

// Discount type options for purchase invoice
const discountTypeOptions = [
  { value: SalesInvoiceDiscountTypes.PERCENTAGE, label: 'Persentase' },
  { value: SalesInvoiceDiscountTypes.FLAT, label: 'Nominal' }
]

const InvoiceDiscount = () => {
  const { control } = useFormContext<SalesInvoiceDtoType>()

  // Watch discount type and value for calculations
  const discountType = useWatch({
    control,
    name: 'discountType',
    defaultValue: SalesInvoiceDiscountTypes.PERCENTAGE
  })

  const discountAmount = useWatch({
    control,
    name: 'discountAmount',
    defaultValue: 0
  })

  const { currenciesList } = useAuth()
  const currencyIdWatch = useWatch({
    control,
    name: 'currencyId'
  })

  const currency = currenciesList?.find(curr => curr.id === currencyIdWatch)

  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-6'>
          <div className='flex justify-between items-start'>
            <Typography variant='h5'>Diskon Faktur (Opsional)</Typography>
          </div>

          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <Controller
                control={control}
                name='discountType'
                render={({ field, fieldState: { error } }) => (
                  <FormControl fullWidth error={!!error}>
                    <InputLabel>Tipe Diskon</InputLabel>
                    <Select {...field} label='Tipe Diskon'>
                      {discountTypeOptions.map(option => (
                        <MenuItem key={option.value} value={option.value}>
                          {option.label}
                        </MenuItem>
                      ))}
                    </Select>
                    {error && <FormHelperText>{error.message}</FormHelperText>}
                  </FormControl>
                )}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <Controller
                control={control}
                name='discountValue'
                render={({ field, fieldState: { error } }) => {
                  const isPercentage = discountType === SalesInvoiceDiscountTypes.PERCENTAGE
                  return (
                    <TextField
                      {...field}
                      fullWidth
                      label='Jumlah Diskon'
                      error={!!error}
                      helperText={error?.message}
                      InputProps={{
                        endAdornment: isPercentage ? '%' : '',
                        inputComponent: (isPercentage ? NumberField : CurrencyField) as any,
                        inputProps: {
                          prefix: isPercentage ? '' : currency?.symbol ?? 'Rp',
                          name: 'discountValue',
                          value: field.value,
                          allowLeadingZeros: false,
                          isAllowed: ({ floatValue }) =>
                            floatValue <= (isPercentage ? 100 : Number.MAX_SAFE_INTEGER) || floatValue === undefined
                        }
                      }}
                    />
                  )
                }}
              />
            </Grid>
          </Grid>

          {/* Discount Summary Section */}
          <div className='bg-[rgba(75,216,139,0.25)] rounded-lg p-4'>
            <div className='flex justify-center gap-4 items-center'>
              <Typography variant='body1' className='font-medium text-[#4C4E64]'>
                Diskon Faktur
              </Typography>
              <Typography variant='body1' className='font-bold text-[#4C4E64]'>
                {toCurrency(discountAmount, false, currency?.code)}
              </Typography>
            </div>
          </div>
        </CardContent>
      </Card>
    </>
  )
}

export default InvoiceDiscount
