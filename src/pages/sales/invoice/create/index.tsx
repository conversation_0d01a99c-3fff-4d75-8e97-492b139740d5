import Grid from '@mui/material/Grid'
import { Button, Typography } from '@mui/material'
import { FormProvider, useForm, useWatch } from 'react-hook-form'
import { useRouter } from '@/routes/hooks'
import { zodResolver } from '@hookform/resolvers/zod'
import LoadingButton from '@mui/lab/LoadingButton'
import { createSalesInvoiceSchema, SalesInvoiceDtoType } from './config/schema'
import InvoiceDetail from './component/InvoiceDetail'
import AttachmentDocument from './component/AttachmentDocument'
import ItemsList from './component/ItemsList'
import OtherExpenses from './component/OtherExpenses'
import InvoiceDiscount from './component/InvoiceDiscount'
import { CreateSalesInvoicePayload, SalesInvoiceDiscountTypes } from '@/types/salesInvoiceTypes'
import InvoiceSummary from './component/InvoiceSummary'
import { useCreateSalesInvoice } from '@/api/services/sales-invoice/mutation'
import { toast } from 'react-toastify'
import { useUploadDocument } from '@/api/services/file/mutation'
import { useMenu } from '@/components/menu/contexts/menuContext'
import { format } from 'date-fns'
import { ApproverType } from '@/types/userTypes'
import { useDraft } from '@/pages/draft/context/DraftContext'
import { DraftScope } from '@/types/draftsTypes'
import { useSearchParams } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'
import { DRAFT_QUERY_KEY } from '@/api/services/draft/service'
import DraftQueryMethods from '@/api/services/draft/query'
import { DefaultApprovalScope } from '@/pages/user/default-approval/config/enum'
import UserQueryMethods, { DEFAULT_APPROVER_QUERY_KEY } from '@/api/services/user/query'
import ApprovalListCard from '@/pages/material-request/create/components/ApprovalListCard'
import { useEffect } from 'react'
import Permission from '@/core/components/Permission'

// Transform form data to API payload
const transformFormDataToPayload = (
  formData: SalesInvoiceDtoType,
  approverList: ApproverType[]
): CreateSalesInvoicePayload => {
  return {
    customerId: formData.customerId,
    invoiceDate: formData.invoiceDate,
    paymentTerms: formData.paymentTerms,
    paymentDueDays: formData.paymentDueDays,
    note: formData.note || undefined,
    currencyId: formData.currencyId,
    exchangeRate: formData.exchangeRate,
    documentUploadId: formData.documentUploadId || undefined,
    taxInvoiceNumber: formData.taxInvoiceNumber,
    taxInvoiceDate: formData.taxInvoiceDate,
    approvals: approverList
      .filter(approval => approval.userId)
      .map(approval => ({
        userId: approval.userId!
      })),
    discountType: formData.discountType || SalesInvoiceDiscountTypes.PERCENTAGE,
    discountValue: formData.discountValue || 0,
    items: formData.items.map(item => ({
      itemId: item.itemId,
      quantity: item.quantity,
      quantityUnit: item.quantityUnit,
      largeUnitQuantity: item.largeUnitQuantity,
      pricePerUnit: item.pricePerUnit,
      taxType: item.taxType,
      taxId: item.taxId,
      taxPercentage: item.taxPercentage,
      discountType: item.discountType,
      discountValue: item.discountValue,
      isDiscountAfterTax: item.isDiscountAfterTax,
      note: item.note
    })),
    otherExpenses: (formData.otherExpenses || []).map(expense => ({
      accountId: expense.accountId,
      amount: expense.amount,
      note: expense.note,
      departmentId: expense.departmentId,
      siteId: expense.siteId
    })),
    departmentId: formData.departmentId,
    siteId: formData.siteId,
    projectLabelId: formData.projectLabelId
  }
}

const CreateSalesInvoicePage = () => {
  const router = useRouter()
  const { setConfirmState } = useMenu()
  const { createDraft, updateDraft, deleteDraft, loadingDraft } = useDraft()
  const [searchParams] = useSearchParams()

  const { mutateAsync: createSalesInvoice, isLoading: isCreating } = useCreateSalesInvoice()
  const { mutateAsync: uploadMutate, isLoading: uploadLoading } = useUploadDocument()

  const method = useForm<SalesInvoiceDtoType>({
    resolver: zodResolver(createSalesInvoiceSchema),
    mode: 'onChange',
    defaultValues: {
      discountType: SalesInvoiceDiscountTypes.PERCENTAGE,
      discountValue: 0,
      items: [],
      otherExpenses: []
    }
  })
  const { handleSubmit, getValues, control, reset } = method
  console.log(method.formState.errors)

  const grandTotalWatch = useWatch({
    control,
    name: 'grandTotal',
    defaultValue: 0
  })

  const siteId = useWatch({
    control,
    name: 'siteId',
    defaultValue: ''
  })

  const departmentId = useWatch({
    control,
    name: 'departmentId',
    defaultValue: ''
  })

  const { data: draftData } = useQuery({
    enabled: !!searchParams.get('draft'),
    queryKey: [DRAFT_QUERY_KEY, searchParams.get('draft')],
    queryFn: () => DraftQueryMethods.getOneDraft(searchParams.get('draft')),
    cacheTime: 0
  })

  const { data: approverList } = useQuery({
    enabled: !!siteId,
    queryKey: [DEFAULT_APPROVER_QUERY_KEY, DefaultApprovalScope.PurchaseInvoice, siteId],
    queryFn: () =>
      UserQueryMethods.getDefaultApproverList({
        limit: 1000,
        divisionId: 'null',
        scope: DefaultApprovalScope.SalesInvoice,
        siteId,
        departmentId: 'null'
        // departmentId
      }),
    placeholderData: []
  })

  const onSubmit = async (formData: SalesInvoiceDtoType) => {
    if ((approverList?.length ?? 0) <= 0) {
      toast.error('Default Approval belum tersedia. Silahkan hubungi admin terlebih dahulu.')
      return
    }
    setConfirmState({
      open: true,
      title: 'Buat Faktur Penjualan',
      content:
        'Apakah kamu yakin akan membuat Faktur Penjualan ini? Pastikan semua detil yang kamu masukkan untuk Faktur Penjualan ini sudah benar',
      confirmText: 'Buat Faktur',
      onConfirm: async () => {
        try {
          if (formData.documentContent && formData.documentName) {
            const uploadResponse = await uploadMutate({
              fieldName: `sales_invoice_document${format(new Date(), 'yyyyMMddHHmmss')}`,
              file: formData.documentContent,
              scope: 'public-document',
              fileName: formData.documentName
            })
            formData.documentUploadId = uploadResponse.data?.id
          }

          const payload = transformFormDataToPayload(formData, approverList) // TODO: Add approver list
          const response = await createSalesInvoice(payload)
          toast.success('Faktur penjualan berhasil dibuat!')
          router.replace(`/sales/invoice/list/${response.data.id}`)
        } catch (error: any) {
          console.error('Error creating sales invoice:', error)
          toast.error(error?.response?.data?.message || 'Gagal membuat faktur penjualan')
        }
      }
    })
  }

  const onSaveDraft = () => {
    const formData = getValues()
    setConfirmState({
      open: true,
      title: 'Simpan Draft Faktur Penjualan',
      content:
        'Apakah kamu yakin akan menyimpan draft Faktur Penjualan ini? Pastikan semua detil yang kamu masukkan sudah benar',
      confirmText: 'Simpan',
      onConfirm: async () => {
        try {
          let draftForm = { ...formData }
          if (draftForm.documentContent && draftForm.documentName) {
            const uploadResponse = await uploadMutate({
              fieldName: `sales_invoice_document${format(new Date(), 'yyyyMMddHHmmss')}`,
              file: draftForm.documentContent,
              scope: 'public-document',
              fileName: draftForm.documentName
            })
            draftForm = {
              ...draftForm,
              documentUploadId: uploadResponse.data?.id,
              documentContent: undefined as any
            }
          }

          const stringifyPayload = JSON.stringify(draftForm)
          if (draftData) {
            updateDraft(
              {
                draftId: draftData.id,
                payload: stringifyPayload,
                siteId: draftForm.siteId ?? undefined
              },
              {
                onSuccess: () => {
                  toast.success('Faktur Penjualan disimpan sebagai draft.')
                  router.replace('/sales/invoice/draft')
                }
              }
            )
          } else {
            createDraft(
              {
                scope: DraftScope['SALES-INVOICE'],
                payload: stringifyPayload,
                siteId: draftForm.siteId ?? undefined
              },
              {
                onSuccess: () => {
                  toast.success('Faktur Penjualan disimpan sebagai draft.')
                  router.replace('/sales/invoice/draft')
                }
              }
            )
          }
        } catch (error: any) {
          console.error('Error saving draft:', error)
          toast.error(error?.response?.data?.message || 'Gagal menyimpan draft')
        }
      }
    })
  }

  useEffect(() => {
    if (draftData) {
      try {
        const parsed = JSON.parse(draftData.payload) as any
        const normalized = {
          ...parsed,
          // Backward compatibility: older drafts might use `paymentMethod`
          paymentTerms: parsed?.paymentTerms ?? parsed?.paymentMethod ?? ''
        } as SalesInvoiceDtoType
        reset(normalized)
      } catch (e) {
        // ignore parse error
      }
    }
  }, [draftData])

  return (
    <FormProvider {...method}>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <div className='flex flex-col md:flex-row justify-between md:items-end gap-4'>
            <div className='flex flex-col'>
              <Typography variant='h4'>Buat Faktur Penjualan</Typography>
              <Typography>Buat faktur penjualan baru</Typography>
            </div>
            <div className='flex gap-2'>
              <Button
                color='secondary'
                variant='outlined'
                className='is-full sm:is-auto'
                onClick={() => router.replace(`/sales/invoice/list`)}
              >
                Batalkan
              </Button>
              <LoadingButton
                className='is-full sm:is-auto'
                startIcon={<></>}
                variant='outlined'
                loading={loadingDraft}
                onClick={onSaveDraft}
              >
                Simpan Draft
              </LoadingButton>
              <Permission permission='sales-invoice.create'>
                <LoadingButton
                  startIcon={<></>}
                  variant='contained'
                  className='is-full sm:is-auto'
                  loading={isCreating || uploadLoading}
                  onClick={handleSubmit(onSubmit)}
                >
                  Buat Faktur
                </LoadingButton>
              </Permission>
            </div>
          </div>
        </Grid>
        <Grid item xs={12}>
          <Grid container spacing={4}>
            <Grid item xs={12} md={6}>
              <InvoiceDetail />
            </Grid>
            <Grid item xs={12} md={6}>
              <Grid container spacing={4}>
                <Grid item xs={12}>
                  <AttachmentDocument />
                </Grid>
                {approverList?.length > 0 && (
                  <Grid item xs={12}>
                    <ApprovalListCard
                      approverList={
                        approverList?.map(approver => ({ ...approver.user, threshold: approver.threshold ?? 0 })) ?? []
                      }
                      scope={DefaultApprovalScope.PurchaseInvoice}
                      amount={grandTotalWatch}
                    />
                  </Grid>
                )}
              </Grid>
            </Grid>
            <Grid item xs={12}>
              <ItemsList />
            </Grid>
            <Grid item xs={12}>
              <OtherExpenses />
            </Grid>
            {/* <Grid item xs={12}>
              <InvoiceDiscount />
            </Grid> */}
            <Grid item xs={12}>
              <InvoiceSummary />
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </FormProvider>
  )
}

export default CreateSalesInvoicePage
