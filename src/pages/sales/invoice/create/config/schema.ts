import { SalesInvoiceDiscountTypes, SalesInvoicePaymentTerms, SalesInvoiceTaxTypes } from '@/types/salesInvoiceTypes'
import { object, z } from 'zod'

const itemSchema = z.object({
  itemId: z.string().min(1),
  quantity: z.number().int().min(1),
  quantityUnit: z.string().min(1),
  largeUnitQuantity: z.number().optional().nullable(),
  pricePerUnit: z.number().min(0),
  taxType: z.nativeEnum(SalesInvoiceTaxTypes),
  taxId: z.string().optional().nullable(),
  taxPercentage: z.number().optional().nullable(),
  discountType: z.nativeEnum(SalesInvoiceDiscountTypes).optional().nullable(),
  discountValue: z.number().optional().nullable(),
  isDiscountAfterTax: z.boolean().optional().nullable(),
  note: z.string().optional().nullable(),
  // UI only
  item: object({
    name: z.string(),
    number: z.string()
  }).optional(),
  totalItemDiscount: z.number().optional(),
  taxAmount: z.number().optional(),
  subtotalPriceIncludeTax: z.number().optional()
})

const otherExpenseSchema = z.object({
  accountId: z.string().min(1),
  siteId: z.string().optional().nullable(),
  departmentId: z.string().optional().nullable(),
  amount: z.number(),
  note: z.string().optional(),
  site: z
    .object({
      id: z.string().optional().nullable(),
      name: z.string().optional().nullable()
    })
    .optional()
    .nullable(),
  department: z
    .object({
      id: z.string().optional().nullable(),
      name: z.string().optional().nullable()
    })
    .optional()
    .nullable()
})

export const createSalesInvoiceSchema = z
  .object({
    customerId: z.string().min(1, 'Customer harus diisi'),
    invoiceDate: z.string().min(1, 'Tanggal faktur harus diisi'),
    paymentTerms: z.nativeEnum(SalesInvoicePaymentTerms, {
      errorMap: () => ({ message: 'Metode pembayaran harus diisi' })
    }),
    paymentDueDays: z.number().optional(),
    note: z.string().optional().nullable(),
    currencyId: z.string(),
    receivableAccountId: z.string().optional().nullable(),
    exchangeRate: z.number(),
    documentUploadId: z.string().optional().nullable(),
    documentContent: z.any().optional().nullable(),
    documentName: z.string().optional().nullable(),
    departmentId: z.string().optional().nullable(),
    siteId: z.string().optional().nullable(),
    projectLabelId: z.number().optional().nullable(),
    items: z.array(itemSchema).min(1, 'Minimal 1 barang'),
    otherExpenses: z.array(otherExpenseSchema).optional(),
    discountType: z.nativeEnum(SalesInvoiceDiscountTypes).optional().nullable(),
    discountValue: z.number().optional().nullable(),
    taxInvoiceNumber: z.string().optional().nullable(),
    taxInvoiceDate: z.string().optional().nullable(),

    // UI only
    subTotalAmount: z.number().optional(),
    taxAmount: z.number().optional(),
    discountAmount: z.number().optional(),
    otherAmount: z.number().optional(),
    totalAmount: z.number().optional(),
    grandTotal: z.number().optional()
  })
  .refine(
    data => {
      if (data.paymentTerms === SalesInvoicePaymentTerms.NET) {
        return data.paymentDueDays && data.paymentDueDays > 0
      }
      return true
    },
    {
      message: 'Jatuh tempo harus diisi jika metode pembayaran NET',
      path: ['paymentDueDays']
    }
  )

export type SalesInvoiceDtoType = z.infer<typeof createSalesInvoiceSchema>
export type SalesInvoiceItemDtoType = z.infer<typeof itemSchema>
export type SalesInvoiceOtherExpenseDtoType = z.infer<typeof otherExpenseSchema>
