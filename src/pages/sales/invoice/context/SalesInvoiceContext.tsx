import { createContext, ReactNode, useContext, useState, useEffect } from 'react'
import { useParams } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'
import { defaultListData } from '@/api/queryClient'
import { ListResponse } from '@/types/api'
import { SalesInvoice, SalesInvoiceLog, SalesInvoiceParams, SalesInvoiceStatuses } from '@/types/salesInvoiceTypes'
import usePartialState from '@/core/hooks/usePartialState'
import SalesInvoiceQueryMethods, {
  SALES_INVOICE_LIST_QUERY_KEY,
  SALES_INVOICE_LOGS_QUERY_KEY,
  SALES_INVOICE_QUERY_KEY
} from '@/api/services/sales-invoice/query'
import { usePathname } from '@/routes/hooks'
import { useAuth } from '@/contexts/AuthContext'

interface SelectedSalesInvoice {
  salesInvoiceId?: string
}

interface SalesInvoiceContextProps {
  salesInvoiceListResponse: ListResponse<SalesInvoice>
  salesInvoiceParams: SalesInvoiceParams
  setSalesInvoiceParams: (params: SalesInvoiceParams) => void
  setPartialSalesInvoiceParams: (key: keyof SalesInvoiceParams, value: any) => void
  setSelectedSalesInvoice: (selected: SelectedSalesInvoice) => void
  selectedSalesInvoiceId?: string
  fetchSalesInvoiceList: () => void
  // Detail page data
  salesInvoiceData?: SalesInvoice
  fetchSalesInvoiceData: () => void
  canView: boolean
  canUpdate: boolean
  isApprovalPage: boolean
  salesInvoiceLogs: SalesInvoiceLog[]
  fetchSalesInvoiceLogs: () => void
}

export const SalesInvoiceContext = createContext<SalesInvoiceContextProps>({} as SalesInvoiceContextProps)

interface SalesInvoiceContextProviderProps {
  children: ReactNode
}

export const useSalesInvoice = () => {
  return useContext(SalesInvoiceContext)
}

export function SalesInvoiceContextProvider({ children }: SalesInvoiceContextProviderProps) {
  const { salesInvoiceId } = useParams()

  const pathname = usePathname()
  const { ownSiteList } = useAuth()

  const isApprovalPage = pathname.includes('/sales/invoice/approval')
  const isCashBankPage = pathname.includes('cash-bank/sales-invoice')

  const [salesInvoiceParams, setSalesInvoiceParams] = useState<SalesInvoiceParams>({
    limit: 10,
    page: 1,
    search: ''
  })

  const [selectedSalesInvoice, setSelectedSalesInvoice] = useState<SelectedSalesInvoice>({
    salesInvoiceId: salesInvoiceId
  })

  const [, setPartialSalesInvoiceParams] = usePartialState(setSalesInvoiceParams)

  // Fetch sales invoice list for approval
  const { data: salesInvoiceListResponse, refetch: fetchSalesInvoiceList } = useQuery({
    queryKey: [SALES_INVOICE_LIST_QUERY_KEY, JSON.stringify(salesInvoiceParams), isApprovalPage, isCashBankPage],
    queryFn: () => {
      if (isApprovalPage) {
        return SalesInvoiceQueryMethods.getSalesInvoiceToMe(salesInvoiceParams)
      } else {
        return SalesInvoiceQueryMethods.getSalesInvoiceList({
          ...salesInvoiceParams,
          ...(isCashBankPage && { status: SalesInvoiceStatuses.APPROVED })
        })
      }
    },
    placeholderData: defaultListData as ListResponse<SalesInvoice>
  })

  // Fetch sales invoice detail
  const { data: salesInvoiceData, refetch: fetchSalesInvoiceData } = useQuery({
    queryKey: [SALES_INVOICE_QUERY_KEY, salesInvoiceId],
    queryFn: () => SalesInvoiceQueryMethods.getSalesInvoice(salesInvoiceId!),
    enabled: !!salesInvoiceId
  })

  const { data: salesInvoiceLogs, refetch: fetchSalesInvoiceLogs } = useQuery({
    queryKey: [SALES_INVOICE_LOGS_QUERY_KEY, salesInvoiceId],
    queryFn: () => SalesInvoiceQueryMethods.getSalesInvoiceLogs(salesInvoiceId!),
    enabled: !!salesInvoiceId,
    select: data => data.items
  })

  useEffect(() => {
    setSelectedSalesInvoice(curr => ({ ...curr, salesInvoiceId: salesInvoiceId }))
  }, [salesInvoiceId])

  useEffect(() => {
    setSalesInvoiceParams({
      ...salesInvoiceParams,
      siteIds: ownSiteList.map(site => site.id) as string[]
    })
  }, [ownSiteList])

  // Permission checks (simplified for now)
  const canView = true
  const canUpdate = true

  const value = {
    salesInvoiceListResponse: salesInvoiceListResponse ?? defaultListData,
    salesInvoiceParams,
    setSalesInvoiceParams,
    setPartialSalesInvoiceParams,
    setSelectedSalesInvoice,
    selectedSalesInvoiceId: selectedSalesInvoice.salesInvoiceId,
    fetchSalesInvoiceList,
    // Detail page data
    salesInvoiceData,
    fetchSalesInvoiceData,
    canView,
    canUpdate,
    isApprovalPage,
    salesInvoiceLogs: salesInvoiceLogs ?? [],
    fetchSalesInvoiceLogs
  }

  return (
    <SalesInvoiceContext.Provider value={value}>
      <>{children}</>
    </SalesInvoiceContext.Provider>
  )
}
