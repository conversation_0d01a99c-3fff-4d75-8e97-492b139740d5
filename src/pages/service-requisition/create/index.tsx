import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Grid, Typo<PERSON> } from '@mui/material'
import { Link, useParams } from 'react-router-dom'
import ItemListCard from './component/ItemListCard'
import AdditionalInfoCard from './component/AdditionalInfoCard'
import SegmentCard from './component/SegmentCard'
import ServiceRequestCard from './component/ServiceRequestCard'
import ApprovalListCard from './component/ApprovalListCard'
import { useAuth } from '@/contexts/AuthContext'
import { useQuery } from '@tanstack/react-query'
import UserQueryMethods, { DEFAULT_APPROVER_QUERY_KEY } from '@/api/services/user/query'
import { DefaultApprovalScope } from '@/pages/user/default-approval/config/enum'
import { useFormContext } from 'react-hook-form'
import { SrRmOPayload } from '@/types/srTypes'
// import { useWoSr } from '../context/WoSrContext'
import { useEffect } from 'react'
import LoadingButton from '@mui/lab/LoadingButton'
import * as Sentry from '@sentry/react'
import { toast } from 'react-toastify'
import { useSrForm } from '../context/SrFormContext'
import UnitCard from './component/UnitCard'
import UnitDataCard from './component/UnitDataCard'
import { useSr } from '../context/SrContext'

const CreateWoSr = () => {
  const { userProfile } = useAuth()
  const params = useParams()
  const { handleSubmitServiceRequest, addLoading, navigate, woDetail } = useSrForm()

  const { handleSubmit, reset, getValues } = useFormContext<SrRmOPayload>()

  const scope = DefaultApprovalScope.ServiceRequisition

  const { data: approverList } = useQuery({
    enabled: !!woDetail?.siteId && !!userProfile?.departmentId,
    queryKey: [DEFAULT_APPROVER_QUERY_KEY, scope, woDetail?.siteId, userProfile?.departmentId],
    queryFn: () =>
      UserQueryMethods.getDefaultApproverList({
        limit: 1000,
        divisionId: 'null',
        scope,
        siteId: woDetail?.siteId,
        departmentId: 'null'
        // departmentId: userProfile?.departmentId
      }),
    placeholderData: []
  })

  useEffect(() => {
    reset({
      ...getValues(),
      workOrderSegmentId: params?.segmentId,
      priority: 4
    })
  }, [woDetail])

  useEffect(() => {
    reset({
      ...getValues(),
      approvals: approverList?.map(approver => ({
        userId: approver.user?.id
      }))
    })
  }, [approverList])

  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Breadcrumbs aria-label='breadcrumb'>
          <Link to='/service-request/list' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Service Request</Typography>
          </Link>
          <Typography>Buat Service Request</Typography>
        </Breadcrumbs>
      </Grid>
      <Grid item xs={12}>
        <div className='flex justify-between items-end flex-col sm:flex-row gap-2 max-sm:items-center'>
          <div className='flex flex-col max-sm:text-center'>
            <Typography variant='h4'>Buat Service Request</Typography>
            <Typography>Buat Service Request untuk diajukan kepada eselon terkait</Typography>
          </div>
          <div className='flex flex-col sm:flex-row gap-2 is-full sm:is-auto'>
            <Button color='secondary' variant='outlined' disabled={addLoading} onClick={() => navigate(-1)}>
              Batalkan
            </Button>
            <LoadingButton
              startIcon={<></>}
              loading={addLoading}
              variant='contained'
              onClick={handleSubmit(handleSubmitServiceRequest, errors => {
                console.error(errors)
                Sentry.captureException(errors)
                // Object.entries(errors).forEach(([field, error]) => {
                //   toast.error(`${field}: ${error?.message}`, {
                //     autoClose: 5000
                //   })
                // })
              })}
            >
              Buat Service Request
            </LoadingButton>
          </div>
        </div>
      </Grid>
      <Grid item xs={12}>
        <SegmentCard />
      </Grid>
      <Grid item xs={12}>
        <ItemListCard />
      </Grid>
      <Grid item xs={12} md={6}>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <AdditionalInfoCard />
          </Grid>
          <Grid item xs={12}>
            <UnitCard />
          </Grid>
          <Grid item xs={12}>
            <UnitDataCard />
          </Grid>
        </Grid>
      </Grid>
      <Grid item xs={12} md={6}>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <ServiceRequestCard />
          </Grid>
          <Grid item xs={12}>
            <ApprovalListCard approverList={approverList?.map(approver => approver.user) ?? []} />
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  )
}

export default CreateWoSr
