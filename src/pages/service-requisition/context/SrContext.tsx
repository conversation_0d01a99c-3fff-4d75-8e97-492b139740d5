import { create<PERSON>ontext, <PERSON>actN<PERSON>, useContext, useEffect, useState } from 'react'
import {
  QueryObserverResult,
  RefetchOptions,
  RefetchQueryFilters,
  UseMutateAsyncFunction,
  UseMutateFunction,
  useQuery
} from '@tanstack/react-query'
import usePartialState from '@/core/hooks/usePartialState'
import { ApiResponse, ListResponse } from '@/types/api'
import { defaultListData } from '@/api/queryClient'
import { useParams } from 'react-router-dom'
import { usePathname } from '@/routes/hooks'
import {
  ServiceRequisition,
  ServiceRequisitionLog,
  ServiceRequisitionParams,
  ServiceRequisitionPayload
} from '@/types/serviceRequisitionsTypes'
import { useAddSr } from '@/api/services/service-requisitions/mutation'
import ServiceRequisitionQueryMethods, {
  SR_QUERY_KEY,
  SR_LOG_QUERY_KEY,
  SR_LIST_QUERY_KEY
} from '@/api/services/service-requisitions/query'
import { useAuth } from '@/contexts/AuthContext'
import { useUploadImage } from '@/api/services/file/mutation'
import { FileType } from '@/types/fileTypes'
import { UploadPayload } from '@/types/payload'
import { DefaultApprovalScope } from '@/pages/user/default-approval/config/enum'
import CancelSrDialog from '@/components/dialogs/cancel-sr-dialog'
import { WorkOrderType } from '@/types/woTypes'
import { SiteOutlineType } from '@/types/companyTypes'
import RnMQueryMethods from '@/api/services/rnm/query'
import { WO_QUERY_DETAIL_KEY, WO_QUERY_LIST_KEY } from '@/api/services/rnm/service'
import { QueryFn, QueryListResponseFn } from '@/types/alias'

interface SrContextProps {
  showLoading: boolean
  srListResponse: ListResponse<ServiceRequisition>
  srsParams: ServiceRequisitionParams
  setSrsParams: React.Dispatch<React.SetStateAction<ServiceRequisitionParams>>
  fetchSrList: QueryListResponseFn<ServiceRequisition>
  srData: ServiceRequisition
  clearSrData: () => void
  selectedSrId?: string
  setSelectedSrId: React.Dispatch<React.SetStateAction<string>>
  setPartialSrsParams: (fieldName: keyof ServiceRequisitionParams, value: any) => void
  fetchSrData: <TPageData>(
    options?: RefetchOptions & RefetchQueryFilters<TPageData>
  ) => Promise<QueryObserverResult<ServiceRequisition, unknown>>
  uploadMutate: UseMutateAsyncFunction<ApiResponse<FileType>, Error, UploadPayload, void>
  createSrMutate: UseMutateFunction<ApiResponse<ServiceRequisition>, Error, ServiceRequisitionPayload, void>
  logList: ServiceRequisitionLog[]
  fetchLogList: QueryFn<ServiceRequisitionLog[]>
  refreshData: () => void
  canUpdate: boolean
  handleCancelSr: () => void
  canRemove: boolean
  workOrder?: WorkOrderType
  fetchWorkOrder: QueryFn<WorkOrderType>
}

export const SrContext = createContext<SrContextProps>({} as SrContextProps)

interface SrContextProviderProps {
  children: ReactNode
}

export const useSr = () => {
  return useContext(SrContext)
}

export function SrContextProvider({ children }: SrContextProviderProps) {
  const pathname = usePathname()
  const { accountPermissions, userProfile } = useAuth()
  const { srId, subSrId } = useParams()
  const [selectedSrId, setSelectedSrId] = useState<string>(subSrId ?? srId)

  const [cancelSrDialogOpen, setCancelSrDialogOpen] = useState(false)

  const [srsParams, setPartialSrsParams, setSrsParams] = usePartialState<ServiceRequisitionParams>({
    limit: 10,
    page: 1
  })

  const { mutateAsync: uploadMutate, isLoading: uploadLoading } = useUploadImage()
  const { mutate: createSrMutate, isLoading: createSrLoading } = useAddSr()

  const canUpdate = accountPermissions.includes(`${DefaultApprovalScope.ServiceRequisition}.update`)
  const isApprovalPage = pathname.includes('/service-request/approval')

  const {
    data: srData,
    refetch: fetchSrData,
    isFetching: fetchSrDataLoading,
    remove: removeSrData
  } = useQuery({
    enabled: !!selectedSrId,
    queryKey: [SR_QUERY_KEY, selectedSrId],
    queryFn: () => ServiceRequisitionQueryMethods.getSr(selectedSrId)
  })

  const canRemove = srData?.isEditable && canUpdate

  const {
    data: logList,
    refetch: fetchLogList,
    remove: removeSrLogList
  } = useQuery({
    enabled: !!selectedSrId,
    queryKey: [SR_LOG_QUERY_KEY, selectedSrId],
    queryFn: () => ServiceRequisitionQueryMethods.getSrLogList(selectedSrId, { limit: 10000 })
  })

  const {
    data: srListResponse,
    refetch: fetchSrList,
    isFetching: fetchSrsLoading
  } = useQuery({
    enabled: (accountPermissions?.length ?? 0) > 0,
    queryKey: [SR_LIST_QUERY_KEY, JSON.stringify(srsParams), canUpdate, isApprovalPage],
    queryFn: () => {
      const siteIdsUser = userProfile?.sites.map(site => site.id).join(',')
      const { search, status, startDate, endDate, siteIds, ...params } = srsParams
      const payload = {
        ...(search && { search }),
        ...(status && !isApprovalPage && { status }),
        ...(!!startDate && !!endDate && { startDate, endDate }),
        ...(siteIds ? { siteIds } : { siteIds: !!siteIdsUser ? siteIdsUser : undefined }),
        ...params
      }
      if (isApprovalPage) {
        return ServiceRequisitionQueryMethods.getToMeSrList(payload)
      }
      if (canUpdate) {
        return ServiceRequisitionQueryMethods.getSrList(payload)
      } else {
        return ServiceRequisitionQueryMethods.getByMeSrList(payload)
      }
    },
    placeholderData: defaultListData as ListResponse<ServiceRequisition>
  })

  const { data: workOrder, refetch: fetchWorkOrder } = useQuery({
    enabled: false,
    queryKey: [WO_QUERY_DETAIL_KEY, srData?.workOrderId],
    queryFn: () => RnMQueryMethods.getWoDetail(srData?.workOrderId)
  })

  const handleCancelSr = () => {
    setCancelSrDialogOpen(true)
  }

  const clearSrData = () => {
    removeSrData()
    removeSrLogList()
    setSelectedSrId(undefined)
  }

  const refreshData = () => {
    fetchSrData()
    fetchLogList()
    fetchSrList()
  }

  useEffect(() => {
    setSrsParams({
      limit: 10,
      page: 1
    })
  }, [])

  const value = {
    srListResponse: srListResponse ?? defaultListData,
    showLoading: uploadLoading || createSrLoading,
    fetchSrList,
    srsParams,
    setPartialSrsParams,
    setSrsParams,
    srData,
    selectedSrId,
    setSelectedSrId,
    clearSrData,
    fetchSrData,
    uploadMutate,
    createSrMutate,
    logList,
    fetchLogList,
    refreshData,
    canUpdate,
    handleCancelSr,
    canRemove,
    workOrder,
    fetchWorkOrder
  }

  return (
    <SrContext.Provider value={value}>
      <>
        {children}
        {cancelSrDialogOpen ? <CancelSrDialog open={cancelSrDialogOpen} setOpen={setCancelSrDialogOpen} /> : null}
      </>
    </SrContext.Provider>
  )
}
