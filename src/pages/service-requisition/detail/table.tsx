import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>lt<PERSON>, Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'
import { ServiceRequisitionItem } from '@/types/serviceRequisitionsTypes'
import truncateString from '@/core/utils/truncate'

type DataTypeWithAction = ServiceRequisitionItem & {
  action?: string
}

type RowActionType = {
  onView: (item: ServiceRequisitionItem) => void
}

// Column Definitions
const columnHelper = createColumnHelper<DataTypeWithAction>()

export const tableColumns = (rowAction: RowActionType) => [
  columnHelper.accessor('item.number', {
    header: 'Kode Barang',
    cell: ({ row }) => <Typography>{row.original.item?.number}</Typography>
  }),
  columnHelper.accessor('serialNumber', {
    header: 'No. Serial',
    cell: ({ row }) => <Typography>{row.original?.serialNumber?.number ?? '-'}</Typography>
  }),
  columnHelper.accessor('item.name', {
    header: '<PERSON><PERSON>',
    cell: ({ row }) => <Typography>{row.original.item?.name ?? '-'}</Typography>
  }),
  columnHelper.accessor('item.brandName', {
    header: 'Merk Barang',
    cell: ({ row }) => <Typography>{row.original.item?.brandName}</Typography>
  }),
  columnHelper.accessor('quantity', {
    header: 'QTY',
    cell: ({ row }) => (
      <Typography>
        {row.original.quantity} {row.original.quantityUnit}
      </Typography>
    )
  }),
  columnHelper.accessor('note', {
    header: 'Keterangan',
    cell: ({ row }) => (
      <Tooltip sx={{ cursor: 'default' }} title={row.original.note}>
        <Typography>{truncateString(row.original.note ?? '-', 10)}</Typography>
      </Tooltip>
    )
  }),
  columnHelper.accessor('action', {
    header: 'Action',
    cell: ({ row }) => (
      <div className='flex items-center gap-0.5'>
        <IconButton size='small' onClick={() => rowAction.onView(row.original)}>
          <i className='ri-eye-line text-secondary' />
        </IconButton>
      </div>
    ),
    enableSorting: false
  })
]
