// MUI Imports

import Grid from '@mui/material/Grid'

// Type Imports
import { Breadcrum<PERSON>, Button, Chip, Typography } from '@mui/material'

import { useSr } from '../context/SrContext'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { statusChipColor } from '../list/config/table'
import { Link } from 'react-router-dom'
import { srStatusOptions } from '../list/config/utils'
import OwnerCard from '@/pages/material-request/detail/components/OwnerCard'
import ApprovalDetailCard from '@/pages/material-request/detail/components/ApprovalDetailCard'
import ActivityLogCard from './components/ActivityLogCard'
import { useEffect, useState, useMemo } from 'react'
import { ServiceRequisitionStatus, ServiceRequisition } from '@/types/serviceRequisitionsTypes'
import ItemListCard from '../approval-detail/components/ItemListCard'
import { EditApproverInput } from '@/components/dialogs/edit-approver-dialog'
import { useUpdateSrApprover } from '@/api/services/service-requisitions/mutation'
import { toast } from 'react-toastify'
import { ApproverType } from '@/types/userTypes'
import AdditionalInfoCard from './components/AdditionalInfoCard'
import DocNumberCard from '@/pages/material-request/mr-in/component/DocNumberCard'
import SegmentInfoCard from './components/SegmentInfoCard'
import UnitCard from './components/UnitCard'
import RequestDetailCard from './components/RequestDetailCard'
import { useQuery } from '@tanstack/react-query'
import CompanyQueryMethods, { VENDOR_QUERY_KEY } from '@/api/services/company/query'
import SrPdfDocument from './components/SrPdfDocument'
import { pdf } from '@react-pdf/renderer'

const SrDetailPage = () => {
  const {
    srData: rawSrData,
    logList: rawLogList,
    handleCancelSr,
    canRemove,
    fetchSrData,
    fetchLogList,
    fetchWorkOrder,
    workOrder: rawWorkOrder
  } = useSr()

  const { mutate: updateApproverMutate, isLoading: updateApproverLoading } = useUpdateSrApprover()

  const handleUpdateApprover = (formData: EditApproverInput) => {
    updateApproverMutate(
      {
        srId: rawSrData?.id,
        approvalId: formData.approvalId,
        ...formData
      },
      {
        onSuccess: () => {
          toast.success('Penerima Pengajuan berhasil diganti')
          fetchSrData()
          fetchLogList()
        },
        onError: error => {
          const message = error?.message
          if (message) {
            toast.error(message)
          } else {
            toast.error('Oops telah terjadi kesalahan, silahkan coba beberapa saat lagi.')
          }
        }
      }
    )
  }

  const [srData, setSrData] = useState<ServiceRequisition>(rawSrData)

  const isClosed = rawSrData?.isClosed && rawSrData?.status !== ServiceRequisitionStatus.CANCELED

  const { data: vendorData } = useQuery({
    enabled: !!srData?.vendorId,
    queryKey: [VENDOR_QUERY_KEY, srData?.vendorId],
    queryFn: () => CompanyQueryMethods.getVendor(srData?.vendorId)
  })

  // Use the raw log list directly
  const logList = useMemo(() => {
    return rawLogList || []
  }, [rawLogList])

  const handlePrint = async () => {
    if (srData) {
      const blob = await pdf(
        <SrPdfDocument segment={srData?.workOrderSegment} woData={srData?.workOrder} srData={srData} />
      ).toBlob()
      const url = URL.createObjectURL(blob)
      const printWindow = window.open(url, '_blank')
      printWindow.onload = () => {
        printWindow.print()
        printWindow.onafterprint = () => {
          printWindow.close()
        }
      }
    }
  }

  useEffect(() => {
    setSrData(rawSrData)
  }, [rawSrData])

  useEffect(() => {
    if (!!srData?.workOrderId) {
      fetchWorkOrder()
    }
  }, [srData])

  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Breadcrumbs aria-label='breadcrumb'>
          <Link to='#' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Service Request</Typography>
          </Link>
          <Link to='/service-request/list' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Service Request Terbuat</Typography>
          </Link>
          <Typography>Detil Service Request</Typography>
        </Breadcrumbs>
      </Grid>
      <Grid item xs={12}>
        <div className='flex justify-between items-end flex-col sm:flex-row max-sm:items-center gap-2'>
          <div className='flex flex-col'>
            <div className='flex items-center gap-2 flex-col sm:flex-row max-sm:text-center'>
              <Typography variant='h4'>No. SR: {rawSrData?.number}</Typography>
              <Chip
                label={isClosed ? 'Selesai' : srStatusOptions.find(option => option.value === rawSrData?.status)?.label}
                color={isClosed ? 'info' : statusChipColor[rawSrData?.status]?.color}
                variant='tonal'
                size='small'
              />
            </div>
            <Typography className='max-sm:text-center max-sm:mt-2'>
              {formatDate(rawSrData?.createdAt ?? Date.now(), 'eeee, dd/MM/yyyy, HH:mm', { locale: id })}
            </Typography>
          </div>
          <div className='flex gap-2 flex-col sm:flex-row is-full sm:is-auto'>
            <div className='flex gap-2'>
              {/* <Button
                color='secondary'
                variant='outlined'
                startIcon={<i className='ri-upload-2-line' />}
                className='is-full sm:is-auto'
              >
                Ekspor
              </Button>
               */}
              <Button
                onClick={handlePrint}
                color='secondary'
                variant='outlined'
                startIcon={<i className='ic-outline-local-printshop' />}
                className='is-full sm:is-auto'
              >
                Cetak
              </Button>
            </div>
            {srData?.status === ServiceRequisitionStatus.PROCESSED && canRemove && (
              <Button color='error' variant='contained' className='is-full sm:is-auto' onClick={handleCancelSr}>
                Batalkan Request
              </Button>
            )}
          </div>
        </div>
      </Grid>
      {srData?.items ? (
        <Grid item xs={12}>
          <ItemListCard />
        </Grid>
      ) : null}
      <Grid item xs={12} md={6}>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <DocNumberCard
              docType='WO'
              warehouseDoc={rawWorkOrder}
              note={`Segment No. ${srData?.workOrderSegment?.number}`}
            />
          </Grid>
          <Grid item xs={12}>
            <AdditionalInfoCard />
          </Grid>
          <Grid item xs={12}>
            <SegmentInfoCard />
          </Grid>
          <Grid item xs={12}>
            <UnitCard />
          </Grid>
        </Grid>
      </Grid>
      <Grid item xs={12} md={6}>
        <Grid container spacing={4}>
          {srData?.approvals ? (
            <Grid item xs={12}>
              <ApprovalDetailCard
                approvalList={
                  (srData?.approvals?.map(approval => ({
                    id: approval.id,
                    level: approval.level,
                    userId: approval.userId,
                    note: approval.note,
                    status: approval.status,
                    waitingAt: approval.waitingAt,
                    isRead: approval.isRead,
                    readAt: approval.readAt,
                    respondedAt: approval.respondedAt,
                    rejectionNote: approval.rejectionNote,
                    responseTime: approval.responseTime,
                    user: approval.user
                      ? {
                          id: approval.user.id,
                          fullName: approval.user.fullName,
                          title: approval.user.title,
                          avatar: '',
                          department: undefined,
                          country: '',
                          contact: '',
                          position: '',
                          username: '',
                          role: '',
                          status: '',
                          email: ''
                        }
                      : undefined
                  })) as unknown as ApproverType[]) ?? []
                }
                handleUpdateApprover={handleUpdateApprover}
                updateApproverLoading={updateApproverLoading}
              />
            </Grid>
          ) : null}
          <Grid item xs={12}>
            <RequestDetailCard vendorData={vendorData} />
          </Grid>
          <Grid item xs={12}>
            <ActivityLogCard logList={logList} />
          </Grid>
          <Grid item xs={12}>
            <OwnerCard user={srData?.createdByUser} />
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  )
}

export default SrDetailPage
