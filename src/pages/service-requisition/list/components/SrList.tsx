import { useEffect, useState } from 'react'
import { Typography } from '@mui/material'
import Card from '@mui/material/Card'
import Button from '@mui/material/Button'

import {
  getCoreRowModel,
  useReactTable,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFacetedMinMaxValues,
  getPaginationRowModel,
  getSortedRowModel
} from '@tanstack/react-table'

import DebouncedInput from '@/components/DebounceInput'
import Table from '@/components/table'
import { tableColumns } from '../config/table'
import { useRouter } from '@/routes/hooks'
import { useSr } from '../../context/SrContext'
import { srPriorityOptions, srStatusOptions } from '../config/utils'
import FilterGroupDialog, { FilterGroupConfig, FilterValues } from '@/components/layout/shared/filter/FilterGroup'
import { ServiceRequisitionStatus } from '@/types/serviceRequisitionsTypes'
import { useAuth } from '@/contexts/AuthContext'

const SrList = () => {
  const router = useRouter()
  const { ownSiteList, departmentList } = useAuth()
  const {
    srListResponse: { items: srList, totalItems, totalPages, limit: limitItems, page: pageItems },
    srsParams,
    setSrsParams,
    setSelectedSrId,
    setPartialSrsParams
  } = useSr()

  const { page, search, status, startDate, endDate, siteIds, departmentId, priority, limit } = srsParams

  const [filterGroupConfig, setFilterGroupConfig] = useState<FilterGroupConfig>({})

  const table = useReactTable({
    data: srList,
    columns: tableColumns({
      showDetail: id => {
        setSelectedSrId(id)
        router.push(`/service-request/list/${id}`)
      }
    }),
    initialState: {
      pagination: {
        pageSize: limit ?? 10,
        pageIndex: page - 1
      }
    },
    state: {
      pagination: {
        pageSize: limitItems,
        pageIndex: pageItems - 1
      }
    },
    manualPagination: true,
    rowCount: totalItems,
    pageCount: totalPages,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getFacetedMinMaxValues: getFacetedMinMaxValues()
  })

  const onFilterChanged = ({ date, status, priority, site, department }: FilterValues) => {
    setSrsParams(prev => {
      return {
        ...prev,
        page: 1,
        startDate: date[0],
        endDate: date[1],
        status: status.length > 0 ? (status[0] as ServiceRequisitionStatus) : undefined,
        priority: priority.length > 0 ? priority[0] : undefined,
        siteIds: site.length > 0 ? site[0] : undefined,
        departmentId: department.length > 0 ? department[0] : undefined
      }
    })
  }

  useEffect(() => {
    setFilterGroupConfig({
      date: {
        options: [],
        values: [startDate, endDate]
      },
      status: {
        options: srStatusOptions,
        values: status ? [status] : []
      },
      priority: {
        options: srPriorityOptions,
        values: priority ? [priority] : []
      },
      site: {
        options: ownSiteList.map(site => {
          return { value: site.id, label: site.name }
        }),
        values: siteIds ? [siteIds] : []
      },
      department: {
        options: departmentList?.map(department => ({ value: department.id, label: department.name })) ?? [],
        values: departmentId ? [departmentId] : []
      }
    })
  }, [srsParams])

  return (
    <Card>
      <div className='flex justify-between gap-4 p-5 flex-col items-start sm:flex-row sm:items-center'>
        <div className='flex gap-4 items-center flex-col sm:flex-row is-full sm:is-auto'>
          <DebouncedInput
            value={search}
            onChange={value => setSrsParams(prev => ({ ...prev, page: 1, search: value as string }))}
            placeholder='Cari Service Request'
            className='is-full sm:w-[240px]'
          />
          <FilterGroupDialog config={filterGroupConfig} onFilterApplied={onFilterChanged} />
        </div>
        <div className='flex items-center gap-x-4 max-sm:gap-y-4 is-full flex-col sm:is-auto sm:flex-row'>
          {/* <Button
            color='secondary'
            variant='outlined'
            startIcon={<i className='ri-upload-2-line' />}
            className='is-full sm:is-auto'
          >
            Ekspor
          </Button> */}
        </div>
      </div>
      <Table
        table={table}
        emptyLabel={
          <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
            <Typography>Belum ada Service Request</Typography>
            <Typography className='text-sm text-gray-400'>
              Semua Service Request yang telah dibuat akan ditampilkan di sini
            </Typography>
          </td>
        }
        onRowsPerPageChange={pageSize => {
          if (pageSize > totalItems) {
            setSrsParams(prev => ({ ...prev, limit: totalItems, page: 1 }))
          } else {
            setPartialSrsParams('limit', pageSize)

            const maxPage = Math.ceil(totalItems / pageSize)
            if (page > maxPage) {
              setSrsParams(prev => ({ ...prev, page: maxPage }))
            }
          }
        }}
        onPageChange={pageIndex => setPartialSrsParams('page', pageIndex)}
      />
    </Card>
  )
}

export default SrList
