import { ServiceRequisitionApprovalStatus, ServiceRequisitionStatus } from '@/types/serviceRequisitionsTypes'

export const srStatusOptions = [
  {
    value: ServiceRequisitionStatus.PROCESSED,
    label: 'Diproses',
    color: 'bg-yellow-400'
  },
  {
    value: ServiceRequisitionStatus.APPROVED,
    label: 'Disetujui',
    color: 'bg-green-500'
  },
  {
    value: ServiceRequisitionStatus.REJECTED,
    label: '<PERSON><PERSON><PERSON>',
    color: 'bg-red-500'
  },
  {
    value: ServiceRequisitionStatus.CANCELED,
    label: '<PERSON><PERSON>alkan',
    color: 'bg-red-500'
  },
  {
    value: ServiceRequisitionStatus.CLOSED,
    label: '<PERSON><PERSON><PERSON>',
    color: 'bg-blue-500'
  }
]

export const srUserStatusOptions = [
  {
    value: ServiceRequisitionApprovalStatus.WAITING,
    label: 'Menunggu'
  },
  {
    value: ServiceRequisitionApprovalStatus.APPROVED,
    label: 'Disetuju<PERSON>'
  },
  {
    value: ServiceRequisitionApprovalStatus.REJECTED,
    label: 'Ditolak'
  }
]

export const srPriorityOptions = [
  { value: '1', label: 'P1', color: 'bg-red-600' },
  { value: '2', label: 'P2', color: 'bg-orange-500' },
  { value: '3', label: 'P3', color: 'bg-yellow-400' },
  { value: '4', label: 'P4', color: 'bg-gray-300' }
]
