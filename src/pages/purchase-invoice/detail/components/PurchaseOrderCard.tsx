// MUI Imports
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'
import { Box, Table, TableHead, TableBody, TableRow, TableCell, IconButton, Grid } from '@mui/material'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { useRouter } from '@/routes/hooks'

// Utils
import { toCurrency } from '@/utils/helper'
import { calculateTax } from '@/utils/calculate-tax'

// Context
import { usePurchaseInvoice } from '../../context/PurchaseInvoiceContext'
import { PurchaseOrderTaxType } from '@/pages/purchase-order/config/enum'
import { PurchaseInvoice } from '@/types/purchaseInvoiceTypes'

const PurchaseOrderCard = ({ purchaseInvoiceData }: { purchaseInvoiceData?: PurchaseInvoice }) => {
  const router = useRouter()

  if (!purchaseInvoiceData || !purchaseInvoiceData.orders?.length) return null

  // If this is a down payment invoice, show the simple view
  if (purchaseInvoiceData.isDownPayment) {
    return (
      <Card>
        <CardContent className='flex flex-col gap-6'>
          <Typography variant='h5'>Purchase Order</Typography>
          {purchaseInvoiceData?.orders?.map((order, index) => (
            <div key={index} className='bg-gray-50 rounded-lg p-4'>
              <div className='flex flex-col gap-3'>
                <div className='flex justify-between items-start'>
                  <div className='flex flex-col gap-1'>
                    <Typography variant='body1' className='font-medium text-gray-700'>
                      No. PO: {order?.purchaseOrder?.number}
                    </Typography>
                    <Typography variant='body2' className='text-textSecondary'>
                      {formatDate(purchaseInvoiceData.createdAt, 'eeee dd/MM/yyyy, HH:mm', { locale: id })}
                    </Typography>
                  </div>
                </div>

                <div className='flex justify-between items-center'>
                  <Typography variant='body1' className='font-medium text-green-600'>
                    Total Purchase{' '}
                    {toCurrency(order?.purchaseOrder?.grandTotal, false, purchaseInvoiceData?.currency?.code)}
                  </Typography>
                </div>

                <div className='bg-green-100 rounded-lg p-3 mt-2'>
                  <div className='flex justify-between items-center'>
                    <Typography className='text-green-800'>Jumlah Uang Muka</Typography>
                    <Typography variant='body1' className='font-medium text-green-600'>
                      {toCurrency(order?.totalAmount ?? 0, false, purchaseInvoiceData?.currency?.code)}
                    </Typography>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </CardContent>
      </Card>
    )
  }

  // For regular invoices (not down payment), show the detailed view
  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <Typography variant='h5'>Purchase Order</Typography>

        {purchaseInvoiceData?.orders?.map((order, orderIndex) => (
          <Box
            key={orderIndex}
            sx={{ backgroundColor: 'rgba(76, 78, 100, 0.05)', borderRadius: '8px', padding: '16px' }}
          >
            {/* Purchase Order Header */}
            <div className='flex flex-col gap-3 mb-6'>
              <div className='flex justify-between items-start'>
                <div className='flex flex-col gap-1'>
                  <Typography variant='body1' className='font-medium text-gray-700'>
                    No. PO: {order?.purchaseOrder?.number}
                  </Typography>
                  <Typography variant='body2' className='text-textSecondary'>
                    {formatDate(purchaseInvoiceData.createdAt, 'eeee dd/MM/yyyy, HH:mm', { locale: id })}
                  </Typography>
                </div>
              </div>
            </div>

            {/* Items Table - Barang */}
            <Box sx={{ marginBottom: '24px' }}>
              <Typography
                variant='h6'
                sx={{
                  marginBottom: '16px',
                  fontWeight: 600,
                  fontSize: '16px',
                  color: 'rgba(76, 78, 100, 0.87)'
                }}
              >
                Barang
              </Typography>

              <Box sx={{ border: '1px solid rgba(76, 78, 100, 0.12)', borderRadius: '8px', overflow: 'hidden' }}>
                <Table>
                  <TableHead sx={{ backgroundColor: 'rgba(76, 78, 100, 0.04)' }}>
                    <TableRow>
                      <TableCell
                        sx={{
                          padding: '12px 16px',
                          fontWeight: 500,
                          fontSize: '12px',
                          textTransform: 'uppercase',
                          color: 'rgba(76, 78, 100, 0.87)'
                        }}
                      >
                        Kode Barang
                      </TableCell>
                      <TableCell
                        sx={{
                          padding: '12px 16px',
                          fontWeight: 500,
                          fontSize: '12px',
                          textTransform: 'uppercase',
                          color: 'rgba(76, 78, 100, 0.87)'
                        }}
                      >
                        Nama Item
                      </TableCell>
                      <TableCell
                        sx={{
                          padding: '12px 16px',
                          fontWeight: 500,
                          fontSize: '12px',
                          textTransform: 'uppercase',
                          color: 'rgba(76, 78, 100, 0.87)'
                        }}
                      >
                        Harga Satuan
                      </TableCell>
                      <TableCell
                        sx={{
                          padding: '12px 16px',
                          fontWeight: 500,
                          fontSize: '12px',
                          textTransform: 'uppercase',
                          color: 'rgba(76, 78, 100, 0.87)'
                        }}
                      >
                        Qty
                      </TableCell>
                      <TableCell
                        sx={{
                          padding: '12px 16px',
                          fontWeight: 500,
                          fontSize: '12px',
                          textTransform: 'uppercase',
                          color: 'rgba(76, 78, 100, 0.87)'
                        }}
                      >
                        Pajak
                      </TableCell>
                      <TableCell
                        sx={{
                          padding: '12px 16px',
                          fontWeight: 500,
                          fontSize: '12px',
                          textTransform: 'uppercase',
                          color: 'rgba(76, 78, 100, 0.87)'
                        }}
                      >
                        Total
                      </TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody className='bg-white'>
                    {!order.items || order.items.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={7} sx={{ textAlign: 'center', padding: '20px' }}>
                          <Typography color='textSecondary'>No items found</Typography>
                        </TableCell>
                      </TableRow>
                    ) : (
                      order.items.map((item, itemIndex) => {
                        const total =
                          (item.pricePerUnit || 0) * (item.quantity || 0) +
                          (item?.taxType && item.taxType === PurchaseOrderTaxType.EXCLUDE_TAX
                            ? calculateTax(
                                (item.pricePerUnit || 0) * (item.quantity || 0),
                                item.taxType,
                                item.taxPercentage
                              )
                            : 0)
                        const taxAmount = item.taxType
                          ? calculateTax(
                              (item.pricePerUnit || 0) * (item.quantity || 0),
                              item.taxType,
                              item.taxPercentage
                            )
                          : 0

                        return (
                          <TableRow key={itemIndex} sx={{ borderBottom: '1px solid rgba(76, 78, 100, 0.12)' }}>
                            <TableCell
                              sx={{ padding: '15px 16px', fontSize: '14px', color: 'rgba(76, 78, 100, 0.87)' }}
                            >
                              {item.item?.number || 'N/A'}
                            </TableCell>
                            <TableCell
                              sx={{ padding: '15px 16px', fontSize: '14px', color: 'rgba(76, 78, 100, 0.87)' }}
                            >
                              {item.item?.name || 'N/A'}
                            </TableCell>
                            <TableCell
                              sx={{ padding: '15px 16px', fontSize: '14px', color: 'rgba(76, 78, 100, 0.87)' }}
                            >
                              {toCurrency(item.pricePerUnit || 0, false, purchaseInvoiceData?.currency?.code)}
                            </TableCell>
                            <TableCell
                              sx={{ padding: '15px 16px', fontSize: '14px', color: 'rgba(76, 78, 100, 0.87)' }}
                            >
                              {item.quantity || 0} {item.quantityUnit ?? ''}
                            </TableCell>
                            <TableCell
                              sx={{
                                padding: '15px 16px',
                                fontSize: '14px',
                                color:
                                  item.taxType === PurchaseOrderTaxType.EXCLUDE_TAX
                                    ? 'rgba(76, 78, 100, 0.87)'
                                    : 'rgb(***********)'
                              }}
                            >
                              <div className='flex flex-col'>
                                {toCurrency(taxAmount, false, purchaseInvoiceData?.currency?.code)}
                                {item.taxType === PurchaseOrderTaxType.INCLUDE_TAX && (
                                  <Typography className='text-xxs' variant='caption'>
                                    *Harga Item Termasuk Pajak
                                  </Typography>
                                )}
                              </div>
                            </TableCell>
                            <TableCell
                              sx={{ padding: '15px 16px', fontSize: '14px', color: 'rgba(76, 78, 100, 0.87)' }}
                            >
                              {toCurrency(total, false, purchaseInvoiceData?.currency?.code)}
                            </TableCell>
                          </TableRow>
                        )
                      })
                    )}
                  </TableBody>
                </Table>
              </Box>
            </Box>

            {/* Down Payments Table - Uang Muka */}
            <Box sx={{ marginBottom: '24px' }}>
              <Typography
                variant='h6'
                sx={{
                  marginBottom: '16px',
                  fontWeight: 600,
                  fontSize: '16px',
                  color: 'rgba(76, 78, 100, 0.87)'
                }}
              >
                Uang Muka
              </Typography>

              <Box sx={{ border: '1px solid rgba(76, 78, 100, 0.12)', borderRadius: '8px', overflow: 'hidden' }}>
                <Table>
                  <TableHead sx={{ backgroundColor: 'rgba(76, 78, 100, 0.04)' }}>
                    <TableRow>
                      <TableCell
                        sx={{
                          padding: '12px 16px',
                          fontWeight: 500,
                          fontSize: '12px',
                          textTransform: 'uppercase',
                          color: 'rgba(76, 78, 100, 0.87)'
                        }}
                      >
                        No. Faktur
                      </TableCell>
                      <TableCell
                        sx={{
                          padding: '12px 16px',
                          fontWeight: 500,
                          fontSize: '12px',
                          textTransform: 'uppercase',
                          color: 'rgba(76, 78, 100, 0.87)'
                        }}
                      >
                        Tanggal Faktur
                      </TableCell>
                      <TableCell
                        sx={{
                          padding: '12px 16px',
                          fontWeight: 500,
                          fontSize: '12px',
                          textTransform: 'uppercase',
                          color: 'rgba(76, 78, 100, 0.87)'
                        }}
                      >
                        Jumlah Uang Muka
                      </TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody className='bg-white'>
                    {order?.downPayments.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={4} sx={{ textAlign: 'center', padding: '20px' }}>
                          <Typography color='textSecondary'>Tidak ada uang muka</Typography>
                        </TableCell>
                      </TableRow>
                    ) : (
                      order?.downPayments.map((invoice, index) => (
                        <TableRow key={index} sx={{ borderBottom: '1px solid rgba(76, 78, 100, 0.12)' }}>
                          <TableCell sx={{ padding: '15px 16px', fontSize: '14px', color: 'rgba(76, 78, 100, 0.87)' }}>
                            <Typography
                              color='primary'
                              sx={{ cursor: 'pointer' }}
                              onClick={() => router.push(`/purchase-invoice/list/${invoice.purchaseInvoice?.id}`)}
                            >
                              {invoice.purchaseInvoice?.number}
                            </Typography>
                          </TableCell>
                          <TableCell sx={{ padding: '15px 16px', fontSize: '14px', color: 'rgba(76, 78, 100, 0.87)' }}>
                            {invoice.purchaseInvoice?.invoiceDate
                              ? formatDate(invoice.purchaseInvoice.invoiceDate, 'dd/MM/yyyy', { locale: id })
                              : 'N/A'}
                          </TableCell>
                          <TableCell sx={{ padding: '15px 16px', fontSize: '14px', color: 'rgba(76, 78, 100, 0.87)' }}>
                            {toCurrency(invoice.totalAmount || 0, false, purchaseInvoiceData?.currency?.code)}
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </Box>
            </Box>

            {/* Summary Section */}
            <Grid container spacing={2}>
              {/* Sub Total */}
              <Grid item xs={12} md={4}>
                <Box
                  sx={{
                    backgroundColor: 'rgba(76, 78, 100, 0.05)',
                    borderRadius: '8px',
                    padding: '8px 16px',
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '4px'
                  }}
                >
                  <Typography
                    sx={{
                      fontWeight: 400,
                      fontSize: '12px',
                      lineHeight: '1em',
                      letterSpacing: '1.25%',
                      color: 'rgba(76, 78, 100, 0.6)'
                    }}
                  >
                    Sub Total
                  </Typography>
                  <Typography
                    sx={{
                      fontWeight: 600,
                      fontSize: '16px',
                      lineHeight: '1.5em',
                      letterSpacing: '0.94%',
                      color: 'rgba(76, 78, 100, 0.87)'
                    }}
                  >
                    {toCurrency(order?.subTotalAmount || 0, false, purchaseInvoiceData?.currency?.code)}
                  </Typography>
                </Box>
              </Grid>

              {/* Total Uang Muka */}
              <Grid item xs={12} md={4}>
                <Box
                  sx={{
                    backgroundColor: 'rgba(76, 78, 100, 0.05)',
                    borderRadius: '8px',
                    padding: '8px 16px',
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '4px'
                  }}
                >
                  <Typography
                    sx={{
                      fontWeight: 400,
                      fontSize: '12px',
                      lineHeight: '1em',
                      letterSpacing: '1.25%',
                      color: 'rgba(76, 78, 100, 0.6)'
                    }}
                  >
                    Total Uang Muka
                  </Typography>
                  <Typography
                    sx={{
                      fontWeight: 600,
                      fontSize: '16px',
                      lineHeight: '1.5em',
                      letterSpacing: '0.94%',
                      color: 'rgba(76, 78, 100, 0.87)'
                    }}
                  >
                    {toCurrency(
                      order?.downPayments?.reduce((total, invoice) => total + (invoice.totalAmount || 0), 0),
                      false,
                      purchaseInvoiceData?.currency?.code
                    )}
                  </Typography>
                </Box>
              </Grid>

              {/* Total Bayar */}
              <Grid item xs={12} md={4}>
                <Box
                  sx={{
                    backgroundColor: '#DBF7E8',
                    borderRadius: '8px',
                    padding: '8px 16px',
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '4px'
                  }}
                >
                  <Typography
                    sx={{
                      fontWeight: 400,
                      fontSize: '12px',
                      lineHeight: '1em',
                      letterSpacing: '1.25%',
                      color: 'rgba(76, 78, 100, 0.6)'
                    }}
                  >
                    Total Bayar PO
                  </Typography>
                  <Typography
                    sx={{
                      fontWeight: 600,
                      fontSize: '16px',
                      lineHeight: '1.5em',
                      letterSpacing: '0.94%',
                      color: '#4BD88B'
                    }}
                  >
                    {toCurrency(
                      (order.subTotalAmount || 0) -
                        order?.downPayments.reduce((total, invoice) => total + (invoice.totalAmount || 0), 0),
                      false,
                      purchaseInvoiceData?.currency?.code
                    )}
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </Box>
        ))}
      </CardContent>
    </Card>
  )
}

export default PurchaseOrderCard
