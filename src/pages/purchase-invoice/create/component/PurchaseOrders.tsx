import { <PERSON><PERSON>, <PERSON>, CardContent, FormHelperText, Typography } from '@mui/material'
import { useFormContext, useWatch, useFieldArray } from 'react-hook-form'
import { useState, useEffect } from 'react'

import { PurchaseInvoiceDtoType } from '../config/schema'
import AddPurchaseOrderDialog from '@/components/dialogs/add-po-dialog'
import { PoType } from '@/pages/purchase-order/config/types'
import { ImType } from '@/types/mgTypes'
import MgQueryMethods from '@/api/services/mg/query'
import PoQueryMethods from '@/api/services/po/query'
import SelectedPOsDisplay from './SelectedPOsDisplay'
import SelectedIMsDisplay from './SelectedIMsDisplay'

const PurchaseOrders = () => {
  const {
    control,
    setValue,
    formState: { errors },
    getValues
  } = useFormContext<PurchaseInvoiceDtoType>()
  const [dialogOpen, setDialogOpen] = useState(false)
  const [selectedPOs, setSelectedPOs] = useState<PoType[]>([])
  const [selectedIMs, setSelectedIMs] = useState<ImType[]>([])

  // Use useFieldArray to manage the orders array in the form
  const orderArray = useFieldArray({
    control,
    name: 'orders'
  })
  const { fields: orderFields, append, remove } = orderArray

  const otherExpensesArray = useFieldArray({
    control,
    name: 'otherExpenses'
  })

  // Watch vendorId and isDownPayment to pass to dialog
  const vendorId = useWatch({
    control,
    name: 'vendorId'
  })

  const isGeneralPurchase = useWatch({
    control,
    name: 'isGeneralPurchase'
  })

  const isDownPayment = useWatch({
    control,
    name: 'isDownPayment',
    defaultValue: false
  })

  // Sync selectedPOs and selectedIMs with form orders field
  useEffect(() => {
    // When rehydrating from draft, orders already exist; do not overwrite
    if (orderFields.length > 0) return
    // Map selectedPOs to orders format for down payment
    if (isDownPayment && selectedPOs.length > 0) {
      const newOrders = selectedPOs.map(po => ({
        purchaseOrderId: po.id,
        incomingMaterialId: null,
        items: null,
        downPaymentIds: null
      }))

      // Replace all orders with new ones from selectedPOs
      // Remove existing orders first
      for (let i = orderFields.length - 1; i >= 0; i--) {
        remove(i)
      }
      // Add new orders
      newOrders.forEach(order => append(order))
    }

    // Map selectedIMs to orders format for regular invoice
    if (!isDownPayment && selectedIMs.length > 0) {
      const newOrders = selectedIMs.map(im => {
        if (im.invoiceDate) {
          setValue('invoiceDate', im.invoiceDate)
        }
        return {
          purchaseOrderId: im.purchaseOrder?.id || '',
          incomingMaterialId: im.id,
          items:
            im.items?.map(item => {
              const itemPrice =
                (item.purchaseOrderItem?.pricePerUnit ?? 0) -
                item.purchaseOrderItem?.subtotalDiscount / item.purchaseOrderItem?.quantity
              return {
                incomingMaterialItemId: item.id,
                pricePerUnit: itemPrice,
                quantity: item.quantity,
                taxType: item.purchaseOrderItem?.taxType,
                taxId: item.purchaseOrderItem?.taxId,
                taxPercentage: item.purchaseOrderItem?.taxPercentage
              }
            }) || null,
          downPaymentIds: null
        }
      })

      // Replace all orders with new ones from selectedIMs
      // Remove existing orders first
      for (let i = orderFields.length - 1; i >= 0; i--) {
        remove(i)
      }
      // Add new orders
      newOrders.forEach(order => append(order))
    }
  }, [selectedPOs, selectedIMs, isDownPayment, append, remove, orderFields.length])

  const handleAddPO = () => {
    if (!vendorId && !isGeneralPurchase) {
      // Could show a toast or alert here
      return
    }
    setDialogOpen(true)
  }

  const handleDocumentSubmit = async (poList?: PoType[], imList?: ImType[]) => {
    if (poList && poList.length > 0) {
      setValue('siteId', poList[0].forSiteId || poList[0].siteId)
      setValue('departmentId', poList[0].departmentId)
      setValue('exchangeRate', poList[0].exchangeRate)
      setValue('currencyId', poList[0].currencyId)
      setValue('projectLabelId', poList[0].projectLabelId)
      setSelectedPOs(prev => {
        // Avoid duplicates
        const existingIds = prev.map(po => po.id)
        const newPOs = poList.filter(po => !existingIds.includes(po.id))
        return [...prev, ...newPOs]
      })
    }

    if (imList && imList.length > 0) {
      try {
        // Fetch complete IM details for each IM in the list
        const enrichedIMs = await Promise.all(
          imList.map(async im => {
            try {
              // Call MgQueryMethods.getIm to get complete IM details
              const fullImData = await MgQueryMethods.getIm(im.id)

              // Enrich the purchaseOrder object if it exists
              if (im.purchaseOrder) {
                try {
                  const fullPoData = await PoQueryMethods.getPo(im.purchaseOrder.id)

                  return {
                    ...im,
                    ...fullImData,
                    purchaseOrder: {
                      ...im.purchaseOrder,
                      ...fullPoData
                    }
                  }
                } catch (poError) {
                  console.error(`Failed to fetch PO details for ${im.purchaseOrder.id}:`, poError)
                  // Return with enriched IM but original PO on PO fetch failure
                  return {
                    ...im,
                    ...fullImData
                  }
                }
              }

              // Return with enriched IM data if no purchase order
              return {
                ...im,
                ...fullImData
              }
            } catch (error) {
              console.error(`Failed to fetch IM details for ${im.id}:`, error)
              // Return original IM data if fetch fails
              return im
            }
          })
        )

        // Set form values from the first enriched IM
        setValue('siteId', enrichedIMs[0].purchaseOrder?.forSiteId || enrichedIMs[0].purchaseOrder?.siteId)
        setValue('departmentId', enrichedIMs[0].departmentId)
        setValue('exchangeRate', enrichedIMs[0].purchaseOrder?.exchangeRate)
        setValue('currencyId', enrichedIMs[0].purchaseOrder?.currency?.id)
        setValue('projectLabelId', enrichedIMs[0].purchaseOrder?.projectLabelId)
        setValue('paymentTerms', enrichedIMs[0].purchaseOrder?.paymentTerms)
        setValue('paymentDueDays', enrichedIMs[0].purchaseOrder?.paymentDueDays)

        // Update selectedIMs with enriched data
        setSelectedIMs(prev => {
          // Avoid duplicates
          const existingIds = prev.map(im => im.id)
          const newIMs = enrichedIMs.filter(im => !existingIds.includes(im.id))
          return [...prev, ...newIMs]
        })

        // Add shipping cost from PO as an other expense
        enrichedIMs.forEach(im => {
          if (im.purchaseOrder && im.purchaseOrder.shippingCost && im.purchaseOrder.shippingCost > 0) {
            otherExpensesArray.append({
              accountId: '',
              amount: im.purchaseOrder.shippingCost,
              note: 'Ongkos Kirim'
            })
          }
        })
      } catch (error) {
        console.error('Error fetching IM details:', error)
        // Fallback to original behavior if there's an error
        setValue('siteId', imList[0].purchaseOrder?.forSiteId || imList[0].purchaseOrder?.siteId)
        setValue('departmentId', imList[0].departmentId)
        setSelectedIMs(prev => {
          const existingIds = prev.map(im => im.id)
          const newIMs = imList.filter(im => !existingIds.includes(im.id))
          return [...prev, ...newIMs]
        })
      }
    }
  }

  const handleRemovePO = (poId: string) => {
    setSelectedPOs(prev => prev.filter(po => po.id !== poId))
    // Also remove from form orders
    const orderIndex = orderFields.findIndex(field => field.purchaseOrderId === poId)
    if (orderIndex !== -1) {
      remove(orderIndex)
    }
  }

  const handleRemoveIM = (imId: string) => {
    setSelectedIMs(prev => prev.filter(im => im.id !== imId))
    // Also remove from form orders
    const orderIndex = orderFields.findIndex(field => field.incomingMaterialId === imId)
    if (orderIndex !== -1) {
      remove(orderIndex)
    }
  }

  // Rehydrate selected documents from draft orders
  useEffect(() => {
    if (orderFields.length === 0) return

    const populateFromOrders = async () => {
      try {
        if (isDownPayment) {
          const poIds = Array.from(
            new Set(orderFields.map(o => o.purchaseOrderId).filter(id => typeof id === 'string' && id.length > 0))
          ) as string[]

          // Avoid refetch if already populated
          const already = new Set(selectedPOs.map(po => po.id))
          const missing = poIds.filter(id => !already.has(id))
          if (missing.length === 0 && selectedPOs.length > 0) return

          const results = await Promise.all(missing.map(id => PoQueryMethods.getPo(id)))
          // Keep existing + new unique
          const merged = [...selectedPOs, ...results].reduce<PoType[]>((acc, curr) => {
            if (!acc.find(p => p.id === curr.id)) acc.push(curr)
            return acc
          }, [])
          setSelectedPOs(merged)
        } else {
          const imIds = Array.from(
            new Set(orderFields.map(o => o.incomingMaterialId).filter(id => typeof id === 'string' && id.length > 0))
          ) as string[]

          const already = new Set(selectedIMs.map(im => im.id))
          const missing = imIds.filter(id => !already.has(id))
          if (missing.length === 0 && selectedIMs.length > 0) return

          const results = await Promise.all(missing.map(id => MgQueryMethods.getIm(id)))
          const merged = [...selectedIMs, ...results].reduce<ImType[]>((acc, curr) => {
            if (!acc.find(p => p.id === curr.id)) acc.push(curr)
            return acc
          }, [])
          setSelectedIMs(merged)
        }
      } catch (e) {
        // ignore fetch errors during draft hydration
      }
    }

    populateFromOrders()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isDownPayment, orderFields.length])

  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-6'>
          <div className='flex justify-between items-start'>
            <Typography variant='h5'>{!isDownPayment ? 'Penerimaan Barang' : 'Purchase Order'}</Typography>
            <Button
              variant='contained'
              onClick={handleAddPO}
              disabled={(!vendorId && !isGeneralPurchase) || selectedPOs.length > 0}
            >
              {!isDownPayment ? 'Tambah Penerimaan Barang' : 'Tambah PO'}
            </Button>
          </div>

          {selectedPOs.length === 0 && selectedIMs.length === 0 ? (
            <div className='text-center py-8'>
              <Typography color='textSecondary'>
                Belum ada {isDownPayment ? 'Purchase Order' : 'Penerimaan Barang'} yang dipilih
              </Typography>
              <Typography variant='body2' color='textSecondary' className='mt-2'>
                {!vendorId
                  ? 'Pilih vendor terlebih dahulu untuk menambah dokumen'
                  : `Klik "Tambah" untuk memilih ${isDownPayment ? 'Purchase Order' : 'Penerimaan Barang'}`}
              </Typography>
            </div>
          ) : (
            <div className='flex flex-col gap-4'>
              {/* Display selected POs when isDownPayment is true */}
              {isDownPayment && (
                <SelectedPOsDisplay selectedPOs={selectedPOs} onRemovePO={handleRemovePO} fields={orderFields} />
              )}

              {/* Display selected IMs when isDownPayment is false */}
              {!isDownPayment && (
                <SelectedIMsDisplay
                  selectedIMs={selectedIMs}
                  vendorId={vendorId}
                  onRemoveIM={handleRemoveIM}
                  orderArray={orderArray}
                />
              )}
            </div>
          )}
          {errors?.orders && <FormHelperText error>{errors.orders.message}</FormHelperText>}
        </CardContent>
      </Card>
      {dialogOpen && (
        <AddPurchaseOrderDialog
          isGeneralPurchase={isGeneralPurchase}
          open={dialogOpen}
          setOpen={setDialogOpen}
          onSubmit={handleDocumentSubmit}
          vendorId={vendorId}
          isDownPayment={isDownPayment}
          selectedPOs={selectedPOs}
          selectedIMs={selectedIMs}
        />
      )}
    </>
  )
}

export default PurchaseOrders
