// MUI Imports
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'

// Utils
import { toCurrency } from '@/utils/helper'

// Context
import { usePurchaseInvoice } from '../../context/PurchaseInvoiceContext'

const PaymentSummaryCard = () => {
  const { purchaseInvoiceData } = usePurchaseInvoice()

  if (!purchaseInvoiceData) return null

  const { subTotalAmount = 0, otherAmount = 0, discountAmount = 0, totalAmount = 0 } = purchaseInvoiceData

  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <Typography variant='h5'>Ringkasan Pembayaran</Typography>

        <div className='grid grid-cols-3 gap-4'>
          {/* Sub Total Faktur */}
          <div className='bg-gray-50 rounded-lg p-4 text-center'>
            <Typography variant='body1' className='text-textSecondary mb-2'>
              Sub Total Faktur
            </Typography>
            <Typography variant='h6' className='font-bold'>
              {toCurrency(subTotalAmount, false, purchaseInvoiceData?.currency?.code)}
            </Typography>
          </div>

          {/* Diskon Faktur */}
          {/* <div className='bg-gray-50 rounded-lg p-4 text-center'>
            <Typography variant='body1' className='text-textSecondary mb-2'>
              Diskon Faktur
            </Typography>
            <Typography variant='h6' className='font-bold'>
              {toCurrency(discountAmount, false, purchaseInvoiceData?.currency?.code)}
            </Typography>
          </div> */}

          {/* Biaya Lain-Lain */}
          <div className='bg-gray-50 rounded-lg p-4 text-center'>
            <Typography variant='body1' className='text-textSecondary mb-2'>
              Biaya Lain-Lain
            </Typography>
            <Typography variant='h6' className='font-bold'>
              {toCurrency(otherAmount, false, purchaseInvoiceData?.currency?.code)}
            </Typography>
          </div>

          {/* Total Faktur */}
          <div className='bg-green-100 rounded-lg p-4 text-center'>
            <Typography variant='body1' className='text-green-800 mb-2'>
              Total Faktur
            </Typography>
            <Typography variant='h6' className='font-bold text-green-600'>
              {toCurrency(totalAmount, false, purchaseInvoiceData?.currency?.code)}
            </Typography>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default PaymentSummaryCard
