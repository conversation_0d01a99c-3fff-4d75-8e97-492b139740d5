// MUI Imports
import Grid from '@mui/material/Grid'

// Type Imports
import { Typography } from '@mui/material'

import PurchaseInvoiceList from './components/PurchaseInvoiceList'
import { PurchaseInvoiceContextProvider } from '../context/PurchaseInvoiceContext'

const PurchaseInvoiceListPage = () => {
  return (
    <PurchaseInvoiceContextProvider>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <div className='flex justify-between items-end'>
            <div className='flex flex-col'>
              <Typography variant='h4'>Hutang <PERSON>embelian</Typography>
              <Typography>Semua hutang pembelian akan ditampilkan di sini</Typography>
            </div>
          </div>
        </Grid>
        <Grid item xs={12}>
          <PurchaseInvoiceList />
        </Grid>
      </Grid>
    </PurchaseInvoiceContextProvider>
  )
}

export default PurchaseInvoiceListPage
