import { useEffect, useState } from 'react'
import { Typography } from '@mui/material'
import Card from '@mui/material/Card'

import {
  getCoreRowModel,
  useReactTable,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFacetedMinMaxValues,
  getPaginationRowModel,
  getSortedRowModel
} from '@tanstack/react-table'

import DebouncedInput from '@/components/DebounceInput'
import Table from '@/components/table'
import { tableColumns } from '../config/table'
import { useRouter } from '@/routes/hooks'
import { usePurchaseInvoice } from '../../context/PurchaseInvoiceContext'
import { useAuth } from '@/contexts/AuthContext'
import FilterGroupDialog, { FilterGroupConfig, FilterValues } from '@/components/layout/shared/filter/FilterGroup'

const PurchaseInvoiceList = () => {
  const router = useRouter()
  const {
    purchaseInvoiceListResponse: {
      items: purchaseInvoiceList,
      totalItems,
      totalPages,
      limit: limitItems,
      page: pageItems
    },
    purchaseInvoiceParams,
    setPurchaseInvoiceParams,
    setSelectedPurchaseInvoice,
    setPartialPurchaseInvoiceParams
  } = usePurchaseInvoice()

  const { userProfile, departmentList, ownSiteList } = useAuth()

  const { page, search, startDate, endDate } = purchaseInvoiceParams

  const [filterGroupConfig, setFilterGroupConfig] = useState<FilterGroupConfig>({})

  const table = useReactTable({
    data: purchaseInvoiceList.map(invoice => ({ ...invoice, isRead: invoice.approvals?.[0]?.isRead })),
    columns: tableColumns(
      {
        showDetail: id => {
          setSelectedPurchaseInvoice({ purchaseInvoiceId: id })
          router.push(`/cash-bank/purchase-invoice/${id}`)
        }
      },
      userProfile?.id
    ) as any,
    initialState: {
      pagination: {
        pageSize: purchaseInvoiceParams.limit ?? 10,
        pageIndex: page - 1
      }
    },
    state: {
      pagination: {
        pageSize: limitItems,
        pageIndex: pageItems - 1
      }
    },
    manualPagination: true,
    rowCount: totalItems,
    pageCount: totalPages,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getFacetedMinMaxValues: getFacetedMinMaxValues()
  })

  const onFilterChanged = ({ date }: FilterValues) => {
    setPartialPurchaseInvoiceParams('page', 1)
    if (date[0]) setPartialPurchaseInvoiceParams('startDate', date[0])
    if (date[1]) setPartialPurchaseInvoiceParams('endDate', date[1])
    // Add other filters as needed
  }

  useEffect(() => {
    setFilterGroupConfig({
      date: {
        options: [],
        values: [startDate, endDate]
      },
      site: {
        options: ownSiteList.map(site => {
          return { value: site.id, label: site.name }
        }),
        values: []
      },
      department: {
        options: departmentList?.map(department => ({ value: department.id, label: department.name })) ?? [],
        values: []
      }
    })
  }, [purchaseInvoiceParams, ownSiteList, departmentList, startDate, endDate])

  useEffect(() => {
    setPurchaseInvoiceParams({
      limit: 10,
      page: 1,
      search: ''
    })
  }, [])

  return (
    <Card>
      <div className='flex justify-between gap-4 p-5 flex-col items-start sm:flex-row sm:items-center'>
        <div className='flex gap-4 items-center flex-col sm:flex-row is-full sm:is-auto'>
          <DebouncedInput
            value={search}
            onChange={value => {
              setPartialPurchaseInvoiceParams('page', 1)
              setPartialPurchaseInvoiceParams('search', value as string)
            }}
            placeholder='Cari'
            className='is-full sm:is-auto'
          />
          <FilterGroupDialog config={filterGroupConfig} onFilterApplied={onFilterChanged} />
        </div>
        <div className='flex items-center gap-x-4 max-sm:gap-y-4 is-full flex-col sm:is-auto sm:flex-row'>
          {/* <Button
            color='secondary'
            variant='outlined'
            startIcon={<i className='ri-upload-2-line' />}
            className='is-full sm:is-auto'
          >
            Ekspor
          </Button> */}
        </div>
      </div>
      <Table
        table={table}
        emptyLabel={
          <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
            <Typography>Belum ada Faktur Pembelian</Typography>
            <Typography className='text-sm text-gray-400'>
              Semua Faktur Pembelian yang telah dibuat akan ditampilkan di sini
            </Typography>
          </td>
        }
        onRowsPerPageChange={pageSize => {
          if (pageSize > totalItems) {
            setPartialPurchaseInvoiceParams('limit', totalItems)
            setPartialPurchaseInvoiceParams('page', 1)
          } else {
            setPartialPurchaseInvoiceParams('limit', pageSize)

            const maxPage = Math.ceil(totalItems / pageSize)
            if (page > maxPage) {
              setPartialPurchaseInvoiceParams('page', maxPage)
            }
          }
        }}
        onPageChange={pageIndex => setPartialPurchaseInvoiceParams('page', pageIndex)}
      />
    </Card>
  )
}

export default PurchaseInvoiceList
