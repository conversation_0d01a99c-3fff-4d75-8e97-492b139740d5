import DebouncedInput from '@/components/DebounceInput'
import { Card, Button, Typography, TextField } from '@mui/material'
import { useReconsiliation } from '../../context/ReconsiliationContext'
import Table from '@/components/table'
import { tableColumns } from '../config/table'
import { useMemo } from 'react'
import {
  getCoreRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFacetedMinMaxValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import { useRouter } from '@/routes/hooks'
import AppReactDatepicker from '@/components/libs/styles/AppReactDatepicker'
import { formatISO, toDate } from 'date-fns'
import Permission from '@/core/components/Permission'

const ReconciliationList = () => {
  const router = useRouter()
  const { reconciliationParams, setReconciliationParams, setPartialReconciliationParams, reconciliationList } =
    useReconsiliation()

  const { search, page, limit, endDate } = reconciliationParams
  const { totalItems, totalPages, limit: limitItems, page: pageItems } = reconciliationList

  const tableOptions: any = useMemo(
    () => ({
      data: reconciliationList.items,
      columns: tableColumns({
        onDetail: row => {
          router.push(`/reconciliation/${row.id}`)
        }
      }),
      initialState: {
        pagination: {
          pageSize: limit ?? 10,
          pageIndex: page - 1
        }
      },
      state: {
        pagination: {
          pageSize: limitItems,
          pageIndex: pageItems - 1
        }
      },
      manualPagination: true,
      rowCount: totalItems,
      pageCount: totalPages,
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [reconciliationList, reconciliationParams]
  )

  const table = useReactTable(tableOptions)

  return (
    <Card>
      <div className='flex justify-between gap-4 p-5 flex-col items-start sm:flex-row sm:items-center'>
        <div className='flex gap-4 items-center flex-col sm:flex-row is-full sm:is-auto'>
          <DebouncedInput
            value={search}
            onChange={value => setPartialReconciliationParams('search', value)}
            placeholder='Cari'
            className='is-full sm:is-auto'
          />
          <div className='flex gap-2 items-center'>
            <Typography className='hidden md:block'>Filter: </Typography>
            <AppReactDatepicker
              selected={endDate ? toDate(endDate) : undefined}
              onChange={(date: Date) => setPartialReconciliationParams('endDate', formatISO(date))}
              dateFormat='eeee dd/MM/yyyy'
              customInput={
                <TextField
                  fullWidth
                  label='Tanggal'
                  size='small'
                  className='flex-1 is-full sm:is-auto'
                  InputProps={{
                    readOnly: true
                  }}
                />
              }
            />
          </div>
        </div>
        <div className='flex items-center gap-x-4 max-sm:gap-y-4 is-full flex-col sm:is-auto sm:flex-row'>
          {/* <Button
            color='secondary'
            variant='outlined'
            startIcon={<i className='ri-upload-2-line' />}
            className='is-full sm:is-auto'
          >
            Ekspor
          </Button> */}
          <Permission permission={['bank-reconciliation.create']}>
            <Button className='is-full sm:is-auto' variant='contained' onClick={() => router.push('create')}>
              Buat Rekonsiliasi
            </Button>
          </Permission>
        </div>
      </div>
      <Table
        table={table}
        emptyLabel={
          <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
            <Typography>Belum ada Rekonsiliasi</Typography>
            <Typography className='text-sm text-gray-400'>
              Semua Rekonsiliasi yang kamu buat akan ditampilkan di sini
            </Typography>
          </td>
        }
        onRowsPerPageChange={pageSize => {
          if (pageSize > totalItems) {
            setReconciliationParams(prev => ({ ...prev, limit: totalItems, page: 1 }))
          } else {
            setPartialReconciliationParams('limit', pageSize)

            const maxPage = Math.ceil(totalItems / pageSize)
            if (page > maxPage) {
              setReconciliationParams(prev => ({ ...prev, page: maxPage }))
            }
          }
        }}
        onPageChange={pageIndex => setPartialReconciliationParams('page', pageIndex)}
      />
    </Card>
  )
}

export default ReconciliationList
