import { createColumnHelper } from '@tanstack/react-table'
import { ReconciliationType } from '../../config/types'
import { IconButton, Typography } from '@mui/material'
import { formatThousandSeparator, thousandSeparator, toCurrency } from '@/utils/helper'
import { formatDate } from 'date-fns'

const columnHelper = createColumnHelper<ReconciliationType>()

type RowActionType = {
  onDetail: (row: ReconciliationType) => void
}

export const tableColumns = ({ onDetail }: RowActionType) => [
  columnHelper.accessor('number', {
    header: 'No Rekonsiliasi',
    cell: ({ row }) => (
      <Typography className='cursor-pointer' onClick={() => onDetail(row.original)} color='primary'>
        {row.original?.number}
      </Typography>
    )
  }),
  columnHelper.accessor('account.name', {
    header: '<PERSON><PERSON><PERSON>',
    cell: ({ row }) => (
      <Typography className='text-sm font-medium'>{`[${row.original?.account?.code}] ${row.original?.account?.name}`}</Typography>
    )
  }),
  columnHelper.accessor('date', {
    header: 'Tanggal Rekonsiliasi',
    cell: ({ row }) => (row.original?.date ? formatDate(row.original?.date, 'dd/MM/yyyy') : '-')
  }),
  columnHelper.accessor('accountBalance', {
    header: 'Total Saldo',
    cell: ({ row }) => (
      <Typography className='text-sm font-medium' color={row.original?.accountBalance < 0 ? 'error' : 'primary'}>
        {row.original?.accountBalance < 0
          ? `(${formatThousandSeparator(Math.abs(row.original.accountBalance))})`
          : formatThousandSeparator(row.original.accountBalance)}
      </Typography>
    )
  }),
  columnHelper.accessor('actualBalance', {
    header: 'Saldo Aktual',
    cell: ({ row }) => (
      <Typography className='text-sm font-medium' color={row.original?.actualBalance < 0 ? 'error' : 'primary'}>
        {row.original?.actualBalance < 0
          ? `(${formatThousandSeparator(Math.abs(row.original.actualBalance))})`
          : formatThousandSeparator(row.original.actualBalance)}
      </Typography>
    )
  }),
  columnHelper.accessor('balanceDiffence', {
    header: 'Selisih Saldo',
    cell: ({ row }) => (
      <Typography className='text-sm font-medium' color={row.original?.balanceDiffence < 0 ? 'error' : 'primary'}>
        {formatThousandSeparator(Math.abs(row.original.balanceDiffence) ?? 0)}
      </Typography>
    )
  }),
  columnHelper.display({
    id: 'actions',
    header: 'Aksi',
    cell: ({ row }) => (
      <IconButton onClick={() => onDetail(row.original)}>
        <i className='ri-eye-line' />
      </IconButton>
    )
  })
]
