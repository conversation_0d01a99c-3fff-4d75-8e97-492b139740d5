import { AccountType } from '@/types/accountTypes'
import { UserOutlineType } from '@/types/userTypes'

export enum JournalRefDocType {
  Payment = 'payment',
  CashReceipt = 'cash-receipt',
  SalesInvoice = 'sales-invoice',
  PurchaseInvoice = 'purchase-invoice'
}

export type CreateReconciliationPayload = {
  accountId: string
  checklists: ReconciliationChecklist[]
  accountBalance: number
  actualBalance: number
  date: string
  note: string
}

export type ReconciliationChecklist = {
  journalLineId: number
  note: string
}

export type ReconciliationType = {
  id: string
  number: string
  accountId: string
  accountBalance: number
  actualBalance: number
  balanceDiffence: number
  totalDebit: number
  totalCredit: number
  date: string
  note: string
  status: string
  companyId: string
  parentCompanyId: string
  createdAt: string
  updatedAt: string
  account: AccountType
  createdByUser: UserOutlineType
}
