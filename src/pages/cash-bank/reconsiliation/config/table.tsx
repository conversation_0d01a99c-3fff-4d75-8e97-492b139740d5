import { JournalLineType } from '@/types/accountTypes'
import { formatThousandSeparator, thousandSeparator, toCurrency } from '@/utils/helper'
import { Checkbox, Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'
import { formatDate } from 'date-fns'
import { JournalRefDocType } from './types'
import truncateString from '@/core/utils/truncate'

type RowActionType = {
  detail: (row: JournalLineType, type: JournalRefDocType) => void
  withChecklist?: boolean
}

const columnHelper = createColumnHelper<JournalLineType>()

const refDocTypeConfig = (type: JournalRefDocType) => {
  switch (type) {
    case JournalRefDocType.Payment:
      return 'Pembayaran'
    case JournalRefDocType.CashReceipt:
      return 'Penerimaan'
    default:
      return '-'
  }
}

export const journalLineColumns = ({ withChecklist = true, ...rowAction }: RowActionType) => [
  columnHelper.accessor('voucherNumber', {
    header: 'No Voucher',
    cell: ({ row }) => {
      const data = row.original
      return (
        <Typography
          onClick={() => rowAction.detail(data, data?.journal?.refDocType as JournalRefDocType)}
          className='cursor-pointer text-sm font-medium'
          color='primary'
        >
          {row.original?.journal?.voucherNumber ?? '-'}
        </Typography>
      )
    }
  }),
  columnHelper.accessor('description', {
    header: 'Deskripsi',
    cell: ({ row }) => truncateString(row.original?.description ?? '-', 20)
  }),
  columnHelper.accessor('debit', {
    header: 'Debit',
    cell: ({ getValue }) => {
      const value = getValue<number>()
      return (
        <div className='flex flex-col gap-1'>
          <span className={`text-sm font-medium ${value > 0 ? 'text-primary' : 'text-gray-700'}`}>
            {value < 0 ? `(${formatThousandSeparator(Math.abs(value))})` : formatThousandSeparator(value)}
          </span>
        </div>
      )
    }
  }),
  columnHelper.accessor('credit', {
    header: 'Kredit',
    cell: ({ getValue }) => {
      const value = getValue<number>()
      return (
        <div className='flex flex-col gap-1'>
          <span className={`text-sm font-medium ${value > 0 ? 'text-error' : 'text-gray-700'}`}>
            {value < 0 ? `(${formatThousandSeparator(Math.abs(value))})` : formatThousandSeparator(value)}
          </span>
        </div>
      )
    }
  }),
  ...(!!withChecklist
    ? [
        {
          id: 'select',
          header: 'Checklist',
          size: 20,
          cell: ({ row }) => (
            <Checkbox
              checked={row.getIsSelected()}
              onChange={row.getToggleSelectedHandler()}
              disabled={!row.getCanSelect()}
              indeterminate={row.getIsSomeSelected()}
            />
          ),
          enableSorting: false
        }
      ]
    : [])
]
