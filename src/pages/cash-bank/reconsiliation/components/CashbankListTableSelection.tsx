import React, { useEffect, useMemo } from 'react'
import { useReactTable, getCoreRowModel, RowSelectionState } from '@tanstack/react-table'
import { journalLineColumns } from '../config/table'
import { useReconsiliation } from '../context/ReconsiliationContext'
import { Controller, useFormContext } from 'react-hook-form'
import { CreateReconciliationPayload, JournalRefDocType } from '../config/types'
import { FormHelperText, Typography } from '@mui/material'
import { JournalLineType } from '@/types/accountTypes'
import { mergeArrays } from '@/utils/helper'
import { useRouter } from '@/routes/hooks'
import Table from '@/components/table'

const CashbankListTableSelection: React.FC = () => {
  const router = useRouter()
  const { journalLines } = useReconsiliation()

  const { control, reset, getValues } = useFormContext<CreateReconciliationPayload>()

  const [rowSelection, setRowSelection] = React.useState<RowSelectionState>({})

  const tableOptions: any = useMemo(
    () => ({
      data: journalLines?.items ?? [],
      columns: journalLineColumns({
        detail: (row, type) => {
          window.open(`/accounting/general-ledger/list/${row.journal.id}`, '_blank')
        }
      }),
      state: {
        rowSelection
      },
      onRowSelectionChange: setRowSelection,
      getCoreRowModel: getCoreRowModel(),
      enableRowSelection: true,
      enableMultiRowSelection: true
    }),
    [journalLines, rowSelection]
  )

  const table = useReactTable(tableOptions)

  const selectedRows = table.getSelectedRowModel().rows

  useEffect(() => {
    const selectedChecklists = selectedRows.map(row => row.original) as JournalLineType[]
    if (selectedChecklists.length > 0) {
      reset({
        ...getValues(),
        checklists: mergeArrays(
          getValues('checklists') ?? [],
          selectedChecklists.map(checklist => ({ journalLineId: checklist.id, note: checklist.description })),
          'journalLineId'
        )
      })
    }
  }, [selectedRows])

  return (
    <Controller
      control={control}
      name='checklists'
      rules={{ validate: value => (value?.length > 0 ? true : 'Wajib dipilih') }}
      render={({ fieldState: { error } }) => (
        <div className='w-full space-y-3'>
          <div className='rounded-[8px] shadow-md'>
            <Table
              headerColor='green'
              table={table}
              emptyLabel={
                <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
                  <Typography>Belum ada checklist</Typography>
                  <Typography className='text-sm text-gray-400'>
                    Semua checklist yang telah dibuat akan ditampilkan di sini
                  </Typography>
                </td>
              }
              disablePagination
            />
          </div>
          {!!error && <FormHelperText error>{error?.message}</FormHelperText>}
        </div>
        // <div className='w-full'>
        //   <div className='overflow-x-auto md:rounded-lg'>
        //     <table className='min-w-full divide-y divide-gray-300'>
        //       <tbody className='bg-white divide-y divide-gray-200'>
        //         {table.getRowModel().rows.map(row => (
        //           <tr key={row.id} className='transition-colors duration-150'>
        //             {row.getVisibleCells().map(cell => (
        //               <td
        //                 key={cell.id}
        //                 className={classNames(
        //                   'whitespace-nowrap',
        //                   cell.column.columnDef?.size ? `w-[${cell.column.columnDef?.size}px] p-2` : 'p-4'
        //                 )}
        //               >
        //                 {flexRender(cell.column.columnDef.cell, cell.getContext())}
        //               </td>
        //             ))}
        //           </tr>
        //         ))}
        //       </tbody>
        //     </table>
        //   </div>
        //   {!!error && <FormHelperText error>{error?.message}</FormHelperText>}
        // </div>
      )}
    />
  )
}

export default CashbankListTableSelection
