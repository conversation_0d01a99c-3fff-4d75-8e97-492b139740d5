import { formatThousandSeparator, thousandSeparator, toCurrency } from '@/utils/helper'
import { Autocomplete, Card, CardContent, CardHeader, debounce, TextField, Typography } from '@mui/material'
import { formatDate } from 'date-fns'
import { useState } from 'react'
import CashbankListTableSelection from './CashbankListTableSelection'
import { useReconsiliation } from '../context/ReconsiliationContext'
import DebouncedInput from '@/components/DebounceInput'

const ReconciliationCardResult = () => {
  const { selectedAccountCashbank, journalLineParams, setPartialJournalLineParams, handleSearch, loadingJournalLines } =
    useReconsiliation()
  const [itemSearchQuery, setItemSearchQuery] = useState('')
  const { search } = journalLineParams

  return (
    <Card>
      <CardHeader
        className='bg-[#DBF7E8]'
        title={<Typography variant='h5'>Jurnal Equalindo360</Typography>}
      ></CardHeader>
      <CardContent className='flex flex-col gap-4 mt-4'>
        <div className='p-4 flex justify-between items-end bg-[#4C4E640D]'>
          <div className='flex flex-col gap-2'>
            <Typography className='font-semibold'>Saldo Kas</Typography>
            <Typography>
              {!!selectedAccountCashbank ? `[${selectedAccountCashbank?.code}] ${selectedAccountCashbank?.name}` : '-'}
            </Typography>
          </div>
          <Typography variant='h5' color='primary'>
            {selectedAccountCashbank?.balance < 0
              ? `(${formatThousandSeparator(Math.abs(selectedAccountCashbank.balance))})`
              : formatThousandSeparator(selectedAccountCashbank?.balance ?? 0)}
          </Typography>
        </div>
        <DebouncedInput
          size='medium'
          value={search}
          disabled={loadingJournalLines}
          onChange={value => {
            setPartialJournalLineParams('search', value)
            handleSearch()
          }}
          placeholder='Cari no pencatatan, nominal transaksi, atau memo'
          className='is-full'
        />
        <div>
          <CashbankListTableSelection />
        </div>
      </CardContent>
    </Card>
  )
}

export default ReconciliationCardResult
