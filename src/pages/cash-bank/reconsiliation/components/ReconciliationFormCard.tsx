import AccountsQueryMethods, { ACCOUNT_LIST_QUERY_KEY } from '@/api/services/account/query'
import AppReactDatepicker from '@/components/libs/styles/AppReactDatepicker'
import { AccountParams, AccountType } from '@/types/accountTypes'
import { Autocomplete, Button, Card, CardContent, debounce, Grid, TextField, Typography } from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { useState } from 'react'
import { useReconsiliation } from '../context/ReconsiliationContext'
import { formatISO, toDate } from 'date-fns'
import LoadingButton from '@mui/lab/LoadingButton'
import { Controller, useFormContext } from 'react-hook-form'
import { CreateReconciliationPayload } from '../config/types'

const BASE_ACCOUNT_PARAMS: AccountParams = {
  limit: Number.MAX_SAFE_INTEGER,
  accountTypeIds: 'CASH_BANK',
  level: 1
}

const ReconciliationFormCard = () => {
  const {
    setPartialJournalLineParams,
    handleSearch,
    loadingJournalLines,
    selectedAccountCashbank,
    setSelectedAccountCashbank
  } = useReconsiliation()
  const [queries, setQueries] = useState<string>()

  const { control } = useFormContext<CreateReconciliationPayload>()
  const { data: inventoryAccounts, remove: removeAccounts } = useQuery({
    queryKey: [ACCOUNT_LIST_QUERY_KEY, queries],
    queryFn: () => AccountsQueryMethods.getAccountList({ ...BASE_ACCOUNT_PARAMS, search: queries })
  })

  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between'>
          <Typography variant='h5'>Akun Rekonsiliasi</Typography>
        </div>
        <Grid container alignItems={'end'} spacing={4}>
          <Grid item xs={12} md={6}>
            <Controller
              control={control}
              name='accountId'
              rules={{ required: true }}
              render={({ field, fieldState: { error } }) => (
                <Autocomplete
                  key={JSON.stringify(selectedAccountCashbank)}
                  value={selectedAccountCashbank}
                  onInputChange={debounce((e, newValue, reason) => {
                    if (reason === 'input') {
                      setQueries(newValue as string)
                    }
                  }, 700)}
                  options={inventoryAccounts?.items ?? []}
                  getOptionLabel={(option: AccountType) => `[${option.code}] ${option.name}`}
                  freeSolo={!queries}
                  noOptionsText='Akun tidak ditemukan'
                  onChange={(e, newValue: AccountType) => {
                    if (newValue) {
                      field.onChange(newValue.id)
                      setPartialJournalLineParams('accountId', newValue.id)
                      setSelectedAccountCashbank(newValue)
                      removeAccounts()
                    }
                  }}
                  renderInput={params => (
                    <TextField
                      {...params}
                      InputProps={{
                        ...params.InputProps,
                        onKeyDown: e => {
                          if (e.key === 'Enter') {
                            e.stopPropagation()
                          }
                        }
                      }}
                      placeholder='Cari akun perkiraan'
                      label='Akun Kas & Bank'
                      error={!!error}
                    />
                  )}
                />
              )}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <Controller
              control={control}
              name='date'
              rules={{ required: true }}
              render={({ field: { value, onChange }, fieldState: { error } }) => (
                <AppReactDatepicker
                  boxProps={{ className: 'is-full' }}
                  selected={value ? toDate(value) : undefined}
                  onChange={(date: Date) => onChange(formatISO(date))}
                  dateFormat='eeee dd/MM/yyyy'
                  customInput={
                    <TextField
                      fullWidth
                      label='Tanggal Rekonsiliasi'
                      placeholder='Pilih tanggal'
                      className='flex-1'
                      InputProps={{
                        readOnly: true
                      }}
                      error={!!error}
                    />
                  }
                />
              )}
            />
          </Grid>
          {/* <Grid item xs={12} md={6}>
            <AppReactDatepicker
              boxProps={{ className: 'is-full' }}
              // selected={docsList[index]?.expirationDate ? toDate(docsList[index]?.expirationDate) : undefined}
              // onChange={(date: Date) => onChange(formatISO(date), 'expirationDate')}
              dateFormat='eeee dd/MM/yyyy'
              customInput={
                <TextField
                  fullWidth
                  label='Tanggal Rekonsiliasi'
                  placeholder='Pilih tanggal'
                  className='flex-1'
                  InputProps={{
                    readOnly: true
                  }}
                />
              }
            />
          </Grid> */}
          {/* <Grid item xs={12} md={6}>
            <div className='flex flex-col gap-2'>
              <Typography className='font-semibold'>Tanggal Transaksi</Typography>
              <AppReactDatepicker
                boxProps={{ className: 'is-full' }}
                selected={journalLineParams?.startDate ? toDate(journalLineParams?.startDate) : undefined}
                onChange={(date: Date) => setPartialJournalLineParams('startDate', formatISO(date))}
                dateFormat='eeee dd/MM/yyyy'
                customInput={
                  <TextField
                    fullWidth
                    label='Tanggal Mulai'
                    placeholder='Pilih tanggal'
                    className='flex-1'
                    InputProps={{
                      readOnly: true
                    }}
                  />
                }
              />
            </div>
          </Grid>
          <Grid item xs={12} md={6}>
            <AppReactDatepicker
              boxProps={{ className: 'is-full' }}
              selected={journalLineParams?.endDate ? toDate(journalLineParams?.endDate) : undefined}
              onChange={(date: Date) => setPartialJournalLineParams('endDate', formatISO(date))}
              dateFormat='eeee dd/MM/yyyy'
              customInput={
                <TextField
                  fullWidth
                  label='Tanggal Selesai'
                  placeholder='Pilih tanggal'
                  className='flex-1'
                  InputProps={{
                    readOnly: true
                  }}
                />
              }
            />
          </Grid> */}
          <Grid item xs={12}>
            <LoadingButton loading={loadingJournalLines} onClick={handleSearch} className='is-full' variant='outlined'>
              Cari Transaksi
            </LoadingButton>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  )
}

export default ReconciliationFormCard
