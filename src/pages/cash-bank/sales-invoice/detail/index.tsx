import { useState } from 'react'
import { Link } from 'react-router-dom'

// MUI Imports
import { Breadcrumbs, Chip, Typography, Button, Grid } from '@mui/material'

// Third-party Imports
import { format } from 'date-fns'
import { id } from 'date-fns/locale'

// Components Imports
import DialogDetailJournal from '@/components/dialogs/detail-journal-dialog'
import ApprovalDetailCard from '@/pages/material-request/detail/components/ApprovalDetailCard'
import { statusChipValue } from '@/pages/purchase-invoice/detail/components/ApprovalsCard'
import Permission from '@/core/components/Permission'

// Hooks Imports
import { useAuth } from '@/contexts/AuthContext'
import { useRouter } from '@/routes/hooks'

// Context Imports
import { saveAs } from 'file-saver'
import { pdf } from '@react-pdf/renderer'
import InvoicePdfDocument from '@/pages/sales/invoice/detail/components/InvoicePdfDocument'
import SalesInvoiceDetailCard from '@/pages/sales/invoice/detail/components/SalesInvoiceDetailCard'
import DocumentCard from '@/pages/sales/invoice/detail/components/DocumentCard'
import ItemsCard from '@/pages/sales/invoice/detail/components/ItemsCard'
import OtherExpenseCard from '@/pages/sales/invoice/detail/components/OtherExpenseCard'
import PaymentSummaryCard from '@/pages/sales/invoice/detail/components/PaymentSummaryCard'
import { SalesInvoiceContextProvider, useSalesInvoice } from '@/pages/sales/invoice/context/SalesInvoiceContext'
import ActivityLogCard from '@/pages/sales/invoice/detail/components/ActivityLogCard'

const SalesInvoiceDetailPage = () => {
  const router = useRouter()
  const { salesInvoiceData, salesInvoiceLogs } = useSalesInvoice()
  const [selectedJournalId, setSelectedJournalId] = useState<string | null>(null)

  const handleExport = async () => {
    const blob = await pdf(<InvoicePdfDocument salesInvoice={salesInvoiceData} />).toBlob()
    const filename = salesInvoiceData?.number + '.pdf'
    saveAs(blob, filename)
  }

  const handlePrint = async () => {
    const blob = await pdf(
      <InvoicePdfDocument
        salesInvoice={salesInvoiceData}
        // qrCode={qr}
      />
    ).toBlob()
    const url = URL.createObjectURL(blob)
    const printWindow = window.open(url, '_blank')
    printWindow.onload = () => {
      printWindow.print()
      printWindow.onafterprint = () => {
        printWindow.close()
      }
    }
  }

  return (
    <>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <Breadcrumbs aria-label='breadcrumb'>
            <Link to='#' replace>
              <Typography color='var(--mui-palette-text-disabled)'>Kas & Bank</Typography>
            </Link>
            <Link to='/cash-bank/sales-invoice' replace>
              <Typography color='var(--mui-palette-text-disabled)'>Piutang Penjualan</Typography>
            </Link>
            <Typography>Detil Piutang Penjualan</Typography>
          </Breadcrumbs>
        </Grid>
        <Grid item xs={12}>
          <div className='flex justify-between items-end flex-col sm:flex-row max-sm:items-center gap-2'>
            <div className='flex flex-col'>
              <div className='flex items-center gap-2 flex-col sm:flex-row max-sm:text-center'>
                <Typography variant='h4'>No. Faktur: {salesInvoiceData?.number}</Typography>
                <Chip label='Belum Diterima' color='default' variant='tonal' size='small' />
              </div>
              <Typography className='max-sm:text-center max-sm:mt-2'>
                {format(salesInvoiceData?.createdAt ?? Date.now(), 'eeee dd/MM/yyyy', { locale: id })}
              </Typography>
            </div>
            <div className='flex gap-2 is-full sm:is-auto'>
              {/* <Button color='error' onClick={() => router.back()} variant='outlined'>
                Hapus Faktur
              </Button> */}
              <Button
                color='secondary'
                variant='outlined'
                startIcon={<i className='ri-upload-2-line' />}
                className='is-full sm:is-auto'
                onClick={handleExport}
              >
                Ekspor
              </Button>
              <Button
                color='secondary'
                variant='outlined'
                startIcon={<i className='ri-printer-line' />}
                className='is-full sm:is-auto'
                onClick={handlePrint}
              >
                Cetak
              </Button>
              {salesInvoiceData?.journalId && (
                <Permission permission={['journal.create']}>
                  <Button
                    color='secondary'
                    variant='outlined'
                    startIcon={<i className='ri-eye-line' />}
                    className='is-full sm:is-auto'
                    onClick={() => setSelectedJournalId(salesInvoiceData?.journalId)}
                  >
                    Cek Jurnal
                  </Button>
                </Permission>
              )}
              <Button
                className='is-full sm:is-auto'
                disabled={salesInvoiceData?.status !== 'APPROVED'}
                onClick={() => router.push(`/cash-bank/receipt/create?invoiceId=${salesInvoiceData?.id}`)}
                variant='contained'
              >
                Buat Penerimaan
              </Button>
            </div>
          </div>
        </Grid>

        {/* Left Column */}
        <Grid item xs={12} md={6}>
          <Grid container spacing={4}>
            {/* Sales Invoice Detail Card */}
            <Grid item xs={12}>
              <SalesInvoiceDetailCard />
            </Grid>
          </Grid>
        </Grid>
        {/* Right Column */}
        <Grid item xs={12} md={6}>
          <Grid container spacing={4}>
            {/* Document Card */}
            <Grid item xs={12}>
              <DocumentCard />
            </Grid>

            {/* Approvals Card */}
            {(salesInvoiceData?.approvals?.length ?? 0) > 0 && (
              <Grid item xs={12}>
                <ApprovalDetailCard approvalList={salesInvoiceData?.approvals ?? []} />
              </Grid>
            )}
          </Grid>
        </Grid>

        <Grid item xs={12}>
          <Grid container spacing={4}>
            {/* Items Card */}
            <Grid item xs={12}>
              <ItemsCard />
            </Grid>

            {/* Other Expense Card */}
            <Grid item xs={12}>
              <OtherExpenseCard />
            </Grid>

            {/* Payment Summary Card */}
            <Grid item xs={12}>
              <PaymentSummaryCard />
            </Grid>

            {/* Activity Log Card */}
            <Grid item xs={12}>
              <ActivityLogCard logList={salesInvoiceLogs} />
            </Grid>
          </Grid>
        </Grid>
      </Grid>
      {!!selectedJournalId && (
        <DialogDetailJournal
          open={!!selectedJournalId}
          setOpen={open => setSelectedJournalId(!open && null)}
          journalId={selectedJournalId}
        />
      )}
    </>
  )
}

const SalesInvoiceDetailPageWithProvider = () => {
  return (
    <SalesInvoiceContextProvider>
      <SalesInvoiceDetailPage />
    </SalesInvoiceContextProvider>
  )
}

export default SalesInvoiceDetailPageWithProvider
