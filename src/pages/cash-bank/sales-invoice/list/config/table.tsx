import { Chip, colors, IconButton, Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'
import { addDays, formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { SalesInvoice } from '@/types/salesInvoiceTypes'
import { toCurrency } from '@/utils/helper'
import { statusChipValue } from '@/pages/sales/invoice/approval/config/table'

type SalesInvoiceWithAction = SalesInvoice & {
  action?: string
}

type RowActionType = {
  showDetail: (id: string) => void
}

// Column Definitions
const columnHelper = createColumnHelper<SalesInvoiceWithAction>()

export const tableColumns = (rowAction: RowActionType, userId?: string) => [
  columnHelper.accessor('number', {
    header: 'NO. FAKTUR',
    cell: ({ row }) => (
      <Typography
        color={colors.green.A400}
        className='cursor-pointer'
        onClick={() => rowAction.showDetail(row.original.id)}
      >
        {row.original.number}
      </Typography>
    )
  }),
  columnHelper.accessor('status', {
    header: 'STATUS',
    cell: ({ row }) => {
      return <Chip label='Belum Diterima' color='default' variant='tonal' size='small' />
    }
  }),
  columnHelper.accessor('invoiceDate', {
    header: 'TGL FAKTUR',
    cell: ({ row }) => (
      <Typography>
        {row.original.invoiceDate
          ? formatDate(row.original.invoiceDate ?? Date.now(), 'dd/MM/yyyy', { locale: id })
          : '-'}
      </Typography>
    )
  }),
  columnHelper.accessor('paymentDueDays', {
    header: 'JATUH TEMPO',
    cell: ({ row }) => (
      <Typography color='red'>
        {formatDate(
          addDays(row.original?.invoiceDate ?? row.original?.createdAt, row.original.paymentDueDays ?? 0),
          'dd/MM/yyyy',
          {
            locale: id
          }
        )}
      </Typography>
    )
  }),
  columnHelper.accessor('customer.name', {
    header: 'CUSTOMER',
    cell: ({ row }) => <Typography>{row.original.customer?.name || '-'}</Typography>
  }),
  columnHelper.accessor('totalAmount', {
    header: 'TOTAL FAKTUR',
    cell: ({ row }) => (
      <Typography color={colors.green.A400}>
        {toCurrency(row.original.totalAmount, false, row.original?.currency?.code)}
      </Typography>
    )
  }),
  columnHelper.accessor('action', {
    header: 'ACTION',
    cell: ({ row }) => (
      <div className='flex items-center gap-0.5'>
        <IconButton size='small' onClick={() => rowAction.showDetail(row.original.id)}>
          <i className='ri-eye-line text-textSecondary' />
        </IconButton>
      </div>
    ),
    enableSorting: false
  })
]
