import { createColumnHelper } from '@tanstack/react-table'
import { PaymentType } from './types'
import { Chip, IconButton, Typography } from '@mui/material'
import { getStatusConfig } from './utils'
import { formatDate } from 'date-fns'
import { thousandSeparator, toCurrency } from '@/utils/helper'
import truncateString from '@/core/utils/truncate'
import { id } from 'date-fns/locale'

const columnHelper = createColumnHelper<PaymentType>()

type RowActionType = {
  onDetail?: (row: PaymentType) => void
  onInvoice?: (id: string) => void
}

export const tableColumns = ({ onDetail, onInvoice }: RowActionType) => [
  columnHelper.accessor('number', {
    header: 'No. Pembayaran',
    cell: ({ row }) => (
      <Typography color='primary' sx={{ cursor: 'pointer' }} onClick={() => onDetail(row.original)}>
        {row.original.number}
      </Typography>
    )
  }),
  columnHelper.accessor('purchaseInvoice.id', {
    header: 'No. Faktur',
    cell: ({ row }) => (
      <Typography
        color='primary'
        sx={{ cursor: 'pointer' }}
        onClick={() => onInvoice(row.original?.purchaseInvoice?.id)}
      >
        {row.original?.purchaseInvoice?.number ?? '-'}
      </Typography>
    )
  }),
  columnHelper.accessor('status', {
    header: 'Status',
    cell: ({ row }) => (
      <Chip
        variant='tonal'
        size='small'
        color={getStatusConfig(row.original.status).color as any}
        label={getStatusConfig(row.original.status).label}
      />
    )
  }),
  columnHelper.accessor('account.id', {
    header: 'Akun Pembayaran',
    cell: ({ row }) =>
      row.original?.account?.id ? `[${row.original?.account?.code}] ${row.original?.account?.name}` : '-'
  }),
  columnHelper.accessor('totalAmount', {
    header: 'Total Pembayaran',
    cell: ({ row }) => (
      <Typography color='primary' className='font-bold'>
        {toCurrency(row.original.totalAmount, false, row.original.currency?.code)}
      </Typography>
    )
  }),
  columnHelper.accessor('voucherNumber', {
    header: 'No Voucher',
    cell: ({ row }) => row.original?.voucherNumber ?? '-'
  }),
  columnHelper.accessor('createdAt', {
    header: 'Tgl Dibuat',
    cell: ({ row }) => {
      return <Typography>{formatDate(row.original.createdAt, 'eeee, dd/MM/yyyy', { locale: id })}</Typography>
    }
  }),
  columnHelper.display({
    id: 'action',
    header: 'Action',
    cell: ({ row }) => {
      return (
        <IconButton onClick={() => onDetail(row.original)}>
          <i className='ri-eye-line text-textSecondary' />
        </IconButton>
      )
    }
  })
]
