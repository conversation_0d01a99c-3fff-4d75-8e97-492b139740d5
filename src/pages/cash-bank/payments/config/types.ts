import { AccountType } from '@/types/accountTypes'
import { CodeNameType } from '@/types/common'
import { UnitType } from '@/types/companyTypes'
import { CurrenciesType } from '@/types/currenciesTypes'
import { ListParams } from '@/types/payload'
import { ProjectType } from '@/types/projectTypes'
import { PurchaseInvoice } from '@/types/purchaseInvoiceTypes'
import { ApproverType, UserOutlineType } from '@/types/userTypes'

export type PaymentTypes = 'GENERAL' | 'PURCHASE'
export type PaymentStatus = 'PROCESSED' | 'APPROVED' | 'REJECTED' | 'CANCELED'

export type PaymentParams = {
  status?: PaymentStatus
  type?: PaymentTypes
  purchaseInvoiceId?: string
  projectId?: string
  projectLabelId?: number
} & ListParams

export type CreatePaymentItemPayload = {
  accountId: string
  amount: number
  description: string
  code?: string
  name?: string
  departmentId?: string
  siteId?: string
  projectLabelId?: string
  unitId?: string
}

export type CreatePaymentPayload = {
  type: string
  purchaseInvoiceId?: string
  purchaseInvoiceIds?: string[]
  items: CreatePaymentItemPayload[]
  transactionDate: string
  currencyId?: string
  exchangeRate?: number
  accountId: string
  approvals: { userId: string }[]
  memo?: string
  useCheckNumber?: boolean
  checkNumber?: string
  recipientInfo?: string
  departmentId?: string
  siteId?: string
  projectId?: string
  projectLabelId?: number
  unitId?: string
  unit?: UnitType
}

export type PaymentApprovalPayload = {
  paymentId: string
  approvalId: number
  status: string
  rejectionNote?: string
}

export type PaymentApproval = ApproverType

export type PaymentType = {
  id: string
  type: string
  number: string
  itemsCount: number
  approvalsCount: number
  transactionDate?: string
  currencyId: string
  currency?: CurrenciesType
  exchangeRate: number
  totalAmount: number
  discountAmount: number
  lcTotalAmount: number
  accountId: string
  journalId: string
  memo: string
  checkNumber: string
  voucherNumber: string
  status: string
  recipientInfo?: string
  companyId: string
  parentCompanyId: string
  departmentId: string
  siteId: string
  projectId: string
  projectLabelId: number
  createdAt: string
  updatedAt: string
  createdBy: string
  account: AccountType
  purchaseInvoiceId: string
  purchaseInvoice: PurchaseInvoice
  items: {
    account: AccountType
    accountId: string
    createdAt: string
    amount: number
    description: string
    id: number
    lcAmount: number
    paymentId: string
    updatedAt: string
  }[]
  approvals: PaymentApproval[]
  department: CodeNameType
  site: CodeNameType
  project: ProjectType
  createdByUser: UserOutlineType
}
