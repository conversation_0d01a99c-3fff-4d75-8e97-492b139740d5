import { useRouter } from '@/routes/hooks'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Button, Grid, Typography } from '@mui/material'
import { Link, useParams, useSearchParams } from 'react-router-dom'
import ItemListCard from './components/ItemListCard'
import { FormProvider, useForm, useFormContext, useWatch } from 'react-hook-form'
import { CreatePaymentItemPayload, CreatePaymentPayload } from '../config/types'
import { useQuery } from '@tanstack/react-query'
import UserQueryMethods, { APPROVER_USER_LIST_QUERY_KEY, DEFAULT_APPROVER_QUERY_KEY } from '@/api/services/user/query'
import CompanyQueryMethods from '@/api/services/company/query'
import { useAuth } from '@/contexts/AuthContext'
import ApprovalListCard from './components/ApprovalListCard'
import { useEffect } from 'react'
import PaymentDetailCard from './components/PaymentDetailCard'
import { useMenu } from '@/components/menu/contexts/menuContext'
import { usePayment } from '../context/PaymentContext'
import { useCreatePayment } from '@/api/services/cashbank/mutation'
import { toast } from 'react-toastify'
import { useDraft } from '@/pages/draft/context/DraftContext'
import DraftQueryMethods from '@/api/services/draft/query'
import { DRAFT_QUERY_KEY } from '@/api/services/draft/service'
import { DraftScope } from '@/types/draftsTypes'

const PaymentCreatePage = () => {
  const router = useRouter()
  const [searchParams] = useSearchParams()
  const { setConfirmState } = useMenu()
  const { ownSiteList, userProfile } = useAuth()
  const { reset, getValues, handleSubmit, control } = useFormContext<CreatePaymentPayload>()
  const { fetchPaymentList } = usePayment()
  const { createDraft, updateDraft, deleteDraft, loadingDraft } = useDraft()

  const { data: draftData } = useQuery({
    enabled: !!searchParams.get('draft'),
    queryKey: [DRAFT_QUERY_KEY, searchParams.get('draft')],
    queryFn: () => DraftQueryMethods.getOneDraft(searchParams.get('draft')),
    cacheTime: 0
  })

  // Rehydrate from draft
  useEffect(() => {
    if (draftData) {
      try {
        const parsed = JSON.parse(draftData.payload) as CreatePaymentPayload
        reset(parsed)
      } catch (e) {
        // ignore
      }
    }
  }, [draftData])

  const { mutate: createMutate, isLoading: loadingMutate } = useCreatePayment()

  const scope = `payment`

  const siteIdWatch = useWatch({
    control,
    name: 'siteId',
    defaultValue: ''
  })

  const departmentIdWatch = useWatch({
    control,
    name: 'departmentId',
    defaultValue: ''
  })

  const { data: approverList } = useQuery({
    enabled: !!siteIdWatch && !!departmentIdWatch,
    queryKey: [DEFAULT_APPROVER_QUERY_KEY, scope, siteIdWatch, departmentIdWatch],
    queryFn: () =>
      UserQueryMethods.getDefaultApproverList({
        limit: 1000,
        divisionId: 'null',
        scope,
        siteId: siteIdWatch,
        departmentId: 'null'
        // departmentId: departmentIdWatch
      }),
    placeholderData: []
  })

  const onSubmit = (data: CreatePaymentPayload) => {
    setConfirmState({
      open: true,
      title: 'Buat Pembayaran',
      content: 'Apakah kamu yakin akan membuat pembayaran ini? Action ini tidak dapat diubah',
      confirmText: 'Buat Pembayaran',
      onConfirm: () => {
        createMutate(data, {
          onSuccess: () => {
            toast.success('Pencatatan berhasil dibuat')
            fetchPaymentList()
            router.push('/cash-bank/payments/list')
            if (draftData) deleteDraft(draftData.id)
          }
        })
      }
    })
  }

  const onSaveDraft = () => {
    const formData = getValues()
    setConfirmState({
      open: true,
      title: 'Simpan Draft Pembayaran',
      content: 'Apakah kamu yakin akan menyimpan draft Pembayaran ini? Pastikan semua detil sudah benar',
      confirmText: 'Simpan',
      onConfirm: () => {
        const payload = JSON.stringify(formData)
        if (draftData) {
          updateDraft(
            { draftId: draftData.id, payload, siteId: formData.siteId ?? undefined },
            {
              onSuccess: () => {
                toast.success('Pembayaran disimpan sebagai draft.')
                router.push('/cash-bank/payments/draft')
              }
            }
          )
        } else {
          createDraft(
            { scope: DraftScope.PAYMENT, payload, siteId: formData.siteId ?? '' },
            {
              onSuccess: () => {
                toast.success('Pembayaran disimpan sebagai draft.')
                router.push('/cash-bank/payments/draft')
              }
            }
          )
        }
      }
    })
  }

  useEffect(() => {
    if (approverList?.length > 0) {
      reset({
        ...getValues(),
        approvals: approverList.map(approver => ({
          userId: approver.user?.id
        }))
      })
    }
  }, [approverList])

  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Breadcrumbs>
          <Link to='#' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Kas & Bank</Typography>
          </Link>
          <Link to='/cash-bank/payments/list' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Pembayaran</Typography>
          </Link>
          <Typography>Tambah Pembayaran</Typography>
        </Breadcrumbs>
      </Grid>
      <Grid item xs={12}>
        <div className='flex flex-col md:flex-row gap-4 justify-between items-end'>
          <div className='flex flex-col'>
            <Typography variant='h4'>Tambah Pencatatan Pembayaran</Typography>
            <Typography>Lengkapi data dan tambahkan pencatatan Pembayaran</Typography>
          </div>
          <div className='flex items-center gap-x-4 max-sm:gap-y-4 is-full flex-col sm:is-auto sm:flex-row'>
            <Button
              disabled={loadingMutate}
              onClick={() => router.back()}
              color='secondary'
              variant='outlined'
              className='is-full sm:is-auto'
            >
              Batalkan
            </Button>
            <Button
              disabled={loadingMutate || loadingDraft}
              onClick={onSaveDraft}
              variant='outlined'
              className='is-full sm:is-auto'
            >
              Simpan Draft
            </Button>
            <Button
              variant='contained'
              disabled={loadingMutate}
              onClick={handleSubmit(onSubmit)}
              className='is-full sm:is-auto'
            >
              Tambah Pembayaran
            </Button>
          </div>
        </div>
      </Grid>
      <Grid item xs={12}>
        <ItemListCard />
      </Grid>
      <Grid item xs={12} md={6}>
        <PaymentDetailCard />
      </Grid>
      <Grid item xs={12} md={6}>
        <ApprovalListCard approverList={approverList?.map(approver => approver.user) ?? []} />
      </Grid>
    </Grid>
  )
}

export default PaymentCreatePage
