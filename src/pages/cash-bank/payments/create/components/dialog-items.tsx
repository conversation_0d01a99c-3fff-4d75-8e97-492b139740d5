import AccountsQueryMethods, { ACCOUNT_LIST_QUERY_KEY } from '@/api/services/account/query'
import CurrencyField from '@/components/numeric/CurrencyField'
import { AccountType } from '@/types/accountTypes'
import LoadingButton from '@mui/lab/LoadingButton'
import {
  Autocomplete,
  Button,
  debounce,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Grid,
  IconButton,
  TextField,
  Typography
} from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { useEffect, useState } from 'react'
import { Controller, useForm } from 'react-hook-form'
import { CreatePaymentItemPayload, CreatePaymentPayload } from '../../config/types'
import { useAuth } from '@/contexts/AuthContext'
import CompanyQueryMethods, { PROJECT_LABEL_LIST_QUERY_KEY } from '@/api/services/company/query'
import { ListResponse } from '@/types/api'
import { defaultListData } from '@/api/queryClient'
import { ProjectLabelType } from '@/types/projectTypes'
import UnitAutocomplete from '@/components/UnitAutocomplete'
import { FormControl, FormHelperText, InputLabel, ListItemText, ListSubheader, MenuItem, Select } from '@mui/material'
import { UnitType } from '@/types/companyTypes'

type DialogItemsProps = {
  open: boolean
  setOpen: (open: boolean) => void
  onSubmit: (item: CreatePaymentItemPayload) => void
  item?: CreatePaymentItemPayload
}

const DialogItems = (props: DialogItemsProps) => {
  const { open, setOpen, item } = props
  const [query, setQuery] = useState('')
  const [selectedAccount, setSelectedAccount] = useState<AccountType>()
  const { groupedSiteList, departmentList } = useAuth()
  const [selectedUnit, setSelectedUnit] = useState<UnitType | null>(null)

  const { control, getValues, handleSubmit, reset, watch } = useForm<CreatePaymentItemPayload>({
    defaultValues: item
  })
  const siteIdWatch = watch('siteId')

  const { data: inventoryAccounts, remove: removeAccounts } = useQuery({
    // enabled: !!query,
    queryKey: [ACCOUNT_LIST_QUERY_KEY, query],
    queryFn: () => AccountsQueryMethods.getAccountList({ limit: Number.MAX_SAFE_INTEGER, level: 1, search: query })
  })

  const {
    data: { items: projectLabels }
  } = useQuery({
    queryKey: [PROJECT_LABEL_LIST_QUERY_KEY, siteIdWatch],
    enabled: !!siteIdWatch,
    queryFn: async () => {
      return CompanyQueryMethods.getProjectLabelList({ limit: 100, siteId: siteIdWatch })
    },
    placeholderData: defaultListData as ListResponse<ProjectLabelType>
  })

  const { data: unitDetail } = useQuery({
    enabled: !!item?.unitId,
    queryKey: ['unit', item?.unitId],
    queryFn: () => CompanyQueryMethods.getUnit(item?.unitId as string)
  })

  const { data: accountDetail } = useQuery({
    enabled: !!item?.accountId,
    queryKey: ['account', item?.accountId],
    queryFn: () => AccountsQueryMethods.getAccount(item?.accountId as string)
  })

  useEffect(() => {
    if (unitDetail) {
      setSelectedUnit(unitDetail)
    }
    if (accountDetail) {
      setSelectedAccount(accountDetail)
    }
  }, [item, unitDetail, accountDetail])

  const handleClose = () => {
    setOpen(false)
  }

  return (
    <Dialog scroll='body' open={open} onClose={setOpen} maxWidth='sm'>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-16'>
        Tambah item Pembayaran
        <Typography component='span' className='flex flex-col text-center'>
          Lengkapi detil Pembayaran
        </Typography>
      </DialogTitle>
      <form onSubmit={e => e.preventDefault()}>
        <DialogContent className='overflow-visible pbs-0 sm:pbe-6 sm:px-16'>
          <IconButton onClick={handleClose} className='absolute block-start-4 inline-end-4'>
            <i className='ri-close-line text-textSecondary' />
          </IconButton>
          <Grid container spacing={4}>
            <Grid item xs={12}>
              <Controller
                control={control}
                name='accountId'
                rules={{ required: true }}
                render={({ field: { onChange, value }, fieldState: { error } }) => (
                  <Autocomplete
                    key={selectedAccount?.id}
                    value={selectedAccount}
                    onInputChange={debounce((e, newValue, reason) => {
                      if (reason === 'input') {
                        setQuery(newValue as string)
                      }
                    }, 700)}
                    options={inventoryAccounts?.items ?? []}
                    getOptionLabel={(option: AccountType) => `[${option.code}] ${option.name}`}
                    freeSolo={!query}
                    noOptionsText='Akun tidak ditemukan'
                    onChange={(e, newValue: AccountType) => {
                      if (newValue) {
                        onChange(newValue.id)
                        reset({
                          ...getValues(),
                          code: newValue.code,
                          name: newValue.name
                        })
                        setSelectedAccount(newValue)
                        removeAccounts()
                      }
                    }}
                    renderInput={params => (
                      <TextField
                        fullWidth
                        error={!!error}
                        {...params}
                        InputProps={{
                          ...params.InputProps,
                          onKeyDown: e => {
                            if (e.key === 'Enter') {
                              e.stopPropagation()
                            }
                          }
                        }}
                        placeholder='Cari kode akun perkiraan'
                        label='Akun Perkiraan'
                      />
                    )}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12}>
              <Controller
                control={control}
                name='amount'
                rules={{ required: true }}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label='Nominal'
                    error={!!error}
                    InputProps={{
                      inputComponent: CurrencyField as any
                    }}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12}>
              <Controller
                control={control}
                name='description'
                render={({ field, fieldState: { error } }) => (
                  <TextField {...field} error={!!error} fullWidth label='Memo' />
                )}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Controller
                name='siteId'
                control={control}
                rules={{ required: true }}
                render={({ field: { value, onChange }, fieldState: { error } }) => (
                  <FormControl fullWidth error={!!error}>
                    <InputLabel id='role-select'>Lokasi</InputLabel>
                    <Select
                      key={value}
                      fullWidth
                      id='select-siteId'
                      value={value}
                      onChange={e => onChange((e.target as HTMLInputElement).value)}
                      label='Lokasi'
                      size='medium'
                      labelId='siteId-select'
                      inputProps={{ placeholder: 'Pilih Lokasi' }}
                      defaultValue=''
                    >
                      {groupedSiteList.map(group => {
                        let children = []
                        children.push(
                          <ListSubheader
                            className='bg-green-50 text-primary font-semibold'
                            key={group.projectId ?? 'no_project'}
                          >
                            {group.project?.name || 'Tanpa Proyek'}
                          </ListSubheader>
                        )
                        group.sites.forEach(site => {
                          children.push(
                            <MenuItem key={site.id} value={site.id}>
                              <ListItemText primary={site.name} />
                            </MenuItem>
                          )
                        })
                        return children
                      })}
                    </Select>
                    {error && <FormHelperText>{error.message}</FormHelperText>}
                  </FormControl>
                )}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Controller
                name='departmentId'
                control={control}
                rules={{ required: true }}
                render={({ field: { value, onChange }, fieldState: { error } }) => (
                  <FormControl fullWidth error={!!error}>
                    <InputLabel id='role-select'>Departemen</InputLabel>
                    <Select
                      key={value}
                      fullWidth
                      id='select-departmentId'
                      value={value}
                      onChange={e => onChange((e.target as HTMLInputElement).value)}
                      label='Departemen'
                      size='medium'
                      labelId='departmentId-select'
                      inputProps={{ placeholder: 'Pilih Departemen' }}
                      defaultValue=''
                    >
                      {departmentList?.map(department => (
                        <MenuItem key={department.id} value={department.id}>
                          {department.name}
                        </MenuItem>
                      ))}
                    </Select>
                    {error && <FormHelperText>{error.message}</FormHelperText>}
                  </FormControl>
                )}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Controller
                control={control}
                name='projectLabelId'
                render={({ field: { value, onChange }, fieldState: { error } }) => (
                  <FormControl fullWidth error={!!error}>
                    <InputLabel id='select-projectLabelId'>Label Proyek</InputLabel>
                    <Select
                      key={value}
                      fullWidth
                      id='select-projectLabelId'
                      value={value}
                      disabled={!siteIdWatch}
                      onChange={e => onChange((e.target as HTMLInputElement).value)}
                      label='Label Proyek'
                      labelId='select-projectLabelId'
                      inputProps={{ placeholder: 'Pilih Label' }}
                    >
                      {projectLabels?.map(label => (
                        <MenuItem key={label.id} value={label.id}>
                          {label.name}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                )}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Controller
                control={control}
                name='unitId'
                rules={{
                  validate: value => {
                    return true
                  }
                }}
                render={({ fieldState: { error } }) => (
                  <UnitAutocomplete
                    value={selectedUnit}
                    onChange={unit => {
                      if (unit) {
                        reset({ ...getValues(), unitId: unit.id })
                        setSelectedUnit(unit)
                      } else {
                        reset({ ...getValues(), unitId: undefined })
                        setSelectedUnit(null)
                      }
                    }}
                    error={!!error}
                    helperText={error?.message}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label='Merk Unit'
                variant='outlined'
                value={selectedUnit?.brandName ?? ''}
                disabled
                InputLabelProps={{ shrink: !!selectedUnit?.brandName }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label='Tipe Unit'
                variant='outlined'
                value={selectedUnit?.type ?? ''}
                disabled
                InputLabelProps={{ shrink: !!selectedUnit?.type }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label='Nomor Lambung'
                variant='outlined'
                value={selectedUnit?.hullNumber ?? ''}
                disabled
                InputLabelProps={{ shrink: !!selectedUnit?.hullNumber }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label='Nomor Rangka'
                variant='outlined'
                value={selectedUnit?.chassisNumber ?? ''}
                disabled
                InputLabelProps={{ shrink: !!selectedUnit?.chassisNumber }}
              />
            </Grid>
          </Grid>
        </DialogContent>
      </form>
      <DialogActions className='justify-center pbs-0 sm:pbe-16 sm:px-16'>
        <Button variant='outlined' onClick={handleClose}>
          Batalkan
        </Button>
        <LoadingButton onClick={handleSubmit(props.onSubmit)} variant='contained' color='primary'>
          Simpan
        </LoadingButton>
      </DialogActions>
    </Dialog>
  )
}

export default DialogItems
