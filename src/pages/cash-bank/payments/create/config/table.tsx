import { createColumnHelper } from '@tanstack/react-table'
import { CreatePaymentItemPayload } from '../../config/types'
import { formatThousandSeparator, thousandSeparator, toCurrency } from '@/utils/helper'
import { IconButton } from '@mui/material'

const columnHelper = createColumnHelper<CreatePaymentItemPayload>()

type RowActionType = {
  delete: (index: number) => void
  edit: (index: number) => void
}

export const tableColumns = (rowAction: RowActionType) => [
  columnHelper.accessor('code', {
    header: 'Kode Akun',
    cell: ({ row }) => row.original.code
  }),
  columnHelper.accessor('name', {
    header: '<PERSON><PERSON>kun',
    cell: ({ row }) => row.original.name
  }),
  columnHelper.accessor('amount', {
    header: 'Nominal',
    cell: ({ row }) =>
      row.original?.amount < 0
        ? `(${formatThousandSeparator(Math.abs(row.original.amount))})`
        : formatThousandSeparator(row.original.amount)
  }),
  columnHelper.accessor('description', {
    header: 'Memo',
    cell: ({ row }) => row.original?.description ?? '-'
  }),
  columnHelper.display({
    id: 'action',
    header: 'Action',
    cell: ({ row }) => {
      return (
        <div className='flex items-center gap-0.5'>
          <IconButton onClick={() => rowAction.edit(row.index)}>
            <i className='ri-edit-line text-textSecondary' />
          </IconButton>
          <IconButton onClick={() => rowAction.delete(row.index)}>
            <i className='ri-delete-bin-line text-textSecondary' />
          </IconButton>
        </div>
      )
    },
    enableSorting: false
  })
]
