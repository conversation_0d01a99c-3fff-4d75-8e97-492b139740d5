import PurchaseInvoiceQueryMethods, { PURCHASE_INVOICE_DETAIL_QUERY_KEY } from '@/api/services/purchase-invoice/query'
import { Card, CardContent, Typography } from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { Link } from 'react-router-dom'

type Props = {
  purchaseInvoiceId?: string
}

const DocInvoiceCard = ({ purchaseInvoiceId }: Props) => {
  const { data: purchaseInvoiceData } = useQuery({
    enabled: !!purchaseInvoiceId,
    queryKey: [PURCHASE_INVOICE_DETAIL_QUERY_KEY, purchaseInvoiceId],
    queryFn: () => PurchaseInvoiceQueryMethods.getPurchaseInvoice(purchaseInvoiceId)
  })
  return (
    <Card>
      <CardContent className='flex flex-col gap-2'>
        <small>Dibuat dari <PERSON>aktur <PERSON>embelian</small>
        <Link to={`/purchase-invoice/list/${purchaseInvoiceData?.id}`}>
          <Typography variant='h5'>No. Faktur: {purchaseInvoiceData?.number}</Typography>
        </Link>
        <Typography variant='caption'>
          {purchaseInvoiceData?.createdAt
            ? formatDate(purchaseInvoiceData?.createdAt, 'eeee, dd/MM/yyyy', { locale: id })
            : '-'}
        </Typography>
      </CardContent>
    </Card>
  )
}

export default DocInvoiceCard
