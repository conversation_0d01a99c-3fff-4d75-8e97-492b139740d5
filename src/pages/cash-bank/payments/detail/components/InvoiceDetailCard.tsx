import PurchaseInvoiceQueryMethods, { PURCHASE_INVOICE_DETAIL_QUERY_KEY } from '@/api/services/purchase-invoice/query'
import { useAuth } from '@/contexts/AuthContext'
import { toCurrency } from '@/utils/helper'
import { Card, CardContent, Grid, Typography } from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'

type Props = {
  purchaseInvoiceId?: string
}

const InvoiceDetailCard = ({ purchaseInvoiceId }: Props) => {
  const { allSites, departmentList } = useAuth()
  const { data: purchaseInvoiceData } = useQuery({
    enabled: !!purchaseInvoiceId,
    queryKey: [PURCHASE_INVOICE_DETAIL_QUERY_KEY, purchaseInvoiceId],
    queryFn: () => PurchaseInvoiceQueryMethods.getPurchaseInvoice(purchaseInvoiceId)
  })
  if (!purchaseInvoiceData) return null
  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <Typography variant='h5'>Detil Faktur</Typography>

        <Grid container spacing={4}>
          <Grid item xs={12} sm={6}>
            <div className='flex flex-col gap-1'>
              <Typography variant='body2' className='text-textSecondary'>
                Vendor
              </Typography>
              <Typography variant='body1' className='font-medium'>
                {purchaseInvoiceData.vendor?.name || purchaseInvoiceData.vendorName || '-'}
              </Typography>
            </div>
          </Grid>
          <Grid item xs={12} sm={6}>
            <div className='flex flex-col gap-1'>
              <Typography variant='body2' className='text-textSecondary'>
                No. Faktur Vendor
              </Typography>
              <Typography variant='body1' className='font-medium'>
                {purchaseInvoiceData.vendorNumber || '-'}
              </Typography>
            </div>
          </Grid>

          <Grid item xs={12} sm={6}>
            <div className='flex flex-col gap-1'>
              <Typography variant='body2' className='text-textSecondary'>
                Tanggal Faktur
              </Typography>
              <Typography variant='body1' className='font-medium'>
                {formatDate(purchaseInvoiceData.invoiceDate, 'dd/MM/yyyy', { locale: id })}
              </Typography>
            </div>
          </Grid>

          <Grid item xs={12} sm={6}>
            <div className='flex flex-col gap-1'>
              <Typography variant='body2' className='text-textSecondary'>
                Lokasi
              </Typography>
              <Typography variant='body1' className='font-medium'>
                {allSites?.find(site => site.id === purchaseInvoiceData.siteId)?.name || '-'}
              </Typography>
            </div>
          </Grid>

          <Grid item xs={12} sm={6}>
            <div className='flex flex-col gap-1'>
              <Typography variant='body2' className='text-textSecondary'>
                Departemen
              </Typography>
              <Typography variant='body1' className='font-medium'>
                {departmentList?.find(department => department.id === purchaseInvoiceData.departmentId)?.name || '-'}
              </Typography>
            </div>
          </Grid>

          <Grid item xs={12} sm={6}>
            <div className='flex flex-col gap-1'>
              <Typography variant='body2' className='text-textSecondary'>
                Label Proyek
              </Typography>
              <Typography variant='body1' className='font-medium'>
                {purchaseInvoiceData?.projectLabel?.name || '-'}
              </Typography>
            </div>
          </Grid>

          <Grid item xs={12} sm={6}>
            <div className='flex flex-col gap-1'>
              <Typography variant='body2' className='text-textSecondary'>
                Metode Pembayaran
              </Typography>
              <Typography variant='body1' className='font-medium'>
                {purchaseInvoiceData.paymentTerms || '-'} {purchaseInvoiceData?.paymentDueDays || ''}
              </Typography>
            </div>
          </Grid>
          <Grid item xs={12} sm={6}>
            <div className='flex flex-col gap-1'>
              <Typography variant='body2' className='text-textSecondary'>
                Jatuh Tempo
              </Typography>
              <Typography variant='body1' className='font-medium text-red-500'>
                {purchaseInvoiceData.paymentDueDate
                  ? formatDate(purchaseInvoiceData.paymentDueDate, 'dd/MM/yyyy', { locale: id })
                  : '-'}
              </Typography>
            </div>
          </Grid>

          <Grid item xs={12} sm={6}>
            <div className='flex flex-col gap-1'>
              <Typography variant='body2' className='text-textSecondary'>
                Mata Uang
              </Typography>
              <Typography variant='body1' className='font-medium'>
                {purchaseInvoiceData.currency?.name ?? 'Rupiah'}
              </Typography>
            </div>
          </Grid>
          <Grid item xs={12} sm={6}>
            <div className='flex flex-col gap-1'>
              <Typography variant='body2' className='text-textSecondary'>
                Konversi ke Rupiah
              </Typography>
              <Typography variant='body1' className='font-medium'>
                {toCurrency(purchaseInvoiceData.exchangeRate ?? 1)}
              </Typography>
            </div>
          </Grid>

          <Grid item xs={12} sm={6}>
            <div className='flex flex-col gap-1'>
              <Typography variant='body2' className='text-textSecondary'>
                Dibuat Oleh
              </Typography>
              <Typography variant='body1' className='font-medium'>
                {purchaseInvoiceData.createdByUser?.fullName || '-'} ({purchaseInvoiceData.createdByUser?.title})
              </Typography>
            </div>
          </Grid>
          <Grid item xs={12} sm={6}>
            <div className='flex flex-col gap-1'>
              <Typography variant='body2' className='text-textSecondary'>
                Memo
              </Typography>
              <Typography variant='body1' className='font-medium'>
                {purchaseInvoiceData.note || '-'}
              </Typography>
            </div>
          </Grid>
          {purchaseInvoiceData?.unit && (
            <>
              <Grid item xs={12} sm={6}>
                <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                  <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                    Kode Unit
                  </small>
                </label>
                <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
                <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                  {purchaseInvoiceData?.unit?.number}
                </p>
              </Grid>
              <Grid item xs={12} sm={6}>
                <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                  <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                    Tipe Unit
                  </small>
                </label>
                <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
                <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                  {purchaseInvoiceData?.unit?.type ?? '-'}
                </p>
              </Grid>
              <Grid item xs={12} sm={6}>
                <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                  <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                    Merk Unit
                  </small>
                </label>
                <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
                <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                  {purchaseInvoiceData?.unit?.brandName ?? '-'}
                </p>
              </Grid>
            </>
          )}
        </Grid>
      </CardContent>
    </Card>
  )
}

export default InvoiceDetailCard
