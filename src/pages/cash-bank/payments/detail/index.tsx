import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Grid, Typo<PERSON> } from '@mui/material'
import { Link } from 'react-router-dom'
import { usePayment } from '../context/PaymentContext'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { getStatusConfig } from '../config/utils'
import CreatedByCard from './components/CreatedByCard'
import ApprovalDetailCard from './components/ApprovalDetailCard'
import DocInvoiceCard from './components/DocInvoiceCard'
import InvoiceDetailCard from './components/InvoiceDetailCard'
import PaymentDetailCard from './components/PaymentDetailCard'
import { useRouter } from '@/routes/hooks'
import { useState } from 'react'
import DialogDetailJournal from '@/components/dialogs/detail-journal-dialog'
import Permission from '@/core/components/Permission'
import { pdf } from '@react-pdf/renderer'
import PaymentPdfDocument from './components/PdfDocument'
import { saveAs } from 'file-saver'
import { useAuth } from '@/contexts/AuthContext'

const PaymentDetailPage = () => {
  const router = useRouter()
  const { ownSiteList } = useAuth()
  const [selectedJournalId, setSelectedJournalId] = useState<string | null>(null)
  const { paymentData } = usePayment()

  const handleExport = async () => {
    const blob = await pdf(
      <PaymentPdfDocument
        paymentData={{
          ...paymentData,
          site: ownSiteList.find(site => site.id === paymentData?.siteId)
        }}
      />
    ).toBlob()
    const filename = paymentData?.number + '.pdf'
    saveAs(blob, filename)
  }

  const handlePrint = async () => {
    const blob = await pdf(
      <PaymentPdfDocument
        paymentData={{
          ...paymentData,
          site: ownSiteList.find(site => site.id === paymentData?.siteId)
        }}
      />
    ).toBlob()
    const url = URL.createObjectURL(blob)
    const printWindow = window.open(url, '_blank')
    printWindow.onload = () => {
      printWindow.print()
      printWindow.onafterprint = () => {
        printWindow.close()
      }
    }
  }

  return (
    <>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <Breadcrumbs>
            <Link to='#' replace>
              <Typography color='var(--mui-palette-text-disabled)'>Kas & Bank</Typography>
            </Link>
            <Link to='/cash-bank/payments/list' replace>
              <Typography color='var(--mui-palette-text-disabled)'>Pembayaran</Typography>
            </Link>
            <Typography>Detil Pembayaran</Typography>
          </Breadcrumbs>
        </Grid>
        <Grid item xs={12}>
          <div className='flex justify-between items-end'>
            <div className='flex flex-col'>
              <div className='flex gap-2 items-start'>
                <Typography variant='h4'>No. Pembayaran: {paymentData?.number}</Typography>
                <Chip
                  size='small'
                  label={getStatusConfig(paymentData?.status).label}
                  color={getStatusConfig(paymentData?.status).color as any}
                  variant='tonal'
                />
              </div>
              <Typography>
                {formatDate(paymentData?.createdAt ?? Date.now(), 'eeee, dd/MM/yyyy', { locale: id })}
              </Typography>
            </div>
            <div className='flex items-center gap-x-4 max-sm:gap-y-4 is-full flex-col sm:is-auto sm:flex-row'>
              <Button
                // disabled={loadingMutate}
                onClick={() => handleExport()}
                startIcon={<i className='ri-upload-2-line' />}
                color='secondary'
                variant='outlined'
                className='is-full sm:is-auto'
              >
                Ekspor
              </Button>
              <Button
                variant='contained'
                startIcon={<i className='ic-outline-local-printshop' />}
                // disabled={loadingMutate}
                onClick={handlePrint}
                className='is-full sm:is-auto'
              >
                Cetak
              </Button>
              {paymentData?.journalId && (
                <Permission permission={['journal.create']}>
                  <Button
                    color='secondary'
                    variant='outlined'
                    startIcon={<i className='ri-eye-line' />}
                    className='is-full sm:is-auto'
                    onClick={() => setSelectedJournalId(paymentData?.journalId)}
                  >
                    Cek Jurnal
                  </Button>
                </Permission>
              )}
            </div>
          </div>
        </Grid>
        <Grid item xs={12} md={6}>
          <Grid container spacing={4}>
            <Grid item xs={12}>
              <PaymentDetailCard />
            </Grid>
            <Grid item xs={12}>
              <CreatedByCard />
            </Grid>
          </Grid>
        </Grid>
        <Grid item xs={12} md={6}>
          <Grid container spacing={4}>
            {paymentData?.purchaseInvoiceId && (
              <>
                <Grid item xs={12}>
                  <DocInvoiceCard purchaseInvoiceId={paymentData?.purchaseInvoiceId} />
                </Grid>
                <Grid item xs={12}>
                  <InvoiceDetailCard purchaseInvoiceId={paymentData?.purchaseInvoiceId} />
                </Grid>
              </>
            )}
            <Grid item xs={12}>
              <ApprovalDetailCard approvalList={paymentData?.approvals ?? []} />
            </Grid>
          </Grid>
        </Grid>
      </Grid>
      {!!selectedJournalId && (
        <DialogDetailJournal
          open={!!selectedJournalId}
          setOpen={open => setSelectedJournalId(!open && null)}
          journalId={selectedJournalId}
        />
      )}
    </>
  )
}

export default PaymentDetailPage
