import useMobileScreen from '@/components/dialogs/hooks/useMobileScreen'
import usePartialState from '@/core/hooks/usePartialState'
import React, { useEffect, useState } from 'react'
import { CashReceiptParams, CashReceiptType, PaymentParams, PaymentType } from '../config/types'
import { useQuery } from '@tanstack/react-query'
import CashBankQueryMethods, {
  PAYMENT_QUERY_KEY,
  PAYMENT_QUERY_LIST_KEY,
  RECEIPT_QUERY_KEY,
  RECEIPT_QUERY_LIST_KEY
} from '@/api/services/cashbank/query'
import { defaultListData } from '@/api/queryClient'
import { ListResponse } from '@/types/api'
import { QueryFn } from '@/types/alias'
import { useLocation, useParams } from 'react-router-dom'
import { AccountType } from '@/types/accountTypes'
import { ApproverType, UserOutlineType } from '@/types/userTypes'
import { useAuth } from '@/contexts/AuthContext'

type ReceiptContextProps = {
  isMobile: boolean
  cashReceiptParams: CashReceiptParams
  setPartialCashReceiptParams: (key: keyof CashReceiptParams, value: CashReceiptParams[keyof CashReceiptParams]) => void
  setCashReceiptParams: React.Dispatch<React.SetStateAction<CashReceiptParams>>
  selectedCashReceiptId: string
  setSelectedCashReceiptId: React.Dispatch<React.SetStateAction<string>>
  cashReceiptListResponse: ListResponse<CashReceiptType>
  fetchCashReceiptList: QueryFn<ListResponse<CashReceiptType>>
  cashReceiptData: CashReceiptType
  fetchCashReceiptData: QueryFn<CashReceiptType>
}

export type CashbankReceiptType = {
  purchaseInvoiceNumber: string
  status: string
  account: AccountType
  receiptAccount: AccountType
  totalAmount: number
  voucher: string
  createdAt: string
  description: string
  memo: string
  createdByUser: UserOutlineType
  approvals: ApproverType[]
}

const ReceiptContext = React.createContext<ReceiptContextProps>({} as ReceiptContextProps)

export const useReceipt = () => {
  const context = React.useContext(ReceiptContext)
  if (context === undefined) {
    throw new Error('usePayment must be used within a PaymentProvider')
  }
  return context
}

export const ReceiptProvider = ({ children }: React.PropsWithChildren<unknown>) => {
  const { isMobile } = useMobileScreen()
  const params = useParams()
  const { pathname } = useLocation()
  const { ownSiteList } = useAuth()
  const [cashReceiptParams, setPartialCashReceiptParams, setCashReceiptParams] = usePartialState<CashReceiptParams>({
    limit: 10,
    page: 1
  })
  const [selectedCashReceiptId, setSelectedCashReceiptId] = useState<string>('')

  const isApprovals = pathname.includes('approval')

  const { data: cashReceiptListResponse, refetch: fetchCashReceiptList } = useQuery({
    queryKey: [RECEIPT_QUERY_LIST_KEY, JSON.stringify(cashReceiptParams), isApprovals],
    queryFn: () => {
      if (isApprovals) {
        return CashBankQueryMethods.getCashReceiptToMe(cashReceiptParams)
      } else {
        return CashBankQueryMethods.getCashReceiptList(cashReceiptParams)
      }
    },
    placeholderData: defaultListData as ListResponse<CashReceiptType>
  })

  const { data: cashReceiptData, refetch: fetchCashReceiptData } = useQuery({
    enabled: !!params?.cashReceiptId,
    queryKey: [RECEIPT_QUERY_KEY, params?.cashReceiptId],
    queryFn: () => CashBankQueryMethods.getCashReceipt(params?.cashReceiptId)
  })

  useEffect(() => {
    if (params.cashReceiptId) {
      setSelectedCashReceiptId(params.cashReceiptId)
    }
  }, [params])

  useEffect(() => {
    setCashReceiptParams({
      ...cashReceiptParams,
      siteIds: ownSiteList.map(site => site.id).join(',')
    })
  }, [ownSiteList])

  const value = {
    isMobile,
    cashReceiptParams,
    setPartialCashReceiptParams,
    setCashReceiptParams,
    selectedCashReceiptId,
    setSelectedCashReceiptId,
    cashReceiptListResponse,
    fetchCashReceiptList,
    cashReceiptData,
    fetchCashReceiptData
  }

  return (
    <ReceiptContext.Provider value={value}>
      <div>{children}</div>
    </ReceiptContext.Provider>
  )
}
