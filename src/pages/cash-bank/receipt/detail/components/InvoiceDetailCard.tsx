import SellingInvoiceQueryMethods from '@/api/services/sales-invoice/query'
import { PurchaseOrderPaymentMethod } from '@/pages/purchase-order/config/enum'
import { paymentMethodOptions } from '@/pages/purchase-order/config/options'
import { toCurrency } from '@/utils/helper'
import { Card, CardContent, Typography } from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'

type Props = {
  purchaseInvoiceId?: string
}

const InvoiceDetailCard = ({ purchaseInvoiceId }: Props) => {
  const { data: salesInvoiceData } = useQuery({
    queryKey: ['SALES_INVOICE_DETAIL_QUERY_KEY', purchaseInvoiceId],
    queryFn: () => SellingInvoiceQueryMethods.getSalesInvoice(purchaseInvoiceId)
  })
  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Detil Faktur</Typography>
        </div>
        <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
          <div className='flex flex-col gap-2 col-span-2'>
            <small>Customer/Pelanggan</small>
            <Typography className='font-medium'>{salesInvoiceData?.customer?.name}</Typography>
          </div>
          <div className='flex flex-col gap-2'>
            <small>Tanggal Faktur</small>
            <Typography className='font-medium'>
              {salesInvoiceData?.invoiceDate
                ? formatDate(salesInvoiceData?.invoiceDate, 'dd/MM/yyyy', { locale: id })
                : '-'}
            </Typography>
          </div>
          <div className='flex flex-col gap-2'>
            <small>Lokasi</small>
            <Typography className='font-medium'>{salesInvoiceData?.site?.name ?? '-'}</Typography>
          </div>
          <div className='flex flex-col gap-2'>
            <small>Departemen</small>
            <Typography className='font-medium'>{salesInvoiceData?.department?.name ?? '-'}</Typography>
          </div>
          <div className='flex flex-col gap-2'>
            <small>Label Proyek</small>
            <Typography className='font-medium'>{salesInvoiceData?.projectLabel?.name ?? '-'}</Typography>
          </div>

          <div className='flex flex-col gap-2'>
            <small>Metode Pembayaran</small>
            <Typography className='font-medium'>
              {paymentMethodOptions.find(opt => opt.value === salesInvoiceData?.paymentTerms)?.label}{' '}
              {salesInvoiceData?.paymentTerms === PurchaseOrderPaymentMethod.NET
                ? salesInvoiceData?.paymentDueDays
                : ''}
            </Typography>
          </div>
          <div className='flex flex-col gap-2'>
            <small>Jatuh Tempo</small>
            <Typography className='font-medium' color='error'>
              {salesInvoiceData?.paymentDueDate ? formatDate(salesInvoiceData?.paymentDueDate, 'dd/MM/yyyy') : '-'}
            </Typography>
          </div>
          <div className='flex flex-col gap-2'>
            <small>Mata Uang</small>
            <Typography className='font-medium'>{salesInvoiceData?.currency?.name ?? '-'}</Typography>
          </div>
          <div className='flex flex-col gap-2'>
            <small>Konversi ke Rupiah</small>
            <Typography className='font-medium'>{toCurrency(salesInvoiceData?.exchangeRate ?? 0)}</Typography>
          </div>
          <div className='flex flex-col gap-2'>
            <small>Dibuat Oleh</small>
            <Typography className='font-medium'>
              {salesInvoiceData?.createdByUser?.fullName} ({salesInvoiceData?.createdByUser?.title})
            </Typography>
          </div>
          <div className='flex flex-col gap-2'>
            <small>Memo</small>
            <Typography className='font-medium'>{salesInvoiceData?.note ?? '-'}</Typography>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default InvoiceDetailCard
