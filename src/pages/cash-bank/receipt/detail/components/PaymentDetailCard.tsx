import { Card, CardContent, Typography } from '@mui/material'
import { useReceipt } from '../../context/ReceiptContext'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { formatThousandSeparator, thousandSeparator, toCurrency } from '@/utils/helper'

const PaymentDetailCard = () => {
  const { cashReceiptData } = useReceipt()
  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Detil Penerimaan</Typography>
        </div>
        <div className='flex flex-col gap-2 w-full'>
          <small><PERSON>gal Penerimaan</small>
          <Typography>
            {formatDate(cashReceiptData?.createdAt ?? Date.now(), 'eeee, dd/MM/yyyy', { locale: id })}
          </Typography>
        </div>
        <div className='flex flex-col gap-2 w-full'>
          <small>Diterima di Akun</small>
          <Typography>
            {cashReceiptData?.account?.name
              ? `[${cashReceiptData?.account?.code}] ${cashReceiptData?.account?.name}`
              : '-'}
          </Typography>
        </div>
        <div className='flex flex-col gap-2 w-full'>
          <small>Pengirim</small>
          <Typography>{cashReceiptData?.senderInfo ?? '-'}</Typography>
        </div>
        <div className='flex flex-col gap-2 w-full'>
          <small>No Voucher</small>
          <Typography>{cashReceiptData?.voucherNumber}</Typography>
        </div>
        <div className='flex flex-col gap-2 w-full'>
          <small>Memo</small>
          <Typography>{cashReceiptData?.memo ?? '-'}</Typography>
        </div>
        {!cashReceiptData?.salesInvoiceId && (
          <div className='flex flex-col gap-2 w-full'>
            <small>Nominal Diterima</small>
            <Typography>{formatThousandSeparator(cashReceiptData?.totalAmount)}</Typography>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default PaymentDetailCard
