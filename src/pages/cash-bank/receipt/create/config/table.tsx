import { createColumnHelper } from '@tanstack/react-table'
import { toCurrency } from '@/utils/helper'
import { IconButton } from '@mui/material'
import { CreateCashReceiptItemPayload } from '../../config/types'
import { DepartmentType, SiteType } from '@/types/companyTypes'
import { ProjectType } from '@/types/projectTypes'

type RowActionType = {
  delete: (index: number) => void
  edit: (index: number) => void
}

type TableColumnsProps = {
  actions: RowActionType
  groupedSiteList: {
    projectId?: string
    project?: ProjectType
    sites: SiteType[]
  }[]
  departmentList: DepartmentType[]
}

const columnHelper = createColumnHelper<CreateCashReceiptItemPayload>()

export const tableColumns = ({ actions, groupedSiteList, departmentList }: TableColumnsProps) => [
  columnHelper.accessor('description', {
    header: 'Deskripsi',
    cell: ({ getValue }) => getValue()
  }),
  columnHelper.accessor('siteId', {
    header: 'Lokasi',
    cell: ({ getValue }) => {
      const siteId = getValue()
      const site = groupedSiteList.flatMap(g => g.sites).find(s => s.id === siteId)
      return site?.name ?? '-'
    }
  }),
  columnHelper.accessor('departmentId', {
    header: 'Departemen',
    cell: ({ getValue }) => {
      const departmentId = getValue()
      const department = departmentList.find(d => d.id === departmentId)
      return department?.name ?? '-'
    }
  }),
  columnHelper.accessor('amount', {
    header: 'Jumlah',
    cell: ({ getValue }) => toCurrency(getValue()),
    footer: props => props.column.id
  }),
  columnHelper.display({
    id: 'action',
    header: 'Aksi',
    meta: {
      headerAlign: 'right'
    },
    cell: props => (
      <div className='flex items-center justify-end'>
        <IconButton onClick={() => actions.edit(props.row.index)}>
          <i className='ri-pencil-line'></i>
        </IconButton>
        <IconButton onClick={() => actions.delete(props.row.index)}>
          <i className='ri-delete-bin-line'></i>
        </IconButton>
      </div>
    )
  })
]
