import { useRouter } from '@/routes/hooks'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Button, Grid, Typography } from '@mui/material'
import { Link, useSearchParams } from 'react-router-dom'
import ItemListCard from './components/ItemListCard'
import { FormProvider, useForm, useWatch } from 'react-hook-form'
import { CreateCashReceiptPayload } from '../config/types'
import { useQuery } from '@tanstack/react-query'
import UserQueryMethods, { DEFAULT_APPROVER_QUERY_KEY } from '@/api/services/user/query'
import ApprovalListCard from './components/ApprovalListCard'
import { useEffect, useState } from 'react'
import ReceiptDetailCard from './components/PaymentDetailCard'
import { useMenu } from '@/components/menu/contexts/menuContext'
import { useReceipt } from '../context/ReceiptContext'
import { useCreateCashReceipt } from '@/api/services/cashbank/mutation'
import { toast } from 'react-toastify'
import { useDraft } from '@/pages/draft/context/DraftContext'
import DraftQueryMethods from '@/api/services/draft/query'
import { DRAFT_QUERY_KEY } from '@/api/services/draft/service'
import { DraftScope } from '@/types/draftsTypes'
import { useAuth } from '@/contexts/AuthContext'
import CompanyQueryMethods, { PROJECT_LABEL_LIST_QUERY_KEY } from '@/api/services/company/query'
import { defaultListData } from '@/api/queryClient'
import { ListResponse } from '@/types/api'
import { ProjectLabelType } from '@/types/projectTypes'
import SalesInvoiceQueryMethods, { SALES_INVOICE_QUERY_KEY } from '@/api/services/sales-invoice/query'

const ReceiptCreatePage = () => {
  const router = useRouter()
  const [searchParams] = useSearchParams()
  const { setConfirmState } = useMenu()
  const { groupedSiteList, departmentList } = useAuth()

  const defaultValues: Partial<CreateCashReceiptPayload> = {
    items: [],
    type: !!searchParams.get('invoiceIds') ? 'SALES' : 'GENERAL'
  }

  const methods = useForm<CreateCashReceiptPayload>({
    defaultValues
  })
  const { reset, control, getValues, handleSubmit, setValue } = methods

  const { fetchCashReceiptList } = useReceipt()

  const { mutate: createMutate, isLoading: loadingMutate } = useCreateCashReceipt()
  const { createDraft, updateDraft, deleteDraft, loadingDraft } = useDraft()

  const { data: draftData } = useQuery({
    enabled: !!searchParams.get('draft'),
    queryKey: [DRAFT_QUERY_KEY, searchParams.get('draft')],
    queryFn: () => DraftQueryMethods.getOneDraft(searchParams.get('draft')),
    cacheTime: 0
  })

  useEffect(() => {
    if (draftData) {
      const parsedPayload = JSON.parse(draftData.payload)
      reset({ ...defaultValues, ...parsedPayload })
    }
  }, [draftData, reset])

  const scope = `cash-receipt`

  const siteIdWatch = useWatch({ control, name: 'siteId', defaultValue: '' })
  const departmentIdWatch = useWatch({ control, name: 'departmentId', defaultValue: '' })

  const { data: approverList } = useQuery({
    enabled: !!siteIdWatch && !!departmentIdWatch,
    queryKey: [DEFAULT_APPROVER_QUERY_KEY, scope, siteIdWatch, departmentIdWatch],
    queryFn: () =>
      UserQueryMethods.getDefaultApproverList({
        limit: 1000,
        divisionId: 'null',
        scope,
        siteId: siteIdWatch,
        departmentId: 'null'
        // departmentId: departmentIdWatch
      }),
    placeholderData: []
  })

  const onSubmit = (data: CreateCashReceiptPayload) => {
    setConfirmState({
      open: true,
      title: 'Buat Penerimaan',
      content: 'Apakah kamu yakin akan penerimaan ini? Action ini tidak dapat diubah',
      confirmText: 'Buat Penerimaan',
      onConfirm: () => {
        createMutate(data, {
          onSuccess: () => {
            toast.success('Pencatatan berhasil dibuat')
            fetchCashReceiptList()
            router.push('/cash-bank/receipt/list', { replace: true })
            if (draftData) deleteDraft(draftData.id)
          }
        })
      }
    })
  }

  useEffect(() => {
    if (approverList?.length > 0) {
      reset({
        ...getValues(),
        approvals: approverList.map(approver => ({
          userId: approver.user?.id
        }))
      })
    }
  }, [approverList])

  const onSaveDraft = () => {
    const formData = getValues()
    setConfirmState({
      open: true,
      title: 'Simpan Draft Penerimaan',
      content: 'Apakah kamu yakin akan menyimpan draft Penerimaan ini? Pastikan semua detil sudah benar',
      confirmText: 'Simpan',
      onConfirm: () => {
        const payload = JSON.stringify(formData)
        if (draftData) {
          updateDraft(
            { draftId: draftData.id, payload, siteId: formData.items?.[0]?.siteId ?? undefined },
            {
              onSuccess: () => {
                toast.success('Penerimaan disimpan sebagai draft.')
                router.push('/cash-bank/receipt/draft')
              }
            }
          )
        } else {
          createDraft(
            { scope: DraftScope['CASH-RECEIPT'], payload, siteId: formData.items?.[0]?.siteId ?? undefined },
            {
              onSuccess: () => {
                toast.success('Penerimaan disimpan sebagai draft.')
                router.push('/cash-bank/receipt/draft')
              }
            }
          )
        }
      }
    })
  }

  return (
    <FormProvider {...methods}>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <Breadcrumbs>
            <Link to='#' replace>
              <Typography color='var(--mui-palette-text-disabled)'>Kas & Bank</Typography>
            </Link>
            <Link to='/cash-bank/receipt/list' replace>
              <Typography color='var(--mui-palette-text-disabled)'>Penerimaan</Typography>
            </Link>
            <Typography>Tambah Penerimaan</Typography>
          </Breadcrumbs>
        </Grid>
        <Grid item xs={12}>
          <div className='flex flex-col md:flex-row justify-between items-end'>
            <div className='flex flex-col'>
              <Typography variant='h4'>Tambah Penerimaan</Typography>
              <Typography>Lengkapi data dan tambahkan pencatatan Penerimaan</Typography>
            </div>
            <div className='flex items-center gap-x-4 max-sm:gap-y-4 is-full flex-col sm:is-auto sm:flex-row'>
              <Button
                disabled={loadingMutate}
                onClick={() => router.back()}
                color='secondary'
                variant='outlined'
                className='is-full sm:is-auto'
              >
                Batalkan
              </Button>
              <Button
                variant='outlined'
                disabled={loadingMutate || loadingDraft}
                onClick={onSaveDraft}
                className='is-full sm:is-auto'
              >
                Simpan Draft
              </Button>
              <Button
                variant='contained'
                disabled={loadingMutate}
                onClick={handleSubmit(onSubmit)}
                className='is-full sm:is-auto'
              >
                Tambah Penerimaan
              </Button>
            </div>
          </div>
        </Grid>
        <Grid item xs={12}>
          <ItemListCard groupedSiteList={groupedSiteList} departmentList={departmentList ?? []} draftData={draftData} />
        </Grid>
        <Grid item xs={12} md={6}>
          <ReceiptDetailCard />
        </Grid>
        <Grid item xs={12} md={6}>
          <ApprovalListCard approverList={approverList?.map(approver => approver.user) ?? []} />
        </Grid>
      </Grid>
    </FormProvider>
  )
}

export default ReceiptCreatePage
