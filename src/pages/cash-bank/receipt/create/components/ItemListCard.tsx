import {
  Autocomplete,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Chip,
  CircularProgress,
  debounce,
  FormHelperText,
  IconButton,
  TextField,
  Typography
} from '@mui/material'
import { useEffect, useMemo, useState } from 'react'
import DialogItems from './dialog-items'
import { Controller, useFieldArray, useFormContext, useWatch } from 'react-hook-form'
import { CreateCashReceiptItemPayload, CreateCashReceiptPayload } from '../../config/types'
import AddSalesInvoiceDialog from '@/components/dialogs/add-sales-invoice-dialog'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import { tableColumns } from '../config/table'
import Table from '@/components/table'
import { isNullOrUndefined, toCurrency } from '@/utils/helper'
import { SalesInvoice } from '@/types/salesInvoiceTypes'
import { useQuery } from '@tanstack/react-query'
import SalesInvoiceQueryMethods, {
  SALES_INVOICE_LIST_QUERY_KEY,
  SALES_INVOICE_QUERY_KEY
} from '@/api/services/sales-invoice/query'
import { useSearchParams } from 'react-router-dom'
import InvoiceListCard from './InvoiceListCard'

import { DepartmentType, SiteType } from '@/types/companyTypes'
import { ProjectLabelType, ProjectType } from '@/types/projectTypes'
import { DraftType } from '@/types/draftsTypes'

type ItemListCardProps = {
  groupedSiteList: {
    projectId?: string
    project?: ProjectType
    sites: SiteType[]
  }[]
  departmentList: DepartmentType[]
  draftData?: DraftType
}

const ItemListCard = ({ groupedSiteList, departmentList, draftData }: ItemListCardProps) => {
  const { control, setValue, getValues, watch, reset } = useFormContext<CreateCashReceiptPayload>()
  const { fields, append, remove, update } = useFieldArray({
    control,
    name: 'items'
  })
  const [open, setOpen] = useState(false)
  const [editedItem, setEditedItem] = useState<{ item: CreateCashReceiptItemPayload; index: number } | null>(null)
  const [dialogItem, setDialogItem] = useState(false)
  const [searchParams, setSearchParams] = useSearchParams()

  const [selectedInvoice, setSelectedInvoice] = useState<SalesInvoice[]>([])
  const [selectedInvoiceId, setSelectedInvoiceId] = useState<string>()

  const typeWatch = useWatch({ control, name: 'type' })

  const generalItems = useMemo(() => fields.filter(item => !item.salesInvoiceId), [fields])

  const handleAddItem = (item: CreateCashReceiptItemPayload) => {
    if (!fields || fields?.length <= 0) {
      setValue('siteId', item?.siteId)
      setValue('departmentId', item?.departmentId)
    }
    if (editedItem) {
      update(editedItem.index, item)
    } else {
      append(item)
    }
    setDialogItem(false)
    setEditedItem(null)
  }

  const table = useReactTable({
    data: generalItems,
    columns: tableColumns({
      actions: {
        delete: index => {
          const originalIndex = fields.findIndex(field => field.id === generalItems[index].id)
          if (originalIndex !== -1) {
            remove(originalIndex)
          }
        },
        edit: index => {
          const originalIndex = fields.findIndex(field => field.id === generalItems[index].id)
          if (originalIndex !== -1) {
            setEditedItem({ item: fields[originalIndex], index: originalIndex })
            setDialogItem(true)
          }
        }
      },
      groupedSiteList,
      departmentList
    }),
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getFacetedMinMaxValues: getFacetedMinMaxValues()
  })

  const totalAmount = useMemo(() => {
    return (
      (selectedInvoice?.reduce((acc, item) => acc + item.totalAmount, 0) || 0) +
      (generalItems?.reduce((acc, item) => acc + (item.amount || 0), 0) || 0)
    )
  }, [selectedInvoice, generalItems])

  const { data: salesInvoiceData } = useQuery({
    enabled: !!selectedInvoiceId,
    queryKey: [SALES_INVOICE_QUERY_KEY, selectedInvoiceId],
    queryFn: () => SalesInvoiceQueryMethods.getSalesInvoice(selectedInvoiceId)
  })

  useEffect(() => {
    setValue('total', totalAmount)
  }, [totalAmount, setValue])

  const removeSalesInvoice = (invoiceId: string) => {
    const newSelectedInvoices = selectedInvoice.filter(inv => inv.id !== invoiceId)
    setSelectedInvoice(newSelectedInvoices)
    setValue(
      'salesInvoiceIds',
      newSelectedInvoices.map(i => i.id)
    )
  }

  useEffect(() => {
    const invoiceIds = searchParams.get('invoiceIds')
    if (invoiceIds) {
      setValue('type', 'SALES')
      const ids = invoiceIds.split(',')
      const promises = ids.map(id => SalesInvoiceQueryMethods.getSalesInvoice(id))
      Promise.all(promises).then(invoices => {
        setSelectedInvoice(invoices)
        setValue(
          'salesInvoiceIds',
          invoices.map(i => i.id)
        )
        setValue('siteId', invoices?.[0]?.siteId)
        setValue('departmentId', invoices?.[0]?.departmentId)
      })
    }
  }, [searchParams])

  useEffect(() => {
    if (typeWatch === 'GENERAL') {
      setSelectedInvoice([])
      setValue('salesInvoiceIds', [])
    } else {
      setValue('items', [])
    }
  }, [typeWatch])

  return (
    <>
      {typeWatch === 'GENERAL' && (
        <>
          <Card>
            <CardContent className='flex flex-col gap-4'>
              <div className='flex justify-between'>
                <Typography variant='h5'>List Item</Typography>
                <Button
                  onClick={() => {
                    setEditedItem(null)
                    setDialogItem(true)
                  }}
                  variant='outlined'
                >
                  Tambah Item
                </Button>
              </div>
              <div>
                {fields?.length > 0 ? (
                  <div className='flex flex-col gap-4'>
                    <div className='rounded-[8px] shadow-md'>
                      <Table table={table} disablePagination headerColor='green' />
                    </div>
                    <div className='flex flex-col gap-1 rounded-md p-3 bg-[#DBF7E8]'>
                      <small>Total Penerimaan</small>
                      <Typography color='primary'>
                        {toCurrency(fields.reduce((acc, item) => acc + item.amount, 0))}
                      </Typography>
                    </div>
                  </div>
                ) : (
                  <Controller
                    control={control}
                    name='items'
                    rules={{ required: true }}
                    render={({ fieldState: { error } }) => (
                      <>
                        <div className='flex flex-col justify-center items-center gap-2'>
                          <Typography variant='h5'>Belum ada item</Typography>
                          <Typography variant='body1'>
                            Tambahkan detil item yang harus diterima dengan tombol diatas
                          </Typography>
                        </div>
                        {!!error && <FormHelperText error>Wajib diisi.</FormHelperText>}
                      </>
                    )}
                  />
                )}
              </div>
            </CardContent>
          </Card>
          {dialogItem && (
            <DialogItems
              open={dialogItem}
              setOpen={setDialogItem}
              onSubmit={handleAddItem}
              item={editedItem?.item as any}
              groupedSiteList={groupedSiteList}
              departmentList={departmentList}
            />
          )}
        </>
      )}

      {typeWatch === 'SALES' && (
        <>
          <div className='flex flex-col gap-4'>
            <Card>
              <CardContent className='flex flex-col gap-4'>
                <div className='flex justify-between items-center'>
                  <Typography variant='h5'>Penerimaan Untuk Faktur</Typography>
                  <Button variant='contained' onClick={() => setOpen(true)}>
                    Pilih Faktur Penjualan
                  </Button>
                </div>
                {selectedInvoice.length > 0 ? (
                  <InvoiceListCard invoices={selectedInvoice} onRemove={removeSalesInvoice} />
                ) : (
                  <Controller
                    control={control}
                    name='salesInvoiceIds'
                    rules={{
                      validate: value => {
                        if ((!value || value.length === 0) && typeWatch === 'SALES') {
                          return 'Wajib diisi.'
                        }
                        return true
                      }
                    }}
                    render={({ fieldState: { error } }) => (
                      <div className='flex flex-col items-center justify-center text-center'>
                        <Typography>Belum ada faktur yang dipilih</Typography>
                        <FormHelperText error={!!error}>{error?.message}</FormHelperText>
                      </div>
                    )}
                  />
                )}
              </CardContent>
            </Card>
          </div>
          {open && (
            <AddSalesInvoiceDialog
              open={open}
              setOpen={setOpen}
              selectedInvoices={selectedInvoice}
              onSubmit={invoices => {
                if (invoices) {
                  if (!selectedInvoice || selectedInvoice?.length <= 0) {
                    setValue('siteId', invoices[0]?.siteId)
                    setValue('departmentId', invoices[0]?.departmentId)
                  }
                  setSelectedInvoice(invoices)
                  setValue(
                    'salesInvoiceIds',
                    invoices.map(i => i.id)
                  )
                }
                setOpen(false)
              }}
            />
          )}
        </>
      )}
    </>
  )
}

export default ItemListCard
