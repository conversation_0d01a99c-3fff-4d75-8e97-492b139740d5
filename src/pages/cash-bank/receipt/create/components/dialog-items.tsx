import AccountsQueryMethods, { ACCOUNT_LIST_QUERY_KEY } from '@/api/services/account/query'
import CurrencyField from '@/components/numeric/CurrencyField'
import { AccountType } from '@/types/accountTypes'
import { DepartmentType, SiteType } from '@/types/companyTypes'
import { ProjectLabelType, ProjectType } from '@/types/projectTypes'
import LoadingButton from '@mui/lab/LoadingButton'
import {
  Autocomplete,
  Button,
  debounce,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  Grid,
  IconButton,
  InputLabel,
  ListItemText,
  ListSubheader,
  MenuItem,
  Select,
  TextField,
  Typography
} from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { useState } from 'react'
import { Controller, useForm, useWatch } from 'react-hook-form'
import { CreateCashReceiptItemPayload } from '../../config/types'
import CompanyQueryMethods, { PROJECT_LABEL_LIST_QUERY_KEY } from '@/api/services/company/query'
import { defaultListData } from '@/api/queryClient'
import { ListResponse } from '@/types/api'

type DialogItemsProps = {
  open: boolean
  setOpen: (open: boolean) => void
  onSubmit: (item: CreateCashReceiptItemPayload) => void
  item?: CreateCashReceiptItemPayload
  groupedSiteList: {
    projectId?: string
    project?: ProjectType
    sites: SiteType[]
  }[]
  departmentList: DepartmentType[]
}

const DialogItems = (props: DialogItemsProps) => {
  const { open, setOpen, item, groupedSiteList, departmentList } = props
  const [query, setQuery] = useState('')
  const [selectedAccount, setSelectedAccount] = useState<AccountType>()

  const { control, getValues, handleSubmit, reset } = useForm<CreateCashReceiptItemPayload>({
    defaultValues: item
  })

  const siteIdWatch = useWatch({
    control,
    name: 'siteId'
  })

  const { data: inventoryAccounts, remove: removeAccounts } = useQuery({
    // enabled: !!query,
    queryKey: [ACCOUNT_LIST_QUERY_KEY, query],
    queryFn: () => AccountsQueryMethods.getAccountList({ limit: Number.MAX_SAFE_INTEGER, level: 1, search: query })
  })

  const {
    data: { items: projectLabels }
  } = useQuery({
    queryKey: [PROJECT_LABEL_LIST_QUERY_KEY, siteIdWatch],
    enabled: !!siteIdWatch,
    queryFn: async () => {
      return CompanyQueryMethods.getProjectLabelList({ limit: 100, siteId: siteIdWatch })
    },
    placeholderData: defaultListData as ListResponse<ProjectLabelType>
  })

  const handleClose = () => {
    setOpen(false)
  }

  return (
    <Dialog scroll='body' open={open} onClose={setOpen} maxWidth='sm'>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-16'>
        Tambah Item Penerimaan
        <Typography component='span' className='flex flex-col text-center'>
          Lengkapi Detil Penerimaan
        </Typography>
      </DialogTitle>
      <form onSubmit={e => e.preventDefault()}>
        <DialogContent className='overflow-visible pbs-0 sm:pbe-6 sm:px-16'>
          <IconButton onClick={handleClose} className='absolute block-start-4 inline-end-4'>
            <i className='ri-close-line text-textSecondary' />
          </IconButton>
          <Grid container spacing={4}>
            <Grid item xs={12}>
              <Controller
                control={control}
                name='accountId'
                rules={{ required: true }}
                render={({ field: { onChange, value }, fieldState: { error } }) => (
                  <Autocomplete
                    value={selectedAccount}
                    onInputChange={debounce((e, newValue, reason) => {
                      if (reason === 'input') {
                        setQuery(newValue as string)
                      }
                    }, 700)}
                    options={inventoryAccounts?.items ?? []}
                    getOptionLabel={(option: AccountType) => `[${option.code}] ${option.name}`}
                    freeSolo={!query}
                    noOptionsText='Akun tidak ditemukan'
                    onChange={(e, newValue: AccountType) => {
                      if (newValue) {
                        onChange(newValue.id)
                        reset({
                          ...getValues(),
                          code: newValue.code,
                          name: newValue.name
                        })
                        setSelectedAccount(newValue)
                        removeAccounts()
                      }
                    }}
                    renderInput={params => (
                      <TextField
                        fullWidth
                        error={!!error}
                        {...params}
                        InputProps={{
                          ...params.InputProps,
                          onKeyDown: e => {
                            if (e.key === 'Enter') {
                              e.stopPropagation()
                            }
                          }
                        }}
                        placeholder='Cari kode akun perkiraan'
                        label='Akun Perkiraan'
                      />
                    )}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12}>
              <Controller
                control={control}
                name='amount'
                rules={{ required: true }}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label='Nominal'
                    error={!!error}
                    InputProps={{
                      inputComponent: CurrencyField as any
                    }}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12}>
              <Controller
                control={control}
                name='description'
                render={({ field, fieldState: { error } }) => (
                  <TextField {...field} error={!!error} fullWidth label='Memo' />
                )}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Controller
                name='siteId'
                control={control}
                rules={{ required: true }}
                render={({ field: { value, onChange }, fieldState: { error } }) => (
                  <FormControl fullWidth error={!!error}>
                    <InputLabel id='role-select'>Lokasi</InputLabel>
                    <Select
                      key={value}
                      fullWidth
                      id='select-siteId'
                      value={value}
                      onChange={e => onChange((e.target as HTMLInputElement).value)}
                      label='Lokasi'
                      size='medium'
                      // disabled={!!purchaseInvoiceIdWatch}
                      labelId='siteId-select'
                      inputProps={{ placeholder: 'Pilih Lokasi' }}
                      defaultValue=''
                    >
                      {groupedSiteList.map(group => {
                        let children = []
                        children.push(
                          <ListSubheader
                            className='bg-green-50 text-primary font-semibold'
                            key={group.projectId ?? 'no_project'}
                          >
                            {group.project?.name || 'Tanpa Proyek'}
                          </ListSubheader>
                        )
                        group.sites.forEach(site => {
                          children.push(
                            <MenuItem key={site.id} value={site.id}>
                              <ListItemText primary={site.name} />
                            </MenuItem>
                          )
                        })
                        return children
                      })}
                    </Select>
                    {/* {error && <FormHelperText>{error.message}</FormHelperText>} */}
                  </FormControl>
                )}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Controller
                name='departmentId'
                control={control}
                rules={{ required: true }}
                render={({ field: { value, onChange }, fieldState: { error } }) => (
                  <FormControl fullWidth error={!!error}>
                    <InputLabel id='role-select'>Departemen</InputLabel>
                    <Select
                      key={value}
                      fullWidth
                      id='select-departmentId'
                      value={value}
                      // disabled={!!purchaseInvoiceIdWatch}
                      onChange={e => onChange((e.target as HTMLInputElement).value)}
                      label='Departemen'
                      size='medium'
                      labelId='departmentId-select'
                      inputProps={{ placeholder: 'Pilih Departemen' }}
                      defaultValue=''
                    >
                      {departmentList?.map(department => (
                        <MenuItem key={department.id} value={department.id}>
                          {department.name}
                        </MenuItem>
                      ))}
                    </Select>
                    {/* {error && <FormHelperText>{error.message}</FormHelperText>} */}
                  </FormControl>
                )}
              />
            </Grid>
            <Grid item xs={12}>
              <Controller
                control={control}
                name='projectLabelId'
                render={({ field: { value, onChange }, fieldState: { error } }) => (
                  <FormControl fullWidth error={!!error}>
                    <InputLabel id='select-projectLabelId'>Label Proyek</InputLabel>
                    <Select
                      key={value}
                      fullWidth
                      id='select-projectLabelId'
                      value={value}
                      disabled={!siteIdWatch}
                      onChange={e => onChange((e.target as HTMLInputElement).value)}
                      label='Label Proyek'
                      labelId='select-projectLabelId'
                      inputProps={{ placeholder: 'Pilih Label' }}
                    >
                      {projectLabels?.map(label => (
                        <MenuItem key={label.id} value={label.id}>
                          {label.name}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                )}
              />
            </Grid>
          </Grid>
        </DialogContent>
      </form>
      <DialogActions className='justify-center pbs-0 sm:pbe-16 sm:px-16'>
        <Button variant='outlined' onClick={handleClose}>
          Batalkan
        </Button>
        <LoadingButton onClick={handleSubmit(props.onSubmit)} variant='contained' color='primary'>
          Simpan
        </LoadingButton>
      </DialogActions>
    </Dialog>
  )
}

export default DialogItems
