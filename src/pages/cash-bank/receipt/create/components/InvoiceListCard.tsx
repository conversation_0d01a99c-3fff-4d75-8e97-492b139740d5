import { Chip, IconButton, Typography } from '@mui/material'
import { toCurrency } from '@/utils/helper'
import { SalesInvoice } from '@/types/salesInvoiceTypes'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { statusChipValue } from '@/pages/sales/invoice/approval/config/table'

type InvoiceListCardProps = {
  invoices: SalesInvoice[]
  onRemove: (invoiceId: string) => void
}

const InvoiceListCard = ({ invoices, onRemove }: InvoiceListCardProps) => {
  if (invoices.length === 0) {
    return null
  }

  return (
    <div className='flex flex-col gap-4'>
      {invoices.map(invoice => (
        <div key={invoice.id}>
          <div className='flex justify-between gap-2 p-4 rounded-[8px] bg-[#4C4E640D]'>
            <div className='flex flex-col gap-2'>
              <div className='flex gap-2 items-center'>
                <Typography variant='h5'>No. Faktur: {invoice?.number}</Typography>
                <Chip
                  label={statusChipValue[invoice?.status]?.label}
                  size='small'
                  variant='tonal'
                  color={statusChipValue[invoice?.status]?.color}
                />
              </div>
              <small>{formatDate(invoice?.createdAt, 'eeee, dd/MM/yyyy', { locale: id })}</small>
            </div>
            <IconButton onClick={() => onRemove(invoice.id)}>
              <i className='ri-close-circle-line' />
            </IconButton>
          </div>
          <div className='grid grid-cols-2 md:grid-cols-3 gap-2 mt-2'>
            <div className='flex flex-col gap-2 p-4 rounded-[8px] bg-[#4C4E640D]'>
              <small>Sub Total Faktur</small>
              <Typography className='font-semibold'>
                {toCurrency(invoice?.subTotalAmount, false, invoice?.currency?.code)}
              </Typography>
            </div>
            <div className='flex flex-col gap-2 p-4 rounded-[8px] bg-[#4C4E640D]'>
              <small>Biaya Lain Lain</small>
              <Typography className='font-semibold'>
                {toCurrency(invoice?.otherAmount, false, invoice?.currency?.code)}
              </Typography>
            </div>
            <div className='flex flex-col gap-2 p-4 rounded-[8px] bg-[#DBF7E8]'>
              <small>Total Faktur</small>
              <Typography variant='h6' color='black' className='font-semibold'>
                {toCurrency(invoice?.totalAmount, false, invoice?.currency?.code)}
              </Typography>
            </div>
          </div>
        </div>
      ))}
      <div className='flex flex-col gap-1 rounded-md p-3 bg-[#DBF7E8]'>
        <Typography color='black'>Total Pembayaran</Typography>
        <Typography color='black' className='font-semibold'>
          {toCurrency(invoices.reduce((acc, item) => acc + item.totalAmount, 0))}
        </Typography>
      </div>
    </div>
  )
}

export default InvoiceListCard
