import { createColumnHelper } from '@tanstack/react-table'
import { CashReceiptType, PaymentType } from './types'
import { Chip, IconButton, Typography } from '@mui/material'
import { getStatusConfig } from './utils'
import { formatDate } from 'date-fns'
import { thousandSeparator, toCurrency } from '@/utils/helper'
import truncateString from '@/core/utils/truncate'
import { id } from 'date-fns/locale'

const columnHelper = createColumnHelper<CashReceiptType>()

type RowActionType = {
  onDetail?: (row: CashReceiptType) => void
  onInvoice?: (id: string) => void
}

export const tableColumns = ({ onDetail, onInvoice }: RowActionType) => [
  columnHelper.accessor('number', {
    header: 'No. Penerimaan',
    cell: ({ row }) => (
      <Typography color='primary' onClick={() => onDetail(row.original)} sx={{ cursor: 'pointer' }}>
        {row.original.number}
      </Typography>
    )
  }),
  columnHelper.display({
    id: 'invoice',
    header: 'No. Faktur',
    cell: ({ row }) => (
      <Typography onClick={() => onInvoice(row.original?.salesInvoiceId)} color='primary'>
        {row.original.salesInvoice?.number ?? '-'}
      </Typography>
    )
  }),
  columnHelper.accessor('status', {
    header: 'Status',
    cell: ({ row }) => (
      <Chip
        variant='tonal'
        size='small'
        color={getStatusConfig(row.original.status).color as any}
        label={getStatusConfig(row.original.status).label}
      />
    )
  }),
  columnHelper.accessor('accountId', {
    header: 'Akun Penerimaan',
    cell: ({ row }) => (row.original?.account ? `[${row.original?.account?.code}] ${row.original?.account?.name}` : '-')
  }),
  columnHelper.accessor('totalAmount', {
    header: 'Total Penerimaan',
    cell: ({ row }) => (
      <Typography color='primary'>
        {toCurrency(row.original.totalAmount, false, row.original?.currency?.code ?? 'IDR')}
      </Typography>
    )
  }),
  columnHelper.accessor('voucherNumber', {
    header: 'No. Voucher',
    cell: ({ row }) => row.original?.voucherNumber ?? '-'
  }),
  columnHelper.accessor('createdAt', {
    header: 'Tanggal',
    cell: ({ row }) => {
      return <Typography>{formatDate(row.original.createdAt, 'eeee, dd/MM/yyyy', { locale: id })}</Typography>
    }
  }),
  columnHelper.display({
    id: 'action',
    header: 'Action',
    cell: ({ row }) => {
      return (
        <IconButton onClick={() => onDetail(row.original)}>
          <i className='ri-eye-line text-textSecondary' />
        </IconButton>
      )
    }
  })
]
