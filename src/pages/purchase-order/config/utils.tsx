import { WarehouseItemType } from '@/types/appTypes'
import { PurchaseOrderDiscountType, PurchaseOrderTaxType } from './enum'

const totalDiscountFromItems = (items: WarehouseItemType[]) => {
  if (!items || !Array.isArray(items)) return 0
  return items.reduce((acc, item) => {
    let discountValue = 0
    if (item.discountType === PurchaseOrderDiscountType.FLAT) {
      discountValue = item.discountValue
    } else if (item.discountType === PurchaseOrderDiscountType.PERCENTAGE) {
      discountValue = Math.round(item.subtotalPrice * item.discountValue) / 100
    }
    return acc + discountValue
  }, 0)
}

const calculateSubtotalPrice = (items: WarehouseItemType[]) => {
  if (!items || !Array.isArray(items)) return 0
  return items.reduce((acc, item) => {
    let subTotalPrice = 0
    if (item.taxType === PurchaseOrderTaxType.INCLUDE_TAX) {
      subTotalPrice = item.pricePerUnit * item.quantity - item.taxAmount
    } else {
      subTotalPrice = item.subtotalPrice
    }
    return acc + subTotalPrice
  }, 0)
}

const calculateTotalTaxAmount = (items: WarehouseItemType[]) => {
  if (!items || !Array.isArray(items)) return 0
  return items.reduce((acc, curr) => acc + curr.taxAmount, 0)
}

export { totalDiscountFromItems, calculateSubtotalPrice, calculateTotalTaxAmount }
