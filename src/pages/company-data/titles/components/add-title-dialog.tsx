import {
  Button,
  <PERSON><PERSON>,
  <PERSON>alog<PERSON><PERSON>,
  <PERSON>alogContent,
  DialogTitle,
  Grid,
  IconButton,
  TextField,
  Typography
} from '@mui/material'
import { CreateTitlePayload, TitleType } from '@/types/companyTypes'
import { Controller, useForm } from 'react-hook-form'
import { useEffect } from 'react'
import LoadingButton from '@mui/lab/LoadingButton'
import { useCreateTitle, useUpdateTitle } from '@/api/services/company/mutation'
import { useTitles } from '../context/TitleContext'
import { toast } from 'react-toastify'

type AddTitleDialogProps = {
  open: boolean
  setOpen: (open: boolean) => void
  title?: TitleType
}

const AddTitleDialog = ({ open, setOpen, title }: AddTitleDialogProps) => {
  const { fetchTitleList, onDeleteTitle } = useTitles()
  const {
    control,
    reset,
    handleSubmit,
    formState: { errors }
  } = useForm<CreateTitlePayload>()
  const handleClose = () => {
    setOpen(false)
  }

  const { mutate: createMutate, isLoading: loadingCreate } = useCreateTitle()
  const { mutate: updateMutate, isLoading: loadingUpdate } = useUpdateTitle()

  const isLoading = loadingCreate || loadingUpdate

  const onSubmitHandler = (data: CreateTitlePayload) => {
    if (title) {
      updateMutate(
        {
          id: String(title.id),
          ...data
        },
        {
          onSuccess: () => {
            toast.success('Jabatan berhasil diubah')
            fetchTitleList()
            setOpen(false)
          }
        }
      )
    } else {
      createMutate(data, {
        onSuccess: () => {
          toast.success('Jabatan berhasil ditambahkan')
          fetchTitleList()
          setOpen(false)
        }
      })
    }
  }

  useEffect(() => {
    if (title) {
      reset(title)
    }
  }, [title])

  return (
    <Dialog open={open} onClose={handleClose}>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
        {title ? 'Ubah' : 'Tambah'} Jabatan
        {!title && (
          <Typography component='span' className='flex flex-col text-center'>
            Tambahkan Jabatan di perusahaan kamu
          </Typography>
        )}
      </DialogTitle>
      <DialogContent className='pbs-0 sm:pbe-16 sm:px-12 !py-4'>
        {title && (
          <IconButton onClick={() => onDeleteTitle(String(title.id))} className='absolute block-start-4 inline-start-4'>
            <i className='ri-delete-bin-7-line text-error' />
          </IconButton>
        )}
        <IconButton onClick={() => setOpen(false)} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <Grid container spacing={5}>
          <Grid item xs={12}>
            <Typography className='font-medium'>Jabatan</Typography>
          </Grid>
          <Grid item xs={6} md={4}>
            <Controller
              control={control}
              name='code'
              rules={{
                required: 'Wajib diisi',
                validate: value => (value && !/\s/.test(value) ? true : 'Spasi tidak diperbolehkan')
              }}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  fullWidth
                  {...field}
                  placeholder='Contoh: MNG-1'
                  label='Kode Jabatan'
                  error={!!error}
                  helperText={error?.message}
                  InputLabelProps={{
                    shrink: !!title ? true : undefined
                  }}
                />
              )}
            />
          </Grid>
          <Grid item xs={6} md={8}>
            <Controller
              control={control}
              name='name'
              rules={{ required: true }}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  fullWidth
                  {...field}
                  placeholder='Contoh: Manajemen Perusahaan'
                  label='Nama Jabatan'
                  error={!!error}
                  helperText={!!error && 'Wajib diisi'}
                  InputLabelProps={{
                    shrink: !!title ? true : undefined
                  }}
                />
              )}
            />
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions className='gap-2 sm:gap-6 max-sm:flex-col max-sm:items-center justify-center pbs-0 sm:pbe-10 sm:px-16'>
        <Button onClick={() => setOpen(false)} disabled={isLoading} variant='outlined' className='is-full sm:is-auto'>
          BATALKAN
        </Button>
        <LoadingButton
          startIcon={<></>}
          loading={isLoading}
          loadingPosition='start'
          variant='contained'
          onClick={handleSubmit(onSubmitHandler)}
          className='px-8 is-full !ml-0 sm:is-auto'
        >
          {title ? 'UBAH DATA' : 'TAMBAHKAN'}
        </LoadingButton>
      </DialogActions>
    </Dialog>
  )
}

export default AddTitleDialog
