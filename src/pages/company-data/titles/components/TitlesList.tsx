import DebouncedInput from '@/components/DebounceInput'
import { Card, Button, Typography } from '@mui/material'
import { useTitles } from '../context/TitleContext'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import { tableColumns } from '../config/table'
import { useMemo } from 'react'
import Table from '@/components/table'
import Permission from '@/core/components/Permission'

const TitlesList = () => {
  const {
    titleParams,
    setTitleParams,
    setPartialTitleParams,
    titleListResponse,
    setAddTitleDialogOpen,
    setSelectedTitle,
    onDeleteTitle,
    onAddTitle
  } = useTitles()
  const { search, page, limit } = titleParams
  const { totalItems } = titleListResponse

  const tableOptions: any = useMemo(
    () => ({
      data: titleListResponse.items ?? [],
      columns: tableColumns({
        detail: row => {
          setSelectedTitle(row)
          setAddTitleDialogOpen(true)
        },
        delete: id => onDeleteTitle(String(id))
      }),
      initialState: {
        pagination: {
          pageSize: limit ?? 10,
          pageIndex: page - 1
        }
      },
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [titleListResponse, titleParams]
  )

  const table = useReactTable(tableOptions)

  return (
    <Card>
      <div className='flex justify-between gap-4 p-5 flex-col items-start sm:flex-row items-center'>
        <div className='flex gap-4 items-center flex-col sm:flex-row is-full sm:is-auto'>
          <DebouncedInput
            value={search}
            onChange={value => setTitleParams(prev => ({ ...prev, page: 1, search: value as string }))}
            placeholder='Cari..'
            className='is-full sm:w-[240px]'
          />
        </div>
        <div className='flex items-center gap-x-4 max-sm:gap-y-4 is-full flex-col sm:is-auto sm:flex-row'>
          {/* <Button
            color='secondary'
            variant='outlined'
            startIcon={<i className='ri-upload-2-line' />}
            className='is-full sm:is-auto'
          >
            Ekspor
          </Button> */}
          <Permission permission='title.write'>
            <Button
              variant='contained'
              onClick={onAddTitle}
              startIcon={<i className='ic-baseline-add' />}
              className='is-full sm:is-auto'
            >
              Tambah
            </Button>
          </Permission>
        </div>
      </div>
      <Table
        table={table}
        emptyLabel={
          <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
            <Typography>Belum ada Data Jabatan</Typography>
            <Typography className='text-sm text-gray-400'>
              Semua data jabatan yang telah dibuat akan ditampilkan di sini
            </Typography>
          </td>
        }
        onRowsPerPageChange={pageSize => {
          if (pageSize > totalItems) {
            setTitleParams(prev => ({ ...prev, limit: totalItems, page: 1 }))
          } else {
            setPartialTitleParams('limit', pageSize)

            const maxPage = Math.ceil(totalItems / pageSize)
            if (page > maxPage) {
              setTitleParams(prev => ({ ...prev, page: maxPage }))
            }
          }
        }}
        onPageChange={pageIndex => setPartialTitleParams('page', pageIndex)}
      />
    </Card>
  )
}

export default TitlesList
