import { defaultListData } from '@/api/queryClient'
import CompanyQueryMethods, { TITLE_LIST_QUERY_KEY } from '@/api/services/company/query'
import useMobileScreen from '@/components/dialogs/hooks/useMobileScreen'
import usePartialState from '@/core/hooks/usePartialState'
import { QueryFn } from '@/types/alias'
import { ListResponse } from '@/types/api'
import { TitleType } from '@/types/companyTypes'
import { ListParams } from '@/types/payload'
import { useQuery } from '@tanstack/react-query'
import { createContext, useContext, useState } from 'react'
import AddTitleDialog from '../components/add-title-dialog'
import { useDeleteTitle } from '@/api/services/company/mutation'
import { toast } from 'react-toastify'
import { useMenu } from '@/components/menu/contexts/menuContext'

type TitleContextProps = {
  titleListResponse: ListResponse<TitleType>
  fetchTitleList: QueryFn<ListResponse<TitleType>>
  titleParams: ListParams
  setTitleParams: React.Dispatch<React.SetStateAction<ListParams>>
  setPartialTitleParams: (fieldName: keyof ListParams, value: any) => void
  isMobile: boolean
  selectedTitle: TitleType | null
  setSelectedTitle: React.Dispatch<React.SetStateAction<TitleType | null>>
  setAddTitleDialogOpen: React.Dispatch<React.SetStateAction<boolean>>
  onDeleteTitle: (id: string) => void
  onAddTitle: () => void
}

const TitleContext = createContext<TitleContextProps>({} as TitleContextProps)

export const useTitles = () => {
  const context = useContext(TitleContext)
  if (context === undefined) {
    throw new Error('useTitles must be used within TitleContextProvider')
  }
  return context
}

export const TitleContextProvider = ({ children }: { children: React.ReactNode }) => {
  const { isMobile } = useMobileScreen()
  const { setConfirmState } = useMenu()

  const [addTitleDialogOpen, setAddTitleDialogOpen] = useState(false)
  const [selectedTitle, setSelectedTitle] = useState<TitleType>()
  const [titleParams, setPartialTitleParams, setTitleParams] = usePartialState<ListParams>({
    limit: 10,
    page: 1
  })

  const { data: titleListResponse, refetch: fetchTitleList } = useQuery({
    queryKey: [TITLE_LIST_QUERY_KEY, JSON.stringify(titleParams)],
    queryFn: () => CompanyQueryMethods.getTitleList(titleParams),
    placeholderData: defaultListData as ListResponse<TitleType>
  })

  const { mutate: deleteTitleMutate } = useDeleteTitle()

  const onAddTitle = () => {
    setAddTitleDialogOpen(true)
    setSelectedTitle(null)
  }

  const onDeleteTitle = (id: string) => {
    setConfirmState({
      open: true,
      title: 'Hapus Jabatan',
      content: 'Apakah kamu yakin ingin menghapus jabatan ini?',
      confirmText: 'Hapus',
      confirmColor: 'error',
      onConfirm: () => {
        deleteTitleMutate(id, {
          onSuccess: () => {
            fetchTitleList()
            toast.success('Jabatan berhasil dihapus')
          }
        })
      }
    })
  }

  const value = {
    titleListResponse,
    fetchTitleList,
    selectedTitle,
    setSelectedTitle,
    titleParams,
    setTitleParams,
    setPartialTitleParams,
    isMobile,
    setAddTitleDialogOpen,
    onDeleteTitle,
    onAddTitle
  }
  return (
    <TitleContext.Provider value={value}>
      {addTitleDialogOpen && (
        <AddTitleDialog title={selectedTitle} open={addTitleDialogOpen} setOpen={setAddTitleDialogOpen} />
      )}
      {children}
    </TitleContext.Provider>
  )
}
