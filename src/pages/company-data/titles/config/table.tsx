import { TitleType } from '@/types/companyTypes'
import { Box, IconButton, Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'
import { format } from 'date-fns'
import React from 'react'

const columnHelper = createColumnHelper<TitleType>()

type RowAction = {
  detail: (row: TitleType) => void
  delete: (id: number) => void
}

export const tableColumns = (rowAction: RowAction) => [
  columnHelper.accessor('code', {
    header: 'Kode Jabatan',
    cell: ({ row }) => (
      <Typography
        role='button'
        sx={{ cursor: 'pointer' }}
        color='primary'
        onClick={() => rowAction.detail(row.original)}
      >
        {row.original.code}
      </Typography>
    )
  }),
  columnHelper.accessor('name', {
    header: 'Nama <PERSON>'
  }),
  columnHelper.accessor('createdAt', {
    header: 'Tgl Dibuat',
    cell: ({ row }) => (row.original?.createdAt ? format(row.original.createdAt, 'dd/MM/yyyy') : '-')
  }),
  columnHelper.display({
    id: 'actions',
    header: 'Action',
    cell: ({ row }) => {
      return (
        <Box className='flex gap-2 items-center'>
          <IconButton onClick={() => rowAction.delete(row.original.id)}>
            <i className='ri-delete-bin-7-line' />
          </IconButton>
          <IconButton onClick={() => rowAction.detail(row.original)}>
            <i className='ri-eye-line' />
          </IconButton>
        </Box>
      )
    }
  })
]
