import { But<PERSON>, Card, CardContent, Typography } from '@mui/material'
import { useState } from 'react'
import DialogAccountsCategory from './dialog-accounts-category'
import { CategoryType } from '@/types/companyTypes'
import { useCategory } from '../../../context/CategoryContext'
import { isNullOrUndefined } from '@/utils/helper'
import Permission from '@/core/components/Permission'

const AccountsCard = ({ categoryData }: { categoryData: CategoryType }) => {
  const { fetchAccountCategoryData, accountCategoryData, fetchCategoryData } = useCategory()
  const [editDialog, setEditDialog] = useState(false)

  const isAccountsEmpty =
    isNullOrUndefined(accountCategoryData?.inventoryAccount) &&
    isNullOrUndefined(accountCategoryData?.expenseAccount) &&
    isNullOrUndefined(accountCategoryData?.salesAccount) &&
    isNullOrUndefined(accountCategoryData?.salesReturnAccount) &&
    isNullOrUndefined(accountCategoryData?.salesDiscountAccount) &&
    isNullOrUndefined(accountCategoryData?.goodsShippedAccount) &&
    isNullOrUndefined(accountCategoryData?.cogsAccount) &&
    isNullOrUndefined(accountCategoryData?.purchaseReturnAccount) &&
    isNullOrUndefined(accountCategoryData?.unbilledPurchaseAccount)

  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-4'>
          <div className='flex justify-between items-center'>
            <Typography variant='h5'>Akun Perkiraan</Typography>
            <Permission permission={['category.write']}>
              <Button size='small' onClick={() => setEditDialog(true)} variant='outlined'>
                Edit Akun
              </Button>
            </Permission>
          </div>
          {isAccountsEmpty ? (
            <div className='flex flex-col gap-2 items-center py-6'>
              <Typography variant='h5'>Belum ada Akun Perkiraan</Typography>
              <Typography variant='body1' align='center'>
                Kontak tim accounting untuk mengisi akun perkiraan kategori dan barang ini
              </Typography>
            </div>
          ) : (
            <>
              {!!accountCategoryData?.inventoryAccount && (
                <div className='flex flex-col gap-2'>
                  <small>Persediaan</small>
                  <Typography>{`[${accountCategoryData?.inventoryAccount?.code}] ${accountCategoryData?.inventoryAccount?.name}`}</Typography>
                </div>
              )}
              {!!accountCategoryData?.expenseAccount && (
                <div className='flex flex-col gap-2'>
                  <small>Beban</small>
                  <Typography>{`[${accountCategoryData?.expenseAccount?.code}] ${accountCategoryData?.expenseAccount?.name}`}</Typography>
                </div>
              )}
              {!!accountCategoryData?.salesAccount && (
                <div className='flex flex-col gap-2'>
                  <small>Penjualan</small>
                  <Typography>{`[${accountCategoryData?.salesAccount?.code}] ${accountCategoryData?.salesAccount?.name}`}</Typography>
                </div>
              )}
              {!!accountCategoryData?.salesReturnAccount && (
                <div className='flex flex-col gap-2'>
                  <small>Retur Penjualan</small>
                  <Typography>{`[${accountCategoryData?.salesReturnAccount?.code}] ${accountCategoryData?.salesReturnAccount?.name}`}</Typography>
                </div>
              )}
              {!!accountCategoryData?.salesDiscountAccount && (
                <div className='flex flex-col gap-2'>
                  <small>Diskon Penjualan</small>
                  <Typography>{`[${accountCategoryData?.salesDiscountAccount?.code}] ${accountCategoryData?.salesDiscountAccount?.name}`}</Typography>
                </div>
              )}
              {!!accountCategoryData?.goodsShippedAccount && (
                <div className='flex flex-col gap-2'>
                  <small>Barang Terkirim</small>
                  <Typography>{`[${accountCategoryData?.goodsShippedAccount?.code}] ${accountCategoryData?.goodsShippedAccount?.name}`}</Typography>
                </div>
              )}
              {!!accountCategoryData?.cogsAccount && (
                <div className='flex flex-col gap-2'>
                  <small>Harga Pokok Penjualan</small>
                  <Typography>{`[${accountCategoryData?.cogsAccount?.code}] ${accountCategoryData?.cogsAccount?.name}`}</Typography>
                </div>
              )}
              {!!accountCategoryData?.purchaseReturnAccount && (
                <div className='flex flex-col gap-2'>
                  <small>Retur Pembelian</small>
                  <Typography>{`[${accountCategoryData?.purchaseReturnAccount?.code}] ${accountCategoryData?.purchaseReturnAccount?.name}`}</Typography>
                </div>
              )}
              {!!accountCategoryData?.unbilledPurchaseAccount && (
                <div className='flex flex-col gap-2'>
                  <small>Pembelian Belum Tertagih</small>
                  <Typography>{`[${accountCategoryData?.unbilledPurchaseAccount?.code}] ${accountCategoryData?.unbilledPurchaseAccount?.name}`}</Typography>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>
      {editDialog && (
        <DialogAccountsCategory
          callback={() => Promise.all([fetchAccountCategoryData(), fetchCategoryData()])}
          categoryData={categoryData}
          open={editDialog}
          setOpen={setEditDialog}
        />
      )}
    </>
  )
}

export default AccountsCard
