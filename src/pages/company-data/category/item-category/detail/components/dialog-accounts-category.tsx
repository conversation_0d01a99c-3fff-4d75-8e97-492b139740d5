import AccountsQueryMethods, { ACCOUNT_LIST_QUERY_KEY } from '@/api/services/account/query'
import AccountsService from '@/api/services/account/service'
import { useUpdateCategory } from '@/api/services/company/mutation'
import { useMenu } from '@/components/menu/contexts/menuContext'
import { AccountParams, AccountType } from '@/types/accountTypes'
import { CategoryType } from '@/types/companyTypes'
import { zodResolver } from '@hookform/resolvers/zod'
import LoadingButton from '@mui/lab/LoadingButton'
import {
  Autocomplete,
  Button,
  debounce,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Grid,
  IconButton,
  TextField,
  Typography
} from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { onChange } from 'node_modules/react-toastify/dist/core/store'
import { useEffect, useState } from 'react'
import { Controller, useForm } from 'react-hook-form'
import { toast } from 'react-toastify'
import z from 'zod'
import { useCategory } from '../../../context/CategoryContext'

type Props = {
  open: boolean
  setOpen: (open: boolean) => void
  categoryData: CategoryType
  callback?: () => void
  onSubmitAccounts?: (account: QueriesCollectionType['selectedItem']) => void
  selectedAccounts?: QueriesCollectionType['selectedItem']
}

export type QueriesCollectionType = {
  queries: {
    inventory?: string
    expense?: string
    sales?: string
    salesReturn?: string
    salesDiscount?: string
    goodsDelivered?: string
    costOfGoodsSold?: string
    purchaseReturn?: string
    unbilledPurchases?: string
    officeExpense?: string
  }
  selectedItem?: {
    inventory?: AccountType
    expense?: AccountType
    sales?: AccountType
    salesReturn?: AccountType
    salesDiscount?: AccountType
    goodsDelivered?: AccountType
    costOfGoodsSold?: AccountType
    purchaseReturn?: AccountType
    unbilledPurchases?: AccountType
    officeExpense?: AccountType
  }
}

export const accountSchema = z.object({
  inventoryAccountId: z.string().uuid().optional().nullable(),
  expenseAccountId: z.string().uuid().optional().nullable(),
  salesAccountId: z.string().uuid().optional().nullable(),
  salesReturnAccountId: z.string().uuid().optional().nullable(),
  salesDiscountAccountId: z.string().uuid().optional().nullable(),
  goodsShippedAccountId: z.string().uuid().optional().nullable(),
  cogsAccountId: z.string().uuid().optional().nullable(),
  purchaseReturnAccountId: z.string().uuid().optional().nullable(),
  unbilledPurchaseAccountId: z.string().uuid().optional().nullable()
})

type AccountSchmeaType = z.infer<typeof accountSchema>

const BASE_ACCOUNT_PARAMS: AccountParams = {
  limit: Number.MAX_SAFE_INTEGER,
  level: 1
}

const DialogAccountsCategory = ({
  open,
  setOpen,
  categoryData,
  callback = () => {},
  onSubmitAccounts: handleSubmitAccounts,
  selectedAccounts
}: Props) => {
  const { setConfirmState } = useMenu()
  const { accountCategoryData } = useCategory()
  const { control, handleSubmit, reset, getValues } = useForm<AccountSchmeaType>({
    resolver: zodResolver(accountSchema)
  })
  const [
    {
      queries: {
        inventory,
        expense,
        sales,
        salesReturn,
        salesDiscount,
        goodsDelivered,
        costOfGoodsSold,
        purchaseReturn,
        unbilledPurchases
      },
      selectedItem: {
        inventory: selectedInventory,
        expense: selectedExpense,
        sales: selectedSales,
        salesReturn: selectedSalesReturn,
        salesDiscount: selectedSalesDiscount,
        goodsDelivered: selectedGoodsDelivered,
        costOfGoodsSold: selectedCostOfGoodsSold,
        purchaseReturn: selectedPurchaseReturn,
        unbilledPurchases: selectedUnbilledPurchases
      }
    },
    setQueries
  ] = useState<QueriesCollectionType>({
    queries: {
      inventory: '',
      expense: '',
      sales: '',
      salesReturn: '',
      salesDiscount: '',
      goodsDelivered: '',
      costOfGoodsSold: '',
      purchaseReturn: '',
      unbilledPurchases: ''
    },
    selectedItem: {
      inventory: null,
      expense: null,
      sales: null,
      salesReturn: null,
      salesDiscount: null,
      goodsDelivered: null,
      costOfGoodsSold: null,
      purchaseReturn: null,
      unbilledPurchases: null
    }
  })

  // TODO: make query all of them fc*king fields

  const { data: inventoryAccounts, remove: removeAccounts } = useQuery({
    // enabled: !!inventory,
    queryKey: [ACCOUNT_LIST_QUERY_KEY, inventory, 'INVENTORY'],
    queryFn: () =>
      AccountsQueryMethods.getAccountList({
        ...BASE_ACCOUNT_PARAMS,
        accountTypeIds: ['INVENTORY'],
        search: inventory
      })
  })

  const { data: expenseAccounts, remove: removeExpenseAccounts } = useQuery({
    // enabled: !!expense,
    queryKey: [ACCOUNT_LIST_QUERY_KEY, expense, 'EXPENSE'],
    queryFn: () =>
      AccountsQueryMethods.getAccountList({
        ...BASE_ACCOUNT_PARAMS,
        accountTypeIds: ['COGS', 'EXPENSE', 'OTHER_EXPENSE'],
        search: expense
      })
  })

  const { data: revenueAccounts, remove: removeRevenueAccounts } = useQuery({
    // enabled: !!salesDiscount,
    queryKey: [ACCOUNT_LIST_QUERY_KEY, salesDiscount, 'SALES_DISCOUNT'],
    queryFn: () =>
      AccountsQueryMethods.getAccountList({
        ...BASE_ACCOUNT_PARAMS,
        accountTypeIds: ['REVENUE', 'OTHER_REVENUE'],
        search: salesDiscount
      })
  })

  const { data: goodsDeliveredAccounts, remove: removeGoodsDeliveredAccounts } = useQuery({
    // enabled: !!goodsDelivered,
    queryKey: [ACCOUNT_LIST_QUERY_KEY, goodsDelivered, 'GOODS_DELIVERED'],
    queryFn: () =>
      AccountsQueryMethods.getAccountList({
        ...BASE_ACCOUNT_PARAMS,
        accountTypeIds: ['INVENTORY'],
        search: goodsDelivered
      })
  })

  const { data: costOfGoodsSoldAccounts, remove: removeCostOfGoodsSoldAccounts } = useQuery({
    // enabled: !!costOfGoodsSold,
    queryKey: [ACCOUNT_LIST_QUERY_KEY, costOfGoodsSold, 'COST_OF_GOODS_SOLD'],
    queryFn: () =>
      AccountsQueryMethods.getAccountList({
        ...BASE_ACCOUNT_PARAMS,
        accountTypeIds: ['COGS', 'EXPENSE', 'OTHER_EXPENSE'],
        search: costOfGoodsSold
      })
  })

  const { data: purchaseReturnAccounts, remove: removePurchaseReturnAccounts } = useQuery({
    // enabled: !!purchaseReturn,
    queryKey: [ACCOUNT_LIST_QUERY_KEY, purchaseReturn, 'PURCHASE_RETURN'],
    queryFn: () =>
      AccountsQueryMethods.getAccountList({
        ...BASE_ACCOUNT_PARAMS,
        accountTypeIds: ['INVENTORY', 'COGS', 'EXPENSE', 'OTHER_EXPENSE'],
        search: purchaseReturn
      })
  })

  const { data: unbilledPurchasesAccounts, remove: removeUnbilledPurchasesAccounts } = useQuery({
    // enabled: !!unbilledPurchases,
    queryKey: [ACCOUNT_LIST_QUERY_KEY, unbilledPurchases, 'UNBILLED_PURCHASES'],
    queryFn: () =>
      AccountsQueryMethods.getAccountList({
        ...BASE_ACCOUNT_PARAMS,
        accountTypeIds: ['OTHER_CURRENT_LIABILITY'],
        search: unbilledPurchases
      })
  })

  const { mutate: updateCategoryMutate, isLoading } = useUpdateCategory()

  const handleClose = () => {
    if (!isLoading) {
      setOpen(false)
    }
  }

  const onSubmitAccounts = (acc: AccountSchmeaType) => {
    if (!!handleSubmitAccounts) {
      handleSubmitAccounts({
        inventory: selectedInventory,
        expense: selectedExpense,
        sales: selectedSales,
        salesReturn: selectedSalesReturn,
        salesDiscount: selectedSalesDiscount,
        goodsDelivered: selectedGoodsDelivered,
        costOfGoodsSold: selectedCostOfGoodsSold,
        purchaseReturn: selectedPurchaseReturn,
        unbilledPurchases: selectedUnbilledPurchases
      })
    } else {
      updateCategoryMutate(
        {
          id: categoryData?.id,
          code: categoryData?.code,
          name: categoryData?.name,
          type: categoryData?.type,
          parentId: categoryData?.parentId,
          companyId: categoryData?.companyId,
          accounts: {
            inventoryAccountId: acc?.inventoryAccountId,
            expenseAccountId: acc?.expenseAccountId,
            salesAccountId: acc?.salesAccountId,
            salesReturnAccountId: acc?.salesReturnAccountId,
            salesDiscountAccountId: acc?.salesDiscountAccountId,
            goodsShippedAccountId: acc?.goodsShippedAccountId,
            cogsAccountId: acc?.cogsAccountId,
            purchaseReturnAccountId: acc?.purchaseReturnAccountId,
            unbilledPurchaseAccountId: acc?.unbilledPurchaseAccountId
          }
        },
        {
          onSuccess: () => {
            toast.success('Akun berhasil diperbarui')
            callback()
            handleClose()
          }
        }
      )
    }
  }

  useEffect(() => {
    if (accountCategoryData) {
      reset({
        ...getValues(),
        inventoryAccountId: accountCategoryData?.inventoryAccount?.id,
        expenseAccountId: accountCategoryData?.expenseAccount?.id,
        salesAccountId: accountCategoryData?.salesAccount?.id,
        salesReturnAccountId: accountCategoryData?.salesReturnAccount?.id,
        salesDiscountAccountId: accountCategoryData?.salesDiscountAccount?.id,
        goodsShippedAccountId: accountCategoryData?.goodsShippedAccount?.id,
        cogsAccountId: accountCategoryData?.cogsAccount?.id,
        purchaseReturnAccountId: accountCategoryData?.purchaseReturnAccount?.id,
        unbilledPurchaseAccountId: accountCategoryData?.unbilledPurchaseAccount?.id
      })
      setQueries(curr => ({
        ...curr,
        selectedItem: {
          ...curr.selectedItem,
          inventory: accountCategoryData?.inventoryAccount,
          expense: accountCategoryData?.expenseAccount,
          sales: accountCategoryData?.salesAccount,
          salesReturn: accountCategoryData?.salesReturnAccount,
          salesDiscount: accountCategoryData?.salesDiscountAccount,
          goodsDelivered: accountCategoryData?.goodsShippedAccount,
          costOfGoodsSold: accountCategoryData?.cogsAccount,
          purchaseReturn: accountCategoryData?.purchaseReturnAccount,
          unbilledPurchases: accountCategoryData?.unbilledPurchaseAccount
        }
      }))
    }
  }, [open, accountCategoryData])

  useEffect(() => {
    if (selectedAccounts) {
      reset({
        ...getValues(),
        inventoryAccountId: selectedAccounts?.inventory?.id,
        expenseAccountId: selectedAccounts?.expense?.id,
        salesAccountId: selectedAccounts?.sales?.id,
        salesReturnAccountId: selectedAccounts?.salesReturn?.id,
        salesDiscountAccountId: selectedAccounts?.salesDiscount?.id,
        goodsShippedAccountId: selectedAccounts?.goodsDelivered?.id,
        cogsAccountId: selectedAccounts?.costOfGoodsSold?.id,
        purchaseReturnAccountId: selectedAccounts?.purchaseReturn?.id,
        unbilledPurchaseAccountId: selectedAccounts?.unbilledPurchases?.id
      })
      setQueries(curr => ({
        ...curr,
        selectedItem: {
          ...curr.selectedItem,
          inventory: selectedAccounts?.inventory,
          expense: selectedAccounts?.expense,
          sales: selectedAccounts?.sales,
          salesReturn: selectedAccounts?.salesReturn,
          salesDiscount: selectedAccounts?.salesDiscount,
          goodsDelivered: selectedAccounts?.goodsDelivered,
          costOfGoodsSold: selectedAccounts?.costOfGoodsSold,
          purchaseReturn: selectedAccounts?.purchaseReturn,
          unbilledPurchases: selectedAccounts?.unbilledPurchases
        }
      }))
    }
  }, [selectedAccounts])

  return (
    <Dialog fullWidth maxWidth='sm' scroll='body' open={open} onClose={handleClose}>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-16'>
        Edit Akun Perkiraan Kategori
        <Typography component='span' className='flex flex-col text-center'>
          Ubah kode akun perkiraan yang digunakan di kategori ini
        </Typography>
      </DialogTitle>
      <form onSubmit={e => e.preventDefault()}>
        <DialogContent className='overflow-visible pbs-0 sm:pbe-6 sm:px-16'>
          <IconButton onClick={handleClose} className='absolute block-start-4 inline-end-4'>
            <i className='ri-close-line text-textSecondary' />
          </IconButton>
          <Grid
            container
            spacing={4}
            sx={{
              maxHeight: '50vh',
              overflow: 'auto',
              padding: '24px'
            }}
          >
            <Grid item xs={12}>
              {/* Akun Persediaan */}
              <Controller
                control={control}
                name='inventoryAccountId'
                render={({ field: { onChange, value } }) => (
                  <Autocomplete
                    value={selectedInventory}
                    onInputChange={debounce((e, newValue, reason) => {
                      if (reason === 'input') {
                        setQueries(curr => ({ ...curr, queries: { ...curr.queries, inventory: newValue as string } }))
                      }
                    }, 700)}
                    options={inventoryAccounts?.items ?? []}
                    getOptionLabel={(option: AccountType) => `[${option.code}] ${option.name}`}
                    freeSolo={!inventory}
                    noOptionsText='Akun tidak ditemukan'
                    onChange={(e, newValue: AccountType) => {
                      if (newValue) {
                        onChange(newValue.id)
                        setQueries(curr => ({ ...curr, selectedItem: { ...curr.selectedItem, inventory: newValue } }))
                        removeAccounts()
                      }
                    }}
                    renderInput={params => (
                      <TextField
                        {...params}
                        InputProps={{
                          ...params.InputProps,
                          onKeyDown: e => {
                            if (e.key === 'Enter') {
                              e.stopPropagation()
                            }
                          }
                        }}
                        placeholder='Cari akun perkiraan'
                        label='Persediaan'
                      />
                    )}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12}>
              {/* Akun Beban */}
              <Controller
                control={control}
                name='expenseAccountId'
                render={({ field: { onChange, value } }) => (
                  <Autocomplete
                    value={selectedExpense}
                    onInputChange={debounce((e, newValue, reason) => {
                      if (reason === 'input') {
                        setQueries(curr => ({ ...curr, queries: { ...curr.queries, expense: newValue as string } }))
                      }
                    }, 700)}
                    options={expenseAccounts?.items ?? []}
                    getOptionLabel={(option: AccountType) => `[${option.code}] ${option.name}`}
                    freeSolo={!expense}
                    noOptionsText='Akun tidak ditemukan'
                    onChange={(e, newValue: AccountType) => {
                      if (newValue) {
                        setQueries(curr => ({ ...curr, selectedItem: { ...curr.selectedItem, expense: newValue } }))
                        onChange(newValue.id)
                        removeExpenseAccounts()
                      }
                    }}
                    renderInput={params => (
                      <TextField
                        {...params}
                        InputProps={{
                          ...params.InputProps,
                          onKeyDown: e => {
                            if (e.key === 'Enter') {
                              e.stopPropagation()
                            }
                          }
                        }}
                        placeholder='Cari akun perkiraan'
                        label='Beban'
                      />
                    )}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12}>
              {/* Akun Penjualan */}
              <Controller
                control={control}
                name='salesAccountId'
                render={({ field: { onChange, value } }) => (
                  <Autocomplete
                    value={selectedSales}
                    onInputChange={debounce((e, newValue, reason) => {
                      if (reason === 'input') {
                        setQueries(curr => ({ ...curr, queries: { ...curr.queries, sales: newValue as string } }))
                      }
                    }, 700)}
                    options={revenueAccounts?.items ?? []}
                    getOptionLabel={(option: AccountType) => `[${option.code}] ${option.name}`}
                    freeSolo={!sales}
                    noOptionsText='Akun tidak ditemukan'
                    onChange={(e, newValue: AccountType) => {
                      if (newValue) {
                        setQueries(curr => ({ ...curr, selectedItem: { ...curr.selectedItem, sales: newValue } }))
                        onChange(newValue.id)
                        removeRevenueAccounts()
                      }
                    }}
                    renderInput={params => (
                      <TextField
                        {...params}
                        InputProps={{
                          ...params.InputProps,
                          onKeyDown: e => {
                            if (e.key === 'Enter') {
                              e.stopPropagation()
                            }
                          }
                        }}
                        placeholder='Cari akun perkiraan'
                        label='Penjualan'
                      />
                    )}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12}>
              {/* Akun Retur Penjualan */}
              <Controller
                control={control}
                name='salesReturnAccountId'
                render={({ field: { onChange, value } }) => (
                  <Autocomplete
                    value={selectedSalesReturn}
                    onInputChange={debounce((e, newValue, reason) => {
                      if (reason === 'input') {
                        setQueries(curr => ({ ...curr, queries: { ...curr.queries, salesReturn: newValue as string } }))
                      }
                    }, 700)}
                    options={revenueAccounts?.items ?? []}
                    getOptionLabel={(option: AccountType) => `[${option.code}] ${option.name}`}
                    freeSolo={!salesReturn}
                    noOptionsText='Akun tidak ditemukan'
                    onChange={(e, newValue: AccountType) => {
                      if (newValue) {
                        setQueries(curr => ({ ...curr, selectedItem: { ...curr.selectedItem, salesReturn: newValue } }))
                        onChange(newValue.id)
                        removeRevenueAccounts()
                      }
                    }}
                    renderInput={params => (
                      <TextField
                        {...params}
                        InputProps={{
                          ...params.InputProps,
                          onKeyDown: e => {
                            if (e.key === 'Enter') {
                              e.stopPropagation()
                            }
                          }
                        }}
                        placeholder='Cari akun perkiraan'
                        label='Retur Penjualan'
                      />
                    )}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12}>
              {/* Akun Discount */}
              <Controller
                control={control}
                name='salesDiscountAccountId'
                render={({ field: { onChange, value } }) => (
                  <Autocomplete
                    value={selectedSalesDiscount}
                    onInputChange={debounce((e, newValue, reason) => {
                      if (reason === 'input') {
                        setQueries(curr => ({
                          ...curr,
                          queries: { ...curr.queries, salesDiscount: newValue as string }
                        }))
                      }
                    }, 700)}
                    options={revenueAccounts?.items ?? []}
                    getOptionLabel={(option: AccountType) => `[${option.code}] ${option.name}`}
                    freeSolo={!salesDiscount}
                    noOptionsText='Akun tidak ditemukan'
                    onChange={(e, newValue: AccountType) => {
                      if (newValue) {
                        setQueries(curr => ({
                          ...curr,
                          selectedItem: { ...curr.selectedItem, salesDiscount: newValue }
                        }))
                        onChange(newValue.id)
                        removeRevenueAccounts()
                      }
                    }}
                    renderInput={params => (
                      <TextField
                        {...params}
                        InputProps={{
                          ...params.InputProps,
                          onKeyDown: e => {
                            if (e.key === 'Enter') {
                              e.stopPropagation()
                            }
                          }
                        }}
                        placeholder='Cari akun perkiraan'
                        label='Diskon Penjualan'
                      />
                    )}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12}>
              {/* Akun Goods Delivered */}
              <Controller
                control={control}
                name='goodsShippedAccountId'
                render={({ field: { onChange, value } }) => (
                  <Autocomplete
                    value={selectedGoodsDelivered}
                    onInputChange={debounce((e, newValue, reason) => {
                      if (reason === 'input') {
                        setQueries(curr => ({
                          ...curr,
                          queries: { ...curr.queries, goodsDelivered: newValue as string }
                        }))
                      }
                    }, 700)}
                    options={goodsDeliveredAccounts?.items ?? []}
                    getOptionLabel={(option: AccountType) => `[${option.code}] ${option.name}`}
                    freeSolo={!goodsDelivered}
                    noOptionsText='Akun tidak ditemukan'
                    onChange={(e, newValue: AccountType) => {
                      if (newValue) {
                        setQueries(curr => ({
                          ...curr,
                          selectedItem: { ...curr.selectedItem, goodsDelivered: newValue }
                        }))
                        onChange(newValue.id)
                        removeGoodsDeliveredAccounts()
                      }
                    }}
                    renderInput={params => (
                      <TextField
                        {...params}
                        InputProps={{
                          ...params.InputProps,
                          onKeyDown: e => {
                            if (e.key === 'Enter') {
                              e.stopPropagation()
                            }
                          }
                        }}
                        placeholder='Cari akun perkiraan'
                        label='Barang Terkirim'
                      />
                    )}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12}>
              {/* Akun Harga Pokok Penjualan / COGS */}
              <Controller
                control={control}
                name='cogsAccountId'
                render={({ field: { onChange, value } }) => (
                  <Autocomplete
                    value={selectedCostOfGoodsSold}
                    onInputChange={debounce((e, newValue, reason) => {
                      if (reason === 'input') {
                        setQueries(curr => ({
                          ...curr,
                          queries: { ...curr.queries, costOfGoodsSold: newValue as string }
                        }))
                      }
                    }, 700)}
                    options={costOfGoodsSoldAccounts?.items ?? []}
                    getOptionLabel={(option: AccountType) => `[${option.code}] ${option.name}`}
                    freeSolo={!costOfGoodsSold}
                    noOptionsText='Akun tidak ditemukan'
                    onChange={(e, newValue: AccountType) => {
                      if (newValue) {
                        setQueries(curr => ({
                          ...curr,
                          selectedItem: { ...curr.selectedItem, costOfGoodsSold: newValue }
                        }))
                        onChange(newValue.id)
                        removeCostOfGoodsSoldAccounts()
                      }
                    }}
                    renderInput={params => (
                      <TextField
                        {...params}
                        InputProps={{
                          ...params.InputProps,
                          onKeyDown: e => {
                            if (e.key === 'Enter') {
                              e.stopPropagation()
                            }
                          }
                        }}
                        placeholder='Cari akun perkiraan'
                        label='Harga pokok penjualan'
                      />
                    )}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12}>
              {/* Akun Retur Pembelian */}
              <Controller
                control={control}
                name='purchaseReturnAccountId'
                render={({ field: { onChange, value } }) => (
                  <Autocomplete
                    value={selectedPurchaseReturn}
                    onInputChange={debounce((e, newValue, reason) => {
                      if (reason === 'input') {
                        setQueries(curr => ({
                          ...curr,
                          queries: { ...curr.queries, purchaseReturn: newValue as string }
                        }))
                      }
                    }, 700)}
                    options={purchaseReturnAccounts?.items ?? []}
                    getOptionLabel={(option: AccountType) => `[${option.code}] ${option.name}`}
                    freeSolo={!purchaseReturn}
                    noOptionsText='Akun tidak ditemukan'
                    onChange={(e, newValue: AccountType) => {
                      if (newValue) {
                        setQueries(curr => ({
                          ...curr,
                          selectedItem: { ...curr.selectedItem, purchaseReturn: newValue }
                        }))
                        onChange(newValue.id)
                        removePurchaseReturnAccounts()
                      }
                    }}
                    renderInput={params => (
                      <TextField
                        {...params}
                        InputProps={{
                          ...params.InputProps,
                          onKeyDown: e => {
                            if (e.key === 'Enter') {
                              e.stopPropagation()
                            }
                          }
                        }}
                        placeholder='Cari akun perkiraan'
                        label='Retur Pembelian'
                      />
                    )}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12}>
              {/* Akun Pembelian Belum Tertagih */}
              <Controller
                control={control}
                name='unbilledPurchaseAccountId'
                render={({ field: { onChange, value } }) => (
                  <Autocomplete
                    value={selectedUnbilledPurchases}
                    onInputChange={debounce((e, newValue, reason) => {
                      if (reason === 'input') {
                        setQueries(curr => ({
                          ...curr,
                          queries: { ...curr.queries, unbilledPurchases: newValue as string }
                        }))
                      }
                    }, 700)}
                    options={unbilledPurchasesAccounts?.items ?? []}
                    getOptionLabel={(option: AccountType) => `[${option.code}] ${option.name}`}
                    freeSolo={!unbilledPurchases}
                    noOptionsText='Akun tidak ditemukan'
                    onChange={(e, newValue: AccountType) => {
                      if (newValue) {
                        setQueries(curr => ({
                          ...curr,
                          selectedItem: { ...curr.selectedItem, unbilledPurchases: newValue }
                        }))
                        onChange(newValue.id)
                        removeUnbilledPurchasesAccounts()
                      }
                    }}
                    renderInput={params => (
                      <TextField
                        {...params}
                        InputProps={{
                          ...params.InputProps,
                          onKeyDown: e => {
                            if (e.key === 'Enter') {
                              e.stopPropagation()
                            }
                          }
                        }}
                        placeholder='Cari akun perkiraan'
                        label='Pembelian Belum Tertagih'
                      />
                    )}
                  />
                )}
              />
            </Grid>
          </Grid>
        </DialogContent>
      </form>
      <DialogActions className='justify-center pbs-0 sm:pbe-16 sm:px-16'>
        <Button disabled={isLoading} variant='outlined' onClick={handleClose}>
          Batalkan
        </Button>
        <LoadingButton loading={isLoading} variant='contained' color='primary' onClick={handleSubmit(onSubmitAccounts)}>
          Simpan
        </LoadingButton>
      </DialogActions>
    </Dialog>
  )
}

export default DialogAccountsCategory
