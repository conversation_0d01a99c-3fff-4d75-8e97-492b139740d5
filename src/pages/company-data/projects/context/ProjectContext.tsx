import { defaultListData } from '@/api/queryClient'
import { useDeleteProject, useFinishProject } from '@/api/services/company/mutation'
import CompanyQueryMethods, { PROJECT_LIST_QUERY_KEY, PROJECT_QUERY_KEY } from '@/api/services/company/query'
import AddProjectDialog from '@/components/dialogs/add-project-dialog'
import useMobileScreen from '@/components/dialogs/hooks/useMobileScreen'
import { useMenu } from '@/components/menu/contexts/menuContext'
import usePartialState from '@/core/hooks/usePartialState'
import { useRouter } from '@/routes/hooks'
import { ListResponse } from '@/types/api'
import { ProjectParams, ProjectType } from '@/types/projectTypes'
import { QueryObserverResult, RefetchOptions, RefetchQueryFilters, useQuery } from '@tanstack/react-query'
import * as React from 'react'
import { useParams } from 'react-router-dom'
import { toast } from 'react-toastify'

type ProjectContextProps = {
  isMobile: boolean
  projectParams: ProjectParams
  setPartialProjectParams: (fieldName: keyof ProjectParams, value: any) => void
  setProjectParams: React.Dispatch<React.SetStateAction<ProjectParams>>
  projectsListResponse: ListResponse<ProjectType>
  selectedProjectId: string | undefined
  setSelectedProjectId: React.Dispatch<React.SetStateAction<string | undefined>>
  setProjectDialogState: React.Dispatch<React.SetStateAction<{ openDialog: boolean; dialogData: ProjectType | null }>>
  fetchProjectList: <TPageData>(
    options?: RefetchOptions & RefetchQueryFilters<TPageData>
  ) => Promise<QueryObserverResult<ListResponse<ProjectType>, unknown>>
  fetchProjectData: <TPageData>(
    options?: RefetchOptions & RefetchQueryFilters<TPageData>
  ) => Promise<QueryObserverResult<ProjectType, unknown>>
  projectData: ProjectType
  handleDeleteProject: (id: string) => void
  handleFinishProject: (id: string) => void
}

const ProjectContext = React.createContext<ProjectContextProps>({} as ProjectContextProps)

export const useProject = () => {
  const context = React.useContext(ProjectContext)
  if (context === undefined) {
    throw new Error('useProject must be used within a ProjectContextProvider')
  }
  return context
}

export const ProjectContextProvider = ({ children }: { children: React.ReactNode }) => {
  const { isMobile } = useMobileScreen()
  const { projectId } = useParams()
  const { setConfirmState } = useMenu()
  const router = useRouter()

  const [projectParams, setPartialProjectParams, setProjectParams] = usePartialState<ProjectParams>({
    limit: 10,
    page: 1
  })
  const [selectedProjectId, setSelectedProjectId] = React.useState<string | undefined>(undefined)
  const [{ openDialog, dialogData }, setProjectDialogState] = React.useState<{
    openDialog: boolean
    dialogData: ProjectType | null
  }>({
    dialogData: null,
    openDialog: false
  })

  const { mutate: deleteMutate } = useDeleteProject()
  const { mutate: finishMutate } = useFinishProject()

  const { data: projectsListResponse, refetch: fetchProjectList } = useQuery({
    queryKey: [PROJECT_LIST_QUERY_KEY, JSON.stringify(projectParams)],
    queryFn: () => {
      return CompanyQueryMethods.getProjectList(projectParams)
    },
    placeholderData: defaultListData as ListResponse<ProjectType>
  })

  const { data: projectData, refetch: fetchProjectData } = useQuery({
    enabled: !!projectId,
    queryKey: [PROJECT_QUERY_KEY, projectId],
    queryFn: () => CompanyQueryMethods.getProject(projectId)
  })

  const handleDeleteProject = (id: string) => {
    setConfirmState({
      open: true,
      title: 'Hapus Proyek',
      content: 'Apakah Anda yakin ingin menghapus proyek ini? Action ini tidak dapat dikembalikan',
      confirmText: 'Hapus',
      confirmColor: 'error',
      onConfirm: () => {
        deleteMutate(id, {
          onSuccess: () => {
            toast.success('Proyek berhasil dihapus')
            router.push('/company-data/projects')
            fetchProjectList()
          }
        })
      }
    })
  }

  const handleFinishProject = (id: string) => {
    setConfirmState({
      open: true,
      title: 'Selesaikan Proyek',
      content: 'Apakah Anda yakin ingin mengselesaikan proyek ini? Action ini tidak dapat dikembalikan',
      confirmText: 'Selesaikan',
      onConfirm: () => {
        finishMutate(id, {
          onSuccess: () => {
            toast.success('Proyek berhasil diselesaikan')
            fetchProjectData()
            fetchProjectList()
          }
        })
      }
    })
  }

  React.useEffect(() => {
    if (projectId) {
      setSelectedProjectId(projectId)
    }
  }, [projectId])

  const value = {
    isMobile,
    projectParams,
    setPartialProjectParams,
    setProjectParams,
    projectsListResponse,
    fetchProjectList,
    fetchProjectData,
    selectedProjectId,
    setSelectedProjectId,
    setProjectDialogState,
    projectData,
    handleDeleteProject,
    handleFinishProject
  }
  return (
    <ProjectContext.Provider value={value}>
      {openDialog && (
        <AddProjectDialog
          open={openDialog}
          setOpen={isOpen => {
            if (!isOpen) {
              setProjectDialogState({ openDialog: false, dialogData: null })
            }
          }}
          projectData={dialogData}
          fetchProjectList={fetchProjectList}
          fetchProjectData={fetchProjectData}
        />
      )}
      {children}
    </ProjectContext.Provider>
  )
}
