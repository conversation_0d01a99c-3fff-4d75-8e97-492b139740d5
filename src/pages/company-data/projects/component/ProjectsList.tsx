import { MouseEvent, useEffect, useMemo, useState } from 'react'
import { Box, IconButton, Menu, MenuItem, Typography } from '@mui/material'
import Card from '@mui/material/Card'
import Button from '@mui/material/Button'

import {
  getCoreRowModel,
  useReactTable,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFacetedMinMaxValues,
  getPaginationRowModel,
  getSortedRowModel
} from '@tanstack/react-table'

import DebouncedInput from '@/components/DebounceInput'
import Table from '@/components/table'
import { useRouter } from '@/routes/hooks'
import MobileDropDown from '@/components/layout/shared/components/MobileDropDown'
import { useProject } from '../context/ProjectContext'
import { tableColumns } from '../config/table'

const ProjectsList = () => {
  const router = useRouter()
  const {
    isMobile,
    projectParams,
    setProjectParams,
    setPartialProjectParams,
    projectsListResponse,
    setSelectedProjectId,
    setProjectDialogState
  } = useProject()

  const { items, totalItems, totalPages, page: responsePage, limit: responseLimit } = projectsListResponse
  const { search, page, limit } = projectParams

  const [addAnchorEl, setAddAnchorEl] = useState<null | HTMLElement>(null)
  const open = Boolean(addAnchorEl)

  const [actionBtn, setBtn] = useState<boolean>(false)
  const [searchExtend, setSearchExtend] = useState<boolean>(false)

  const tableOptions = useMemo(
    () => ({
      data: items,
      columns: tableColumns({
        detail: row => router.push(`/company-data/projects/${row.id}`)
      }),
      initialState: {
        pagination: {
          pageSize: limit ?? 10,
          pageIndex: page - 1
        }
      },
      state: {
        pagination: {
          pageSize: responseLimit,
          pageIndex: responsePage - 1
        }
      },
      manualPagination: true,
      rowCount: totalItems,
      pageCount: totalPages,
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [projectsListResponse, projectParams]
  )

  // TODO: MOVE THIS SHIT
  const table = useReactTable<any>(tableOptions)

  const handleAddClick = (event: MouseEvent<HTMLButtonElement>) => {
    setProjectDialogState(prev => ({ ...prev, openDialog: true }))
    // setAddAnchorEl(event.currentTarget)
  }

  const handleAddClose = () => {
    setAddAnchorEl(null)
  }

  useEffect(() => {
    setProjectParams({
      limit: 10,
      page: 1
    })
    setSelectedProjectId(undefined)
  }, [])

  const actionButton = (
    <></>
    // <Button
    //   color='secondary'
    //   variant='outlined'
    //   startIcon={<i className='ri-upload-2-line' />}
    //   className='is-full sm:is-auto'
    // >
    //   Ekspor
    // </Button>
  )

  return (
    <>
      <Card>
        <div className='flex justify-between gap-4 p-5 flex-row items-start sm:flex-row sm:items-center'>
          {searchExtend ? (
            <div className='flex gap-4 items-center is-full flex-col sm:flex-row'>
              <DebouncedInput
                value={search}
                onChange={value => setProjectParams(prev => ({ ...prev, page: 1, search: value as string }))}
                onBlur={() => setSearchExtend(false)}
                placeholder='Cari'
                className='is-full'
              />
            </div>
          ) : !isMobile ? (
            <div className='flex gap-4 items-center is-full sm:is-auto flex-col sm:flex-row'>
              <DebouncedInput
                value={search}
                onChange={value => setProjectParams(prev => ({ ...prev, page: 1, search: value as string }))}
                placeholder='Cari'
                className='is-full sm:is-auto'
              />
            </div>
          ) : (
            <IconButton onClick={() => setSearchExtend(true)} className='bg-[#4C4E640D]/5 rounded-[8px]'>
              <i className='ri-search-line' />
            </IconButton>
          )}
          {!searchExtend && (
            <div className='flex items-center justify-end md:justify-between gap-x-4 max-sm:gap-y-4 is-full flex-row sm:is-auto'>
              {/* {!isMobile ? (
                actionButton
              ) : (
                <IconButton onClick={() => setBtn(true)} className='bg-[#4C4E640D]/5 rounded-[8px]'>
                  <i className='pepicons-pop--dots-y' />
                </IconButton>
              )} */}
              <Button
                variant='contained'
                aria-haspopup='true'
                onClick={handleAddClick}
                aria-expanded={open ? 'true' : undefined}
                startIcon={<i className='ri-add-circle-line' />}
                aria-controls={open ? 'user-view-overview-export' : undefined}
                className='is-auto sm:is-auto'
              >
                {isMobile ? 'Tambah' : 'Tambah Proyek'}
              </Button>
              {isMobile ? (
                <MobileDropDown
                  className='z-10'
                  open={open}
                  onOpen={() => setAddAnchorEl(true as any)}
                  onClose={() => setAddAnchorEl(null)}
                >
                  <Typography sx={{ marginTop: 2 }} align='center' variant='h5'>
                    Action
                  </Typography>
                  <Box className='flex gap-2 p-4 pb-2 flex-col'>
                    {/* <Button
                      variant='outlined'
                      startIcon={<i className='mdi-file-document-outline size-5' />}
                      onClick={() => {
                        handleAddClose()
                      }}
                    >
                      Impor dari Excel
                    </Button> */}
                    <Button
                      variant='contained'
                      startIcon={<i className='ic-baseline-add-circle-outline size-5' />}
                      onClick={() => {
                        // setAddSiteOpen(true)
                        handleAddClose()
                      }}
                    >
                      Tambah Manual
                    </Button>
                  </Box>
                </MobileDropDown>
              ) : (
                <Menu open={open} anchorEl={addAnchorEl} onClose={handleAddClose} id='user-view-overview-export'>
                  {/* <MenuItem onClick={handleAddClose} className='flex h-12'>
                    <i className='mdi-file-document-outline size-5' />
                    Impor dari Excel
                  </MenuItem> */}
                  <MenuItem
                    onClick={() => {
                      //   setAddSiteOpen(true)
                      handleAddClose()
                    }}
                    className='flex h-12'
                  >
                    <i className='ic-baseline-add-circle-outline size-5' />
                    Tambah Manual
                  </MenuItem>
                </Menu>
              )}
            </div>
          )}
        </div>
        <Table
          table={table}
          emptyLabel={
            <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
              <Typography> Belum ada Proyek</Typography>
              <Typography className='text-sm text-gray-400'>
                Semua proyek di perusahaan kamu akan ditampilkan di sini
              </Typography>
            </td>
          }
          onRowsPerPageChange={pageSize => {
            if (pageSize > totalItems) {
              setProjectParams(prev => ({ ...prev, limit: totalItems, page: 1 }))
            } else {
              setPartialProjectParams('limit', pageSize)

              const maxPage = Math.ceil(totalItems / pageSize)
              if (page > maxPage) {
                setProjectParams(prev => ({ ...prev, page: maxPage }))
              }
            }
          }}
          onPageChange={pageIndex => setPartialProjectParams('page', pageIndex)}
        />
      </Card>
      <MobileDropDown className='z-1' open={actionBtn} onClose={() => setBtn(false)} onOpen={() => setBtn(true)}>
        <Typography sx={{ marginTop: 2 }} align='center' variant='h5'>
          Action
        </Typography>
        <Box className='flex gap-2 p-4 pb-2 flex-col'>{actionButton}</Box>
      </MobileDropDown>
    </>
  )
}

export default ProjectsList
