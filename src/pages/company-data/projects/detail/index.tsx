// MUI Imports

import Grid from '@mui/material/Grid'

// Type Imports
import { Breadcrumbs, Button, Chip, Typography } from '@mui/material'

import { Link } from 'react-router-dom'
import { useProject } from '../context/ProjectContext'
import ProjectLocationCard from './component/LocationDetailCard'
import CreatedByCard from './component/CreatedByCard'
import ReceivedCard from './component/ReceivedCard'
import RealizationCard from './component/RealizationCard'
import RequirementsCard from './component/RequirementCard'
import { ProjectStatus } from '@/types/projectTypes'
import { projectStatusConfig } from '../config/utils'
import ProjectLabelCard from './component/ProjectLabelCard'

const ProjectDetailPage = () => {
  const { projectData, selectedProjectId, handleDeleteProject, handleFinishProject, setProjectDialogState } =
    useProject()

  return (
    <>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <Breadcrumbs aria-label='breadcrumb'>
            <Link to='/company-data/projects' replace>
              <Typography color='var(--mui-palette-text-disabled)'>Manajemen Proyek</Typography>
            </Link>
            <Typography>Detil Proyek</Typography>
          </Breadcrumbs>
        </Grid>
        <Grid item xs={12}>
          <div className='flex justify-between items-end flex-col sm:flex-row max-sm:items-center gap-2'>
            <div className='flex flex-col'>
              <div className='flex items-center gap-2 flex-col sm:flex-row max-sm:text-center'>
                <Typography variant='h4'>{projectData?.name}</Typography>
                <Chip
                  label={projectStatusConfig[projectData?.status]?.label}
                  color={projectStatusConfig[projectData?.status]?.color as any}
                  variant='tonal'
                  size='small'
                />
              </div>
              <Typography className='max-sm:text-center max-sm:mt-2'>
                Kode: <span className='font-semibold'>{projectData?.code}</span>
              </Typography>
            </div>
            <div className='flex gap-2 flex-col sm:flex-row is-full sm:is-auto'>
              {/* <Button
                color='secondary'
                variant='outlined'
                startIcon={<i className='ri-upload-2-line' />}
                className='is-full sm:is-auto'
                onClick={handleExport}
              >
                Ekspor
              </Button>
              <Button
                color='secondary'
                variant='outlined'
                startIcon={<i className='ic-outline-local-printshop' />}
                className='is-full sm:is-auto'
                onClick={handlePrint}
              >
                Cetak
              </Button> */}
              {projectData?.status !== ProjectStatus.FINISHED && (
                <>
                  <Button
                    variant='outlined'
                    color={'error'}
                    className='is-full sm:is-auto'
                    onClick={() => handleDeleteProject(selectedProjectId)}
                  >
                    Hapus Proyek
                  </Button>
                  <Button
                    variant='outlined'
                    color={'secondary'}
                    className='is-full sm:is-auto'
                    onClick={() =>
                      setProjectDialogState(prev => ({ ...prev, openDialog: true, dialogData: projectData }))
                    }
                  >
                    Edit Proyek
                  </Button>
                  {/* <Button
                    variant='outlined'
                    // disabled={!isCanDoRma}
                    color={'primary'}
                    className='is-full sm:is-auto'
                    onClick={() => handleFinishProject(selectedProjectId)}
                  >
                    Selesaikan Proyek
                  </Button> */}
                </>
              )}
            </div>
          </div>
        </Grid>
        <Grid item xs={12} md={6}>
          <Grid container spacing={4}>
            <Grid item xs={12}>
              <ProjectLabelCard projectData={projectData} />
            </Grid>
            <Grid item xs={12}>
              <ProjectLocationCard projectData={projectData} />
            </Grid>
            {/* <Grid item xs={12}>
              <ReceivedCard projectData={projectData} />
            </Grid> */}
          </Grid>
        </Grid>
        <Grid item xs={12} md={6}>
          <Grid container spacing={4}>
            {/* <Grid item xs={12}>
              <RequirementsCard projectData={projectData} />
            </Grid>
            <Grid item xs={12}>
              <RealizationCard projectData={projectData} />
            </Grid> */}
            <Grid item xs={12}>
              <CreatedByCard createdBy={projectData?.createdByUser} data={projectData} />
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </>
  )
}

export default ProjectDetailPage
