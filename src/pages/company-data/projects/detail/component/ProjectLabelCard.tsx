import CompanyQueryMethods, { PROJECT_LABEL_LIST_QUERY_KEY } from '@/api/services/company/query'
import { useCreateProjectLabel, useDeleteProjectLabel, useUpdateProjectLabel } from '@/api/services/company/mutation'
import { ProjectLabelType, ProjectType } from '@/types/projectTypes'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { Card, CardContent, Grid, Typography, IconButton, TextField, Button, CircularProgress } from '@mui/material'
import { useState } from 'react'
import { useMenu } from '@/components/menu/contexts/menuContext'
import { toast } from 'react-toastify'
import { defaultListData } from '@/api/queryClient'
import { ListResponse } from '@/types/api'

type LabelItemProps = {
  label: ProjectLabelType
  projectId: string
}

const LabelItem = ({ label, projectId }: LabelItemProps) => {
  const queryClient = useQueryClient()
  const { setConfirmState } = useMenu()
  const [isEdit, setIsEdit] = useState(false)
  const [editedLabel, setEditedLabel] = useState({ name: label.name, description: label.description })

  const { mutate: updateLabelMutate, isLoading: isUpdateLoading } = useUpdateProjectLabel()
  const { mutate: deleteLabelMutate, isLoading: isDeleteLoading } = useDeleteProjectLabel()

  const handleUpdate = () => {
    if (!editedLabel.name || !editedLabel.description) {
      toast.error('Nama dan deskripsi label tidak boleh kosong.')
      return
    }
    updateLabelMutate(
      {
        projectId,
        labelId: label.id,
        ...editedLabel
      },
      {
        onSuccess: () => {
          toast.success('Label berhasil diubah')
          queryClient.invalidateQueries([PROJECT_LABEL_LIST_QUERY_KEY, projectId])
          setIsEdit(false)
        },
        onError: () => {
          toast.error('Gagal mengubah label')
        }
      }
    )
  }

  const handleDelete = () => {
    setConfirmState({
      open: true,
      title: 'Hapus Label Proyek',
      content: `Apakah Anda yakin ingin menghapus label "${label.name}"? Aksi ini tidak dapat dibatalkan.`,
      confirmText: 'Hapus',
      confirmColor: 'error',
      onConfirm: () => {
        deleteLabelMutate(
          { projectId, labelId: label.id },
          {
            onSuccess: () => {
              toast.success('Label berhasil dihapus')
              queryClient.invalidateQueries([PROJECT_LABEL_LIST_QUERY_KEY, projectId])
            },
            onError: () => {
              toast.error('Gagal menghapus label')
            }
          }
        )
      }
    })
  }

  if (isEdit) {
    return (
      <Grid container spacing={2} alignItems='center' className='bg-gray-100 px-4 py-3 rounded-lg'>
        <Grid item xs={12}>
          <TextField
            fullWidth
            label='Nama Label'
            value={editedLabel.name}
            onChange={e => setEditedLabel(prev => ({ ...prev, name: e.target.value }))}
            size='small'
            InputProps={{ className: 'bg-white' }}
          />
        </Grid>
        <Grid item xs={12}>
          <TextField
            fullWidth
            label='Deskripsi'
            value={editedLabel.description}
            onChange={e => setEditedLabel(prev => ({ ...prev, description: e.target.value }))}
            size='small'
            InputProps={{ className: 'bg-white' }}
          />
        </Grid>
        <Grid item xs={12} sm={4} className='flex gap-2'>
          <Button onClick={handleUpdate} disabled={isUpdateLoading} variant='contained' size='small'>
            {isUpdateLoading ? <CircularProgress size={20} color='inherit' /> : 'Simpan'}
          </Button>
          <Button onClick={() => setIsEdit(false)} variant='outlined' size='small' color='secondary'>
            Batal
          </Button>
        </Grid>
      </Grid>
    )
  }

  return (
    <div className='bg-gray-100 px-4 py-2 flex justify-between rounded-lg items-start'>
      <div className='flex flex-col gap-1 flex-1'>
        <Typography className='text-textPrimary text-base'>{label.name ?? '-'}</Typography>
        <Typography variant='caption'>{label.description ?? '-'}</Typography>
      </div>
      <div className='flex'>
        <IconButton size='small' onClick={() => setIsEdit(true)}>
          <i className='ic-baseline-edit size-5' />
        </IconButton>
        <IconButton size='small' onClick={handleDelete} disabled={isDeleteLoading}>
          {isDeleteLoading ? <CircularProgress size={20} /> : <i className='ri-delete-bin-line size-5 text-red-500' />}
        </IconButton>
      </div>
    </div>
  )
}

type AddLabelFormProps = {
  projectId: string
  onCancel: () => void
}

const AddLabelForm = ({ projectId, onCancel }: AddLabelFormProps) => {
  const queryClient = useQueryClient()
  const [newLabel, setNewLabel] = useState({ name: '', description: '' })
  const { mutate: createLabelMutate, isLoading } = useCreateProjectLabel()

  const handleCreate = () => {
    if (!newLabel.name || !newLabel.description) {
      toast.error('Nama dan deskripsi label tidak boleh kosong.')
      return
    }

    createLabelMutate(
      { projectId, ...newLabel },
      {
        onSuccess: () => {
          toast.success('Label baru berhasil ditambahkan')
          queryClient.invalidateQueries([PROJECT_LABEL_LIST_QUERY_KEY, projectId])
          onCancel()
        },
        onError: () => {
          toast.error('Gagal menambahkan label baru')
        }
      }
    )
  }

  return (
    <div className='border-t border-gray-200 mt-4 pt-4'>
      <Grid container spacing={3} alignItems='center'>
        <Grid item xs={12}>
          <TextField
            fullWidth
            label='Nama Label'
            value={newLabel.name}
            onChange={e => setNewLabel(prev => ({ ...prev, name: e.target.value }))}
            size='small'
          />
        </Grid>
        <Grid item xs={12}>
          <TextField
            fullWidth
            label='Deskripsi'
            value={newLabel.description}
            onChange={e => setNewLabel(prev => ({ ...prev, description: e.target.value }))}
            size='small'
          />
        </Grid>
        <Grid item xs={12} sm={4} className='flex gap-2'>
          <Button onClick={handleCreate} disabled={isLoading} variant='contained' size='small'>
            {isLoading ? <CircularProgress size={20} color='inherit' /> : 'Simpan'}
          </Button>
          <Button onClick={onCancel} variant='outlined' size='small' color='secondary'>
            Batal
          </Button>
        </Grid>
      </Grid>
    </div>
  )
}

type Props = {
  projectData?: ProjectType
}

const ProjectLabelCard = ({ projectData }: Props) => {
  const [isAdding, setIsAdding] = useState(false)
  const {
    data: projectLabelResponse,
    refetch: fetchLabels,
    isLoading
  } = useQuery(
    [PROJECT_LABEL_LIST_QUERY_KEY, projectData?.id],
    () => CompanyQueryMethods.getProjectLabels(projectData?.id),
    {
      enabled: !!projectData?.id,
      placeholderData: defaultListData as ListResponse<ProjectLabelType>
    }
  )

  return (
    <Card>
      <CardContent className='flex flex-col gap-3'>
        <div className='flex justify-between items-center mb-3'>
          <Typography variant='h5'>Label Proyek</Typography>
          {!isAdding && (
            <Button variant='contained' size='small' onClick={() => setIsAdding(true)}>
              Tambah Label
            </Button>
          )}
        </div>

        {isLoading && <CircularProgress size={24} />}

        {projectLabelResponse?.items?.map(label => (
          <LabelItem key={label.id} label={label} projectId={projectData.id} />
        ))}

        {!isLoading && !isAdding && projectLabelResponse?.items?.length === 0 && (
          <Typography color='text.secondary' fontStyle='italic'>
            Tidak ada label yang ditambahkan.
          </Typography>
        )}

        {isAdding && projectData && <AddLabelForm projectId={projectData.id} onCancel={() => setIsAdding(false)} />}
      </CardContent>
    </Card>
  )
}

export default ProjectLabelCard
