import { ProjectType } from '@/types/projectTypes'
import { Card, CardContent, Typography } from '@mui/material'

type Props = {
  projectData: ProjectType
}

const ProjectLocationCard = ({ projectData }: Props) => {
  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Lokasi Proyek</Typography>
        </div>
        {projectData?.sites?.map(site => (
          <div className='flex flex-col gap-1' key={site.id}>
            <small><PERSON><PERSON></small>
            <Typography>{site.name}</Typography>
          </div>
        ))}
      </CardContent>
    </Card>
  )
}

export default ProjectLocationCard
