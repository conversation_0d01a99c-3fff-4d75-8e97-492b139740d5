import { UnitType } from '@/types/companyTypes'
import {
  Autocomplete,
  Card,
  CardContent,
  CircularProgress,
  debounce,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography
} from '@mui/material'
import { formatDate, formatISO, toDate } from 'date-fns'
import { Controller, useFormContext } from 'react-hook-form'
import { AddUnitInput, useFormUnit } from '../../context/FormUnitContext'
import { useAuth } from '@/contexts/AuthContext'
import AppReactDatepicker from '@/components/libs/styles/AppReactDatepicker'
import { useQuery } from '@tanstack/react-query'
import CompanyQueryMethods, { PROJECT_LIST_QUERY_KEY } from '@/api/services/company/query'
import { useCallback, useState } from 'react'
import { ProjectType } from '@/types/projectTypes'

const AdditionalInfoCard = ({ unitData }: { unitData: UnitType }) => {
  const { isLoading } = useFormUnit()
  const { control } = useFormContext<AddUnitInput>()
  const [searchProject, setSearchProject] = useState('')

  const { data: projectList, isFetching: isLoadingAccounts } = useQuery({
    queryKey: [PROJECT_LIST_QUERY_KEY, searchProject],
    queryFn: () => CompanyQueryMethods.getProjectList({ limit: 10, search: searchProject })
  })

  const debouncedSearch = useCallback(
    debounce((value: string) => {
      setSearchProject(value)
    }, 500),
    []
  )

  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Data Lainnya</Typography>
        </div>
        <div className='grid grid-cols-1 gap-6'>
          <Controller
            control={control}
            name='ownerName'
            rules={{ required: true }}
            render={({ field: { value, onChange, ref }, fieldState: { error } }) => (
              <TextField
                required
                value={value}
                onChange={onChange}
                ref={ref}
                error={!!error}
                fullWidth
                label='Aset Milik'
                placeholder='Masukkan Aset Milik'
                disabled={isLoading}
                InputLabelProps={{ shrink: !!value }}
              />
            )}
          />
          <Controller
            control={control}
            name='projectId'
            render={({ field, fieldState: { error } }) => (
              <Autocomplete
                options={projectList?.items || []}
                getOptionLabel={(option: ProjectType) => `${option.name}`}
                onInputChange={(_, value) => debouncedSearch(value)}
                loading={isLoadingAccounts}
                onChange={(_, newValue) => field.onChange(newValue?.id || '')}
                value={projectList?.items?.find(account => account.id === field.value) || null}
                renderInput={params => (
                  <TextField
                    {...params}
                    label='Proyek (Opsional)'
                    error={!!error}
                    helperText={error?.message}
                    InputProps={{
                      ...params.InputProps,
                      endAdornment: (
                        <>
                          {isLoadingAccounts ? <CircularProgress color='inherit' size={20} /> : null}
                          {params.InputProps.endAdornment}
                        </>
                      )
                    }}
                  />
                )}
              />
            )}
          />
          <Controller
            control={control}
            name='specification'
            render={({ field: { value, onChange, ref }, fieldState: { error } }) => (
              <TextField
                value={value}
                onChange={onChange}
                ref={ref}
                error={!!error}
                fullWidth
                label='Spesifikasi (Opsional)'
                placeholder='Masukkan Spesifikasi'
                disabled={isLoading}
                InputLabelProps={{ shrink: !!value }}
              />
            )}
          />
          <Controller
            name='purchaseDate'
            control={control}
            rules={{ required: true }}
            render={({ field: { value, onChange }, fieldState: { error } }) => (
              <AppReactDatepicker
                boxProps={{ className: 'is-full' }}
                selected={value ? toDate(value) : undefined}
                onChange={(date: Date) => onChange(formatISO(date))}
                dateFormat='eeee dd/MM/yyyy'
                disabled={isLoading}
                required
                customInput={
                  <TextField
                    required
                    fullWidth
                    label='Tanggal Pembelian'
                    className='flex-1'
                    InputProps={{
                      readOnly: true
                    }}
                    {...(!!error && { error: true })}
                  />
                }
              />
            )}
          />
          {/* <Controller
            control={control}
            name='condition'
            rules={{ required: true }}
            render={({ field, fieldState: { error } }) => (
              <FormControl fullWidth>
                <InputLabel required id='select-condition'>
                  Kondisi Unit
                </InputLabel>
                <Select
                  key={field.value}
                  label='Kondisi Unit'
                  fullWidth
                  id='select-condition'
                  value={field.value}
                  onChange={e => field.onChange(e.target.value)}
                  labelId='condition-select'
                  inputProps={{ placeholder: 'Pilih Kondisi Unit' }}
                  defaultValue=''
                  error={!!error}
                  disabled={isLoading}
                >
                  {['BREAKDOWN', 'READY_FOR_USE'].map(condition => (
                    <MenuItem key={condition} value={condition}>
                      {condition.replaceAll('_', ' ')}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            )}
          />
          <Controller
            control={control}
            name='smType'
            rules={{ required: true }}
            render={({ field, fieldState: { error } }) => (
              <FormControl fullWidth>
                <InputLabel required id='select-smType'>
                  Tipe SM
                </InputLabel>
                <Select
                  key={field.value}
                  label='Tipe SM'
                  fullWidth
                  id='select-smType'
                  value={field.value}
                  onChange={e => field.onChange(e.target.value)}
                  labelId='smType-select'
                  inputProps={{ placeholder: 'Pilih Tipe SM' }}
                  defaultValue=''
                  error={!!error}
                  disabled={isLoading}
                >
                  {['SM_250', 'SM_500', 'SM_1000', 'SM_2000'].map(smType => (
                    <MenuItem key={smType} value={smType}>
                      {smType.replaceAll('_', ' ')}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            )}
          /> */}
          <Controller
            control={control}
            name='description'
            render={({ field }) => (
              <TextField
                multiline
                rows={3}
                {...field}
                fullWidth
                label='Keterangan (Opsional)'
                variant='outlined'
                placeholder='Masukkkan Keterangan'
                disabled={isLoading}
                InputLabelProps={{ shrink: !!field.value }}
              />
            )}
          />
        </div>
      </CardContent>
    </Card>
  )
}

export default AdditionalInfoCard
