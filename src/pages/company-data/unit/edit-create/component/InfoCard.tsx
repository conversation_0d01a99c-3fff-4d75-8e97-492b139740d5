// MUI Imports
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'

import { useUnit } from '../../context/UnitContext'
import { Controller, useFormContext } from 'react-hook-form'
import { AddUnitInput, useFormUnit } from '../../context/FormUnitContext'
import { Autocomplete, FormControl, InputLabel, MenuItem, Select, TextField } from '@mui/material'
import { CategoryType } from '@/types/companyTypes'

const InfoCard = () => {
  const { unitData, categoryList } = useUnit()
  const { isLoading, subCategoryList } = useFormUnit()
  const { control } = useFormContext<AddUnitInput>()
  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Detil Unit</Typography>
        </div>
        <div className='grid md:grid-cols-2 gap-6'>
          <Controller
            control={control}
            name='number'
            rules={{ required: true }}
            render={({ field: { value, onChange, ref }, fieldState: { error } }) => (
              <TextField
                required
                value={value}
                onChange={onChange}
                ref={ref}
                error={!!error}
                fullWidth
                label='Kode Unit'
                placeholder='Masukkan Kode Unit'
                disabled={isLoading}
                InputLabelProps={{ shrink: !!value }}
              />
            )}
          />
          {/* <Controller
            control={control}
            name='assetCode'
            rules={{ required: true }}
            render={({ field: { value, onChange, ref }, fieldState: { error } }) => (
              <TextField
                required
                value={value}
                onChange={onChange}
                ref={ref}
                error={!!error}
                fullWidth
                label='Kode Activa'
                placeholder='Masukkan Kode Activa'
                disabled={isLoading}
                InputLabelProps={{ shrink: !!value }}
              />
            )}
          /> */}
          <Controller
            control={control}
            name='categoryId'
            rules={{ required: true }}
            render={({ field: { value, onChange }, formState: { errors } }) => (
              <Autocomplete
                key={JSON.stringify(categoryList)}
                selectOnFocus
                clearOnBlur
                handleHomeEndKeys
                freeSolo
                value={categoryList.find(category => category.id === value) || null}
                getOptionLabel={option => (option as CategoryType).name}
                options={categoryList}
                disabled={isLoading}
                onChange={(e, newValue) => {
                  if (newValue) {
                    onChange((newValue as CategoryType).id)
                  } else {
                    onChange(null)
                  }
                }}
                renderInput={params => (
                  <TextField required {...params} label='Kategori Unit' {...(errors.categoryId && { error: true })} />
                )}
              />
            )}
          />
          <Controller
            name='subCategoryId'
            control={control}
            rules={{ required: true }}
            render={({ field: { value, onChange }, formState: { errors } }) => (
              <Autocomplete
                key={JSON.stringify(subCategoryList)}
                selectOnFocus
                clearOnBlur
                handleHomeEndKeys
                freeSolo
                value={subCategoryList.find(category => category.id === value) || null}
                getOptionLabel={option => (option as CategoryType).name}
                options={subCategoryList}
                disabled={isLoading}
                onChange={(e, newValue) => {
                  if (newValue) {
                    onChange((newValue as CategoryType).id)
                  } else {
                    onChange(null)
                  }
                }}
                renderInput={params => (
                  <TextField {...params} label='Jenis Unit' {...(errors.subCategoryId && { error: true })} />
                )}
              />
            )}
          />
          {/* <div className='col-span-2'>
            <Controller
              control={control}
              name='equipmentType'
              rules={{ required: true }}
              render={({ field: { value, onChange, ref }, fieldState: { error } }) => (
                <FormControl fullWidth>
                  <InputLabel required id='select-type'>
                    Tipe Equipment
                  </InputLabel>
                  <Select
                    key={value}
                    label='Tipe Equipment'
                    fullWidth
                    id='select-type'
                    value={value}
                    onChange={onChange}
                    labelId='type-select'
                    inputProps={{ placeholder: 'Pilih Tipe Equipment' }}
                    error={!!error}
                    disabled={isLoading}
                  >
                    {['SUPPORT', 'OPERATIONAL'].map(i => (
                      <MenuItem key={i} value={i}>
                        {i}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              )}
            />
          </div> */}
          <Controller
            name='brandName'
            control={control}
            rules={{ required: true }}
            render={({ field, fieldState: { error } }) => (
              <TextField
                required
                {...field}
                fullWidth
                label='Merk Unit'
                variant='outlined'
                placeholder='Masukkkan Merk Unit'
                disabled={isLoading}
                InputLabelProps={{ shrink: !!field.value }}
                error={!!error}
              />
            )}
          />
          <Controller
            name='type'
            control={control}
            rules={{ required: true }}
            render={({ field, fieldState: { error } }) => (
              <TextField
                required
                {...field}
                fullWidth
                label='Tipe Unit'
                variant='outlined'
                placeholder='Masukkkan Tipe Unit'
                disabled={isLoading}
                InputLabelProps={{ shrink: !!field.value }}
                error={!!error}
              />
            )}
          />
          <Controller
            name='hullNumber'
            control={control}
            rules={{ required: true }}
            render={({ field, fieldState: { error } }) => (
              <TextField
                required
                {...field}
                fullWidth
                label='Nomor Lambung'
                variant='outlined'
                disabled={isLoading}
                placeholder='Masukkkan Nomor Lambung Unit'
                InputLabelProps={{ shrink: !!field.value }}
                error={!!error}
              />
            )}
          />
          <Controller
            name='chassisNumber'
            control={control}
            rules={{ required: true }}
            render={({ field, fieldState: { error } }) => (
              <TextField
                required
                {...field}
                fullWidth
                label='Nomor Rangka'
                variant='outlined'
                disabled={isLoading}
                placeholder='Masukkkan Nomor Rangka Unit'
                InputLabelProps={{ shrink: !!field.value }}
                error={!!error}
              />
            )}
          />
          <Controller
            control={control}
            name='engineNumber'
            rules={{ required: true }}
            render={({ field: { value, onChange, ref }, fieldState: { error } }) => (
              <TextField
                required
                value={value}
                onChange={onChange}
                ref={ref}
                error={!!error}
                fullWidth
                label='Nomor Mesin'
                placeholder='Masukkan Nomor Mesin'
                disabled={isLoading}
                InputLabelProps={{ shrink: !!value }}
              />
            )}
          />
          <Controller
            control={control}
            name='plateNumber'
            rules={{ required: true }}
            render={({ field: { value, onChange, ref }, fieldState: { error } }) => (
              <TextField
                required
                value={value}
                onChange={onChange}
                ref={ref}
                error={!!error}
                fullWidth
                label='Plat Nomor'
                placeholder='Masukkan Plat Nomor'
                disabled={isLoading}
                InputLabelProps={{ shrink: !!value }}
              />
            )}
          />
        </div>
      </CardContent>
    </Card>
  )
}

export default InfoCard
