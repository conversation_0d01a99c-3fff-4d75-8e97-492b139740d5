import { Grid, Breadcrumbs, Typography, Button } from '@mui/material'
import { Link, useParams } from 'react-router-dom'
import { useUnit } from '../context/UnitContext'
import InfoCard from './component/InfoCard'
import DocumentCards from './component/DocumentCard'
import AdditionalInfoCard from './component/AdditionalInfoCard'
import ImagesCard from './component/ImagesCard'
import { FormProvider, SubmitHandler, useForm, useFormContext, useWatch } from 'react-hook-form'
import LoadingButton from '@mui/lab/LoadingButton'
import { AddUnitInput, defaultDocsList, defaultInsurance, DocObjectType, useFormUnit } from '../context/FormUnitContext'
import { useEffect } from 'react'

const EditCreateUnitPage = () => {
  const { unitData } = useUnit()
  const { onSubmitHandler, isLoading, setDocsList, setInsuranceList, navigate } = useFormUnit()
  const { mode, unitId } = useParams()

  const { handleSubmit, reset, getValues } = useFormContext<AddUnitInput>()

  useEffect(() => {
    reset({
      ...getValues(),
      projectId: unitData?.projectId,
      chassisNumber: unitData?.chassisNumber,
      number: unitData?.number,
      categoryId: unitData?.categoryId,
      subCategoryId: unitData?.subCategoryId,
      brandName: unitData?.brandName,
      type: unitData?.type,
      hullNumber: unitData?.hullNumber,
      description: unitData?.description,
      assetCode: unitData?.asset?.code,
      ownerName: unitData?.ownerName,
      purchaseDate: unitData?.purchaseDate,
      condition: unitData?.condition as any,
      smType: unitData?.smType as any,
      equipmentType: unitData?.equipmentType,
      engineNumber: unitData?.engineNumber,
      plateNumber: unitData?.plateNumber,
      km: unitData?.km ?? 0,
      hm: unitData?.hm ?? 0,
      status: 'ACTIVE'
    })
    setDocsList(
      defaultDocsList.map(doc => ({
        ...doc,
        name: unitData?.documents?.find(d => d.type === doc.type)?.name,
        content: unitData?.documents?.find(d => d.type === doc.type)?.url,
        uploadId: unitData?.documents?.find(d => d.type === doc.type)?.uploadId,
        effectiveDate: unitData?.documents?.find(d => d.type === doc.type)?.effectiveDate,
        expirationDate: unitData?.documents?.find(d => d.type === doc.type)?.expirationDate,
        renewalDate: unitData?.documents?.find(d => d.type === doc.type)?.renewalDate
      }))
    )
    setInsuranceList(
      defaultInsurance.map(insurance => ({
        ...insurance,
        name: unitData?.documents?.find(d => d.type === insurance.type)?.name,
        content: unitData?.documents?.find(d => d.type === insurance.type)?.url,
        uploadId: unitData?.documents?.find(d => d.type === insurance.type)?.uploadId,
        effectiveDate: unitData?.documents?.find(d => d.type === insurance.type)?.effectiveDate,
        expirationDate: unitData?.documents?.find(d => d.type === insurance.type)?.expirationDate,
        renewalDate: unitData?.documents?.find(d => d.type === insurance.type)?.renewalDate
      }))
    )
  }, [unitData])

  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Breadcrumbs aria-label='breadcrumb'>
          <Link to='#' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Manajemen Aset</Typography>
          </Link>
          <Link to='/company-data/assets/unit' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Aset Unit</Typography>
          </Link>
          {mode == 'edit' && (
            <Link to={'/company-data/assets/unit/' + unitId} replace>
              <Typography color='var(--mui-palette-text-disabled)'>List Unit</Typography>
            </Link>
          )}
          <Typography sx={{ color: 'text.primary' }}>{mode === 'edit' ? 'Edit' : 'Tambah'} Unit</Typography>
        </Breadcrumbs>
      </Grid>
      <Grid item xs={12}>
        <div className='flex justify-between items-end'>
          <div className='flex flex-col gap-2'>
            <Typography variant='h4'>{mode === 'edit' ? 'Edit' : 'Tambah'} Unit</Typography>
            <Typography>Lengkapi data unit dan tambahkan ke daftar aset unit</Typography>
          </div>
          <div className='flex gap-2'>
            <Button
              onClick={() => navigate(`/company-data/assets/unit${unitId !== '-' ? `/${unitId}` : ''}`)}
              disabled={isLoading}
              color='secondary'
              variant='outlined'
              className='is-full sm:is-auto'
            >
              Batalkan
            </Button>
            <LoadingButton
              onClick={handleSubmit(onSubmitHandler, console.error)}
              loading={isLoading}
              color='primary'
              variant='contained'
              className='is-full sm:is-auto'
            >
              {mode === 'edit' ? 'Ubah Unit' : 'Tambah Unit'}
            </LoadingButton>
          </div>
        </div>
      </Grid>
      <Grid item xs={12} md={6}>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <InfoCard />
          </Grid>
          <Grid item xs={12}>
            <DocumentCards />
          </Grid>
        </Grid>
      </Grid>
      <Grid item xs={12} md={6}>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <AdditionalInfoCard unitData={unitData} />
          </Grid>
          <Grid item xs={12}>
            <ImagesCard unitData={unitData} />
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  )
}

export default EditCreateUnitPage
