// MUI Imports
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'

import { useItem } from '../../context/GoodsItemContext'
import PhotoPicker from '@/components/PhotoPicker'

const ImagesCard = () => {
  const { itemData } = useItem()
  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Foto Barang/Jasa</Typography>
        </div>
        <div className='flex items-center gap-4'>
          {itemData?.images?.map(image => <PhotoPicker key={image?.id} content={image?.url} isPreview />)}
        </div>
      </CardContent>
    </Card>
  )
}

export default ImagesCard
