// MUI Imports
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'

import { useItem } from '../../context/GoodsItemContext'
import { Grid } from '@mui/material'
import ConditionalWrapping from '@/components/layouts/ConditionalWrapping'
import { DEFAULT_CATEGORY } from '@/data/default/category'

const InfoCard = () => {
  const { itemData, isMobile } = useItem()
  // const coaList = JSON.parse(itemData?.description ?? null)?.coa
  // const inventoryCoa = JSON.parse(itemData?.description ?? null)?.inventoryCoa
  // const expenseCoa = JSON.parse(itemData?.description ?? null)?.expenseCoa

  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Card>
          <CardContent className='flex flex-col gap-6'>
            <div className='flex justify-between items-center'>
              <Typography variant='h5'>Detil Barang/Jasa</Typography>
            </div>
            <div className='flex flex-col gap-6'>
              <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
                <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                  <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                    Kode Induk
                  </small>
                </label>
                <div className='rounded-[10px] pt-0.5 flex flex-col gap-0 items-start relative bg-transparent' />
                <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                  {itemData?.parentCode ?? '-'}
                </p>
              </div>
              <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
                <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                  <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                    Kategori Item
                  </small>
                </label>
                <div className='rounded-[10px] pt-0.5 flex flex-col gap-0 items-start relative bg-transparent' />
                <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                  {itemData?.category?.name ?? DEFAULT_CATEGORY.name}
                </p>
              </div>
              <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
                <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                  <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                    Kelompok Barang/Jasa
                  </small>
                </label>
                <div className='rounded-[10px] pt-0.5 flex flex-col gap-0 items-start relative bg-transparent' />
                <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                  {itemData?.groupCategory?.name ?? DEFAULT_CATEGORY.name}
                </p>
              </div>
              <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
                <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                  <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                    Nama Item
                  </small>
                </label>
                <div className='rounded-[10px] pt-0.5 flex flex-col gap-0 items-start relative bg-transparent' />
                <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                  {itemData?.name}
                </p>
              </div>
              <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
                <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                  <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                    Merk Item
                  </small>
                </label>
                <div className='rounded-[10px] pt-0.5 flex flex-col gap-0 items-start relative bg-transparent' />
                <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                  {itemData?.brandName}
                </p>
              </div>
              <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
                <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                  <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                    Satuan Besar
                  </small>
                </label>
                <div className='rounded-[10px] pt-0.5 flex flex-col gap-0 items-start relative bg-transparent' />
                <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                  {itemData?.largeUnit}
                </p>
              </div>
              <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
                <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                  <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                    Satuan Kecil
                  </small>
                </label>
                <div className='rounded-[10px] pt-0.5 flex flex-col gap-0 items-start relative bg-transparent' />
                <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                  {itemData?.smallUnit}
                </p>
              </div>
              <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
                <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                  <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                    Konversi Satuan
                  </small>
                </label>
                <div className='rounded-[10px] pt-0.5 flex flex-col gap-0 items-start relative bg-transparent' />
                <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                  1 {itemData?.largeUnit} = {itemData?.largeUnitQuantity} {itemData?.smallUnit}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </Grid>
      {itemData?.vendor ? (
        <Grid item xs={12}>
          <Card>
            <CardContent className='flex flex-col gap-6'>
              <div className='flex justify-between items-center'>
                <Typography variant='h5'>Informasi Vendor</Typography>
              </div>
              <div className='flex flex-col gap-6'>
                <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
                  <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                    <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                      Kode Vendor
                    </small>
                  </label>
                  <div className='rounded-[10px] pt-0.5 flex flex-col gap-0 items-start relative bg-transparent' />
                  <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                    {itemData?.vendor?.code}
                  </p>
                </div>
                <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
                  <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                    <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                      Nama Vendor
                    </small>
                  </label>
                  <div className='rounded-[10px] pt-0.5 flex flex-col gap-0 items-start relative bg-transparent' />
                  <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                    {itemData?.vendor?.name}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </Grid>
      ) : null}
    </Grid>
  )
}

export default InfoCard
