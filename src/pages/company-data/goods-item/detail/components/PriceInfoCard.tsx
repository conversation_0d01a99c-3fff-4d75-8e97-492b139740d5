// MUI Imports
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'

import { useItem } from '../../context/GoodsItemContext'
import { Grid } from '@mui/material'
import { isNullOrUndefined, toCurrency } from '@/utils/helper'
import { PurchaseOrderDiscountType, PurchaseOrderTaxType } from '@/pages/purchase-order/config/enum'
import { discountTypeOptions, taxTypeOptions } from '@/pages/purchase-order/config/options'

const PriceInfoCard = () => {
  const { itemData } = useItem()
  // const coaList = JSON.parse(itemData?.description ?? null)?.coa
  // const inventoryCoa = JSON.parse(itemData?.description ?? null)?.inventoryCoa
  // const expenseCoa = JSON.parse(itemData?.description ?? null)?.expenseCoa

  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Informasi Harga</Typography>
        </div>
        <div className='flex flex-col gap-4'>
          <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
            <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
              <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                Mata Uang
              </small>
            </label>
            <div className='rounded-[10px] pt-0.5 flex flex-col gap-0 items-start relative bg-transparent' />
            <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
              {itemData?.currency?.name ?? 'Rupiah'}
            </p>
          </div>
          <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
            <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
              <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                Harga Satuan Kecil
              </small>
            </label>
            <div className='rounded-[10px] pt-0.5 flex flex-col gap-0 items-start relative bg-transparent' />
            <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
              {itemData?.pricePerUnit ? toCurrency(itemData?.pricePerUnit, false, itemData?.currency?.code) : '-'}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default PriceInfoCard
