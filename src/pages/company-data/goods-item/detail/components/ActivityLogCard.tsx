// MUI Imports
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import TimelineDot from '@mui/lab/TimelineDot'
import TimelineItem from '@mui/lab/TimelineItem'
import TimelineContent from '@mui/lab/TimelineContent'
import TimelineSeparator from '@mui/lab/TimelineSeparator'
import TimelineConnector from '@mui/lab/TimelineConnector'
import Typography from '@mui/material/Typography'

import { Timeline } from '@/components/Timeline'
import { Avatar } from '@mui/material'
import { formatDistanceToNow } from 'date-fns'
import { id } from 'date-fns/locale'
import { ActivityLogsType } from '@/types/companyTypes'
import ConditionalWrapping from '@/components/layouts/ConditionalWrapping'

type Props = {
  logList?: ActivityLogsType[]
  context?: string
  withoutCard?: boolean
}

const ActivityLogCard = ({ logList = [], context = 'Barang/Jasa', withoutCard = false }: Props) => {
  const renderTimeLine = () => (
    <Timeline>
      {logList?.map(log => {
        let dotColor: 'primary' | 'secondary' | 'error' | 'warning' | 'info' | 'success' | 'inherit' | 'grey' =
          'primary'
        let title = ''
        const changes = ((JSON.parse(log.changes) as string[]) ?? []).map(change =>
          change.replaceAll('"', '').replaceAll('{changed}', '→').replaceAll('{added}', '')
        )
        switch (log.action) {
          // case 'CREATE':
          //   title = `${context} Dibuat`
          //   dotColor = 'success'
          //   break
          case 'UPDATE':
            title = `Detil ${context} Diperbarui`
            dotColor = 'warning'
            break
          case 'DELETE':
            title = `${context} Dihapus`
            dotColor = 'error'
            break
          default:
            break
        }
        return title ? (
          <TimelineItem key={log.id} className='pt-2'>
            <TimelineSeparator>
              <TimelineDot color={dotColor} />
              <TimelineConnector />
            </TimelineSeparator>
            <TimelineContent>
              <div className='flex flex-wrap items-center justify-between gap-x-2 mbe-1'>
                <Typography color='text.primary' className='font-medium text-base'>
                  {title}
                </Typography>
                <Typography variant='caption'>
                  {formatDistanceToNow(log.createdAt, {
                    locale: id,
                    addSuffix: true
                  })
                    .replace('sekitar ', '')
                    .replace('kurang dari ', '')}
                </Typography>
              </div>
              {changes?.map(change => (
                <Typography key={change} className='mbe-2 text-sm'>
                  {change}
                </Typography>
              ))}
              {log.user ? (
                <div className='flex items-center gap-3'>
                  <Avatar src={log?.user?.profilePictureUrl} />
                  <div className='flex flex-col'>
                    <Typography color='text.primary' className='font-medium'>
                      {log.user?.fullName}
                    </Typography>
                    <Typography variant='body2'>{log.user?.title}</Typography>
                  </div>
                </div>
              ) : null}
            </TimelineContent>
          </TimelineItem>
        ) : null
      })}
    </Timeline>
  )
  if (withoutCard) {
    return <>{renderTimeLine()}</>
  }
  return (
    <Card>
      <CardHeader title='Log Aktivitas' />
      <CardContent>{renderTimeLine()}</CardContent>
    </Card>
  )
}

export default ActivityLogCard
