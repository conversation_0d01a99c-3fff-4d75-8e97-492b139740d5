import { But<PERSON>, Card, CardContent, Typography } from '@mui/material'
import { useState } from 'react'
import DialogAccountsItem from './dialog-accounts-item'
import { CategoryType, ItemType } from '@/types/companyTypes'
import { isNullOrUndefined } from '@/utils/helper'
import Permission from '@/core/components/Permission'
import { useItem } from '../../context/GoodsItemContext'

const AccountsCard = ({ itemData }: { itemData: ItemType }) => {
  const { fetchAccountItemData, accountItemData, fetchItemData } = useItem()
  const [editDialog, setEditDialog] = useState(false)

  const isAccountsEmpty =
    isNullOrUndefined(accountItemData?.inventoryAccount) &&
    isNullOrUndefined(accountItemData?.expenseAccount) &&
    isNullOrUndefined(accountItemData?.salesAccount) &&
    isNullOrUndefined(accountItemData?.salesReturnAccount) &&
    isNullOrUndefined(accountItemData?.salesDiscountAccount) &&
    isNullOrUndefined(accountItemData?.goodsShippedAccount) &&
    isNullOrUndefined(accountItemData?.cogsAccount) &&
    isNullOrUndefined(accountItemData?.purchaseReturnAccount) &&
    isNullOrUndefined(accountItemData?.unbilledPurchaseAccount)

  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-4'>
          <div className='flex justify-between items-center'>
            <Typography variant='h5'>Akun Perkiraan</Typography>
            <Permission permission={['item.write']}>
              <Button size='small' onClick={() => setEditDialog(true)} variant='outlined'>
                Edit Akun
              </Button>
            </Permission>
          </div>
          {isAccountsEmpty ? (
            <div className='flex flex-col gap-2 items-center py-6'>
              <Typography variant='h5'>Belum ada Akun Perkiraan</Typography>
              <Typography variant='body1' align='center'>
                Kontak tim accounting untuk mengisi akun perkiraan kategori dan barang/jasa ini
              </Typography>
            </div>
          ) : (
            <>
              {!!accountItemData?.inventoryAccount && (
                <div className='flex flex-col gap-2'>
                  <small>Persediaan</small>
                  <Typography>{`[${accountItemData?.inventoryAccount?.code}] ${accountItemData?.inventoryAccount?.name}`}</Typography>
                </div>
              )}
              {!!accountItemData?.expenseAccount && (
                <div className='flex flex-col gap-2'>
                  <small>Beban</small>
                  <Typography>{`[${accountItemData?.expenseAccount?.code}] ${accountItemData?.expenseAccount?.name}`}</Typography>
                </div>
              )}
              {!!accountItemData?.salesAccount && (
                <div className='flex flex-col gap-2'>
                  <small>Penjualan</small>
                  <Typography>{`[${accountItemData?.salesAccount?.code}] ${accountItemData?.salesAccount?.name}`}</Typography>
                </div>
              )}
              {!!accountItemData?.salesReturnAccount && (
                <div className='flex flex-col gap-2'>
                  <small>Retur Penjualan</small>
                  <Typography>{`[${accountItemData?.salesReturnAccount?.code}] ${accountItemData?.salesReturnAccount?.name}`}</Typography>
                </div>
              )}
              {!!accountItemData?.salesDiscountAccount && (
                <div className='flex flex-col gap-2'>
                  <small>Diskon Penjualan</small>
                  <Typography>{`[${accountItemData?.salesDiscountAccount?.code}] ${accountItemData?.salesDiscountAccount?.name}`}</Typography>
                </div>
              )}
              {!!accountItemData?.goodsShippedAccount && (
                <div className='flex flex-col gap-2'>
                  <small>Barang/Jasa Terkirim</small>
                  <Typography>{`[${accountItemData?.goodsShippedAccount?.code}] ${accountItemData?.goodsShippedAccount?.name}`}</Typography>
                </div>
              )}
              {!!accountItemData?.cogsAccount && (
                <div className='flex flex-col gap-2'>
                  <small>Harga pokok penjualan / COGS</small>
                  <Typography>{`[${accountItemData?.cogsAccount?.code}] ${accountItemData?.cogsAccount?.name}`}</Typography>
                </div>
              )}
              {!!accountItemData?.purchaseReturnAccount && (
                <div className='flex flex-col gap-2'>
                  <small>Retur Pembelian</small>
                  <Typography>{`[${accountItemData?.purchaseReturnAccount?.code}] ${accountItemData?.purchaseReturnAccount?.name}`}</Typography>
                </div>
              )}
              {!!accountItemData?.unbilledPurchaseAccount && (
                <div className='flex flex-col gap-2'>
                  <small>Pembelian Belum Tertagih</small>
                  <Typography>{`[${accountItemData?.unbilledPurchaseAccount?.code}] ${accountItemData?.unbilledPurchaseAccount?.name}`}</Typography>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>
      {editDialog && (
        <DialogAccountsItem
          callback={() => Promise.all([fetchAccountItemData(), fetchItemData()])}
          itemData={itemData}
          open={editDialog}
          setOpen={setEditDialog}
        />
      )}
    </>
  )
}

export default AccountsCard
