import Grid from '@mui/material/Grid'
import { Box, Breadcrumbs, Button, Chip, IconButton, Typography } from '@mui/material'

import { Link, useLocation, useParams } from 'react-router-dom'

import { useItem } from '../context/GoodsItemContext'
import { useEffect, useState } from 'react'
import InfoCard from './components/InfoCard'
import ImagesCard from './components/ImagesCard'
import CreatorCard from './components/CreatorCard'
import PriceInfoCard from './components/PriceInfoCard'
import MobileDropDown from '@/components/layout/shared/components/MobileDropDown'
import AccountsCard from './components/AccountsCard'
import ActivityLogCard from './components/ActivityLogCard'

const GoodsItemDetailPage = () => {
  const location = useLocation()
  const { itemData, handleRemoveItem, selectedItemId, setSelectedItemId, setAddItemOpen, isMobile, itemLogs } =
    useItem()
  const { itemId } = useParams()
  const [actionBtn, setBtn] = useState<boolean>(false)

  const isAsset = location.pathname.includes('assets')

  useEffect(() => {
    if (!selectedItemId && setSelectedItemId) {
      setSelectedItemId(itemId)
    }
  }, [selectedItemId, setSelectedItemId])

  const actionButton = (
    <div className='flex flex-col gap-2 sm:flex-row is-full sm:is-auto'>
      <div className='flex flex-col gap-2'>
        {/* <Button
          color='secondary'
          variant='outlined'
          startIcon={<i className='ri-upload-2-line' />}
          className='is-full sm:is-auto'
        >
          Ekspor
        </Button>
        <Button
          color='secondary'
          variant='outlined'
          startIcon={<i className='ic-outline-local-printshop' />}
          className='is-full sm:is-auto'
        >
          Cetak
        </Button> */}
      </div>
      <Button
        color='error'
        variant='outlined'
        className='is-full sm:is-auto'
        onClick={() => handleRemoveItem(itemData?.id)}
      >
        Hapus
      </Button>
      <Button variant='contained' className='is-full sm:is-auto' onClick={() => setAddItemOpen(true)}>
        Ubah Data
      </Button>
    </div>
  )

  return (
    <>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <Breadcrumbs aria-label='breadcrumb'>
            <Link to={isAsset ? '/company-data/assets/goods' : '/company-data/item'} replace>
              <Typography color='var(--mui-palette-text-disabled)'>List Barang & Jasa</Typography>
            </Link>
            <Typography sx={{ color: 'text.primary' }}>Detil Barang & Jasa</Typography>
          </Breadcrumbs>
        </Grid>
        <Grid item xs={12}>
          <div className='flex flex-row justify-between gap-2 items-end sm:flex-row max-sm:items-center is-full sm:is-auto'>
            <div className='flex flex-col items-start max-sm:text-center'>
              <div className='flex gap-2'>
                <Typography className='text-left md:text-justify' variant='h4'>
                  Kode Barang/Jasa {itemData?.number}
                </Typography>
                <Chip
                  variant='tonal'
                  size='small'
                  label={!itemData?.isDiscontinue ? 'Tersedia' : 'Discontinue'}
                  color={!itemData?.isDiscontinue ? 'success' : 'error'}
                />
              </div>
              <Typography>Kode Eksternal {itemData?.vendorNumber}</Typography>
            </div>
            {isMobile ? (
              <IconButton onClick={() => setBtn(true)} className='bg-[#4C4E640D]/5 rounded-[8px]'>
                <i className='pepicons-pop--dots-y' />
              </IconButton>
            ) : (
              <div className='flex flex-col gap-2 sm:flex-row is-full sm:is-auto'>
                <div className='flex gap-2'>
                  {/* <Button
                    color='secondary'
                    variant='outlined'
                    startIcon={<i className='ri-upload-2-line' />}
                    className='is-full sm:is-auto'
                  >
                    Ekspor
                  </Button>
                  <Button
                    color='secondary'
                    variant='outlined'
                    startIcon={<i className='ic-outline-local-printshop' />}
                    className='is-full sm:is-auto'
                  >
                    Cetak
                  </Button> */}
                </div>
                <Button
                  color='error'
                  variant='outlined'
                  className='is-full sm:is-auto'
                  onClick={() => handleRemoveItem(itemData?.id)}
                >
                  Hapus
                </Button>
                <Button variant='contained' className='is-full sm:is-auto' onClick={() => setAddItemOpen(true)}>
                  Ubah Data
                </Button>
              </div>
            )}
          </div>
        </Grid>
        <Grid item xs={12} md={6}>
          <Grid container spacing={4}>
            <Grid item xs={12}>
              <InfoCard />
            </Grid>
            <Grid item xs={12}>
              <AccountsCard itemData={itemData} />
            </Grid>
          </Grid>
        </Grid>
        <Grid item xs={12} md={6}>
          <Grid container spacing={4}>
            {itemData?.pricePerUnit || itemData?.discountValue ? (
              <Grid item xs={12}>
                <PriceInfoCard />
              </Grid>
            ) : null}
            {(itemData?.images?.length ?? 0) > 0 && (
              <Grid item xs={12}>
                <ImagesCard />
              </Grid>
            )}
            <Grid item xs={12}>
              <CreatorCard />
            </Grid>
            {itemLogs && (
              <Grid item xs={12}>
                <ActivityLogCard logList={itemLogs?.items ?? []} />
              </Grid>
            )}
          </Grid>
        </Grid>
      </Grid>
      <MobileDropDown open={actionBtn} onClose={() => setBtn(false)} onOpen={() => setBtn(true)}>
        <Typography sx={{ marginTop: 2 }} align='center' variant='h5'>
          Action
        </Typography>
        <Box className='flex gap-2 p-4 pb-2 flex-col'>{actionButton}</Box>
      </MobileDropDown>
    </>
  )
}

export default GoodsItemDetailPage
