import { DEFAULT_CATEGORY } from '@/data/default/category'
import { ItemType } from '@/types/companyTypes'
import { Chip, colors, IconButton, Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'

type ItemTypeWithAction = ItemType & {
  action?: string
}

type RowActionType = {
  showDetail: (id: string) => void
  remove: (id: string) => void
}

// Column Definitions
const columnHelper = createColumnHelper<ItemTypeWithAction>()

export const tableColumns = (rowAction: RowActionType) => [
  columnHelper.accessor('parentCode', {
    header: 'Kode Induk',
    cell: ({ row }) => <Typography color={colors.green.A400}>{row.original.parentCode ?? '-'}</Typography>
  }),
  columnHelper.accessor('number', {
    header: 'Kode <PERSON>/Jasa',
    cell: ({ row }) => (
      <Typography
        color={colors.green.A400}
        className='cursor-pointer'
        onClick={() => rowAction.showDetail(row.original.id)}
      >
        {row.original.number}
      </Typography>
    )
  }),
  columnHelper.accessor('name', {
    header: 'Nama Item',
    cell: ({ row }) => <Typography>{row.original.name}</Typography>
  }),
  columnHelper.accessor('brandName', {
    header: 'Merk Item',
    cell: ({ row }) => <Typography>{row.original.brandName}</Typography>
  }),
  columnHelper.accessor('category.name', {
    header: 'Kategori Item',
    cell: ({ row }) => <Typography>{row.original?.category?.name ?? DEFAULT_CATEGORY.name}</Typography>
  }),
  columnHelper.accessor('isDiscontinue', {
    header: 'Status',
    cell: ({ row }) => (
      <Chip
        size='small'
        variant='tonal'
        label={row.original.isDiscontinue ? 'Discontinue' : 'Tersedia'}
        color={row.original.isDiscontinue ? 'error' : 'success'}
      />
    )
  }),
  columnHelper.accessor('createdAt', {
    header: 'Tanggal Dibuat',
    cell: ({ row }) => (
      <Typography>{formatDate(row.original.createdAt ?? Date.now(), 'eeee, dd/MM/yyyy', { locale: id })}</Typography>
    )
  }),
  columnHelper.accessor('action', {
    header: 'Action',
    cell: ({ row }) => (
      <div className='flex items-center gap-0.5'>
        <IconButton size='small' onClick={() => rowAction.remove(row.original.id)}>
          <i className='ri-delete-bin-7-line text-textSecondary' />
        </IconButton>
        <IconButton size='small' onClick={() => rowAction.showDetail(row.original.id)}>
          <i className='ri-eye-line text-textSecondary' />
        </IconButton>
      </div>
    ),
    enableSorting: false
  })
]
