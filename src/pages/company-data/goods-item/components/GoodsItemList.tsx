import { MouseEvent, useCallback, useEffect, useState } from 'react'
import { Box, FormControl, IconButton, InputLabel, Menu, MenuItem, Select, Typography } from '@mui/material'
import Card from '@mui/material/Card'
import Button from '@mui/material/Button'

import {
  getCoreRowModel,
  useReactTable,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFacetedMinMaxValues,
  getPaginationRowModel,
  getSortedRowModel
} from '@tanstack/react-table'

import DebouncedInput from '@/components/DebounceInput'
import Table from '@/components/table'
import { tableColumns } from '../config/table'
import { useItem } from '../context/GoodsItemContext'
import { useRouter } from '@/routes/hooks'
import { useExportState } from '@/core/hooks/useExportImport'
import { ExportImportScope } from '@/types/exportImportTypes'
import { ItemParams } from '@/types/payload'
import ProcessingImportDialog from '@/components/dialogs/export-dialog'
import ImportDialog from '@/components/dialogs/import-dialog'
import { isNullOrUndefined } from '@/utils/helper'
import MobileDropDown from '@/components/layout/shared/components/MobileDropDown'
import { useLocation } from 'react-router-dom'

const GoodsItemList = () => {
  const router = useRouter()
  const location = useLocation()
  const {
    itemListResponse,
    itemsParams,
    setItemsParams,
    setSelectedItemId,
    setPartialItemsParams,
    handleRemoveItem,
    categoryList,
    setAddItemOpen,
    clearItemData,
    isMobile
  } = useItem()

  const { items: itemList, totalItems, totalPages, page: pageItems, limit: limitItems } = itemListResponse
  const { page, search, categoryId, limit } = itemsParams

  const [actionBtn, setBtn] = useState<boolean>(false)
  const [searchExtend, setSearchExtend] = useState<boolean>(false)
  const [addAnchorEl, setAddAnchorEl] = useState<null | HTMLElement>(null)
  const open = Boolean(addAnchorEl)
  const isAsset = location.pathname.includes('assets')

  // export boilerplate - start
  const [exportDialogOpen, setExportDialogOpen] = useState(false)
  const onExportSuccess = useCallback((success: boolean) => {
    if (success) {
      setExportDialogOpen(true)
    }
  }, [])
  const { exportFn, isExporting } = useExportState<ItemParams>(ExportImportScope.ITEM, onExportSuccess)
  // export boilerplate - end

  const [importDialogOpen, setImportDialogOpen] = useState(false)

  const handleImportSubmit = () => {
    setImportDialogOpen(false)
  }

  // TODO: MOVE THIS SHIT
  const table = useReactTable({
    data: itemList,
    columns: tableColumns({
      showDetail: id => {
        setSelectedItemId(id)
        router.push(isAsset ? `/company-data/assets/goods/${id}` : `/company-data/item/${id}`)
      },
      remove: handleRemoveItem
    }),
    initialState: {
      pagination: {
        pageSize: limit ?? 10,
        pageIndex: page - 1
      }
    },
    state: {
      pagination: {
        pageSize: limitItems,
        pageIndex: pageItems - 1
      }
    },
    manualPagination: true,
    rowCount: totalItems,
    pageCount: totalPages,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getFacetedMinMaxValues: getFacetedMinMaxValues()
  })

  const handleAddClick = (event: MouseEvent<HTMLButtonElement>) => {
    setAddAnchorEl(event.currentTarget)
    setBtn(false)
  }

  const handleAddClose = () => {
    setAddAnchorEl(null)
  }

  useEffect(() => {
    setItemsParams({
      limit: 10,
      page: 1
    })
    setSelectedItemId(undefined)
  }, [])

  const actionButton = (
    <>
      <Button
        color='secondary'
        variant='outlined'
        startIcon={<i className='ri-upload-2-line' />}
        className='is-full sm:is-auto'
        onClick={() => exportFn({ search, categoryId })}
        disabled={isExporting}
      >
        Ekspor
      </Button>
    </>
  )

  return (
    <>
      <ProcessingImportDialog
        open={exportDialogOpen}
        onClose={() => setExportDialogOpen(false)}
        contentScope='Barang'
      />
      <ImportDialog
        open={importDialogOpen}
        scope={ExportImportScope.ITEM}
        onSubmit={handleImportSubmit}
        setOpen={() => setImportDialogOpen(!importDialogOpen)}
      />
      <Card>
        <div className='flex justify-between gap-4 p-5 flex-row items-start sm:flex-row sm:items-center'>
          <div className='flex gap-4 items-center is-auto sm:is-auto flex-row sm:flex-row'>
            {searchExtend ? (
              <div className='flex gap-4 items-center is-full flex-col sm:flex-row'>
                <DebouncedInput
                  value={search}
                  onChange={value => setItemsParams(prev => ({ ...prev, page: 1, search: value as string }))}
                  onBlur={() => setSearchExtend(false)}
                  placeholder='Cari'
                  className='is-full'
                />
              </div>
            ) : !isMobile ? (
              <div className='flex gap-4 items-center is-full sm:is-auto flex-col sm:flex-row'>
                <DebouncedInput
                  value={search}
                  onChange={value => setItemsParams(prev => ({ ...prev, page: 1, search: value as string }))}
                  placeholder='Cari'
                  className='is-full sm:is-auto'
                />
              </div>
            ) : (
              <IconButton onClick={() => setSearchExtend(true)} className='bg-[#4C4E640D]/5 rounded-[8px]'>
                <i className='ri-search-line' />
              </IconButton>
            )}
            {!searchExtend && !isMobile && (
              <FormControl size='small' className='w-[240px] max-sm:is-full'>
                <InputLabel id='role-select'>Pilih Kategori</InputLabel>
                <Select
                  fullWidth
                  id='select-category'
                  value={categoryId}
                  onChange={e => setPartialItemsParams('categoryId', e.target.value)}
                  label='Pilih Kategori'
                  size='small'
                  labelId='category-select'
                  inputProps={{ placeholder: 'Pilih Kategori' }}
                  defaultValue=''
                >
                  <MenuItem value=''>Semua Kategori</MenuItem>
                  {categoryList.map(category => (
                    <MenuItem key={category.id} value={category.id}>
                      {category.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            )}
          </div>
          {!searchExtend && (
            <div className='flex items-center justify-end gap-x-4 max-sm:gap-y-4 is-full flex-row sm:is-auto sm:flex-row'>
              {isMobile ? (
                <IconButton onClick={() => setBtn(true)} className='bg-[#4C4E640D]/5 rounded-[8px]'>
                  <i className='pepicons-pop--dots-y' />
                </IconButton>
              ) : (
                <Button
                  color='secondary'
                  variant='outlined'
                  startIcon={<i className='ri-upload-2-line' />}
                  className='is-full sm:is-auto'
                  onClick={() => exportFn({ search, categoryId })}
                  disabled={isExporting}
                >
                  Ekspor
                </Button>
              )}
              <Button
                variant='contained'
                aria-haspopup='true'
                onClick={handleAddClick}
                aria-expanded={open ? 'true' : undefined}
                endIcon={<i className='ri-arrow-down-s-line' />}
                aria-controls={open ? 'user-view-overview-export' : undefined}
                className='sm:is-auto'
              >
                Tambah
              </Button>
              {isMobile ? (
                <MobileDropDown
                  className='z-10'
                  open={open}
                  onOpen={() => setAddAnchorEl(true as any)}
                  onClose={() => setAddAnchorEl(null)}
                >
                  <Typography sx={{ marginTop: 2 }} align='center' variant='h5'>
                    Action
                  </Typography>
                  <Box className='flex gap-2 p-4 pb-2 flex-col'>
                    <Button
                      variant='outlined'
                      startIcon={<i className='mdi-file-document-outline size-5' />}
                      onClick={() => {
                        setImportDialogOpen(true)
                        handleAddClose()
                      }}
                    >
                      Impor dari Excel
                    </Button>
                    <Button
                      variant='contained'
                      startIcon={<i className='ic-baseline-add-circle-outline size-5' />}
                      onClick={() => {
                        setAddItemOpen(true)
                        handleAddClose()
                      }}
                    >
                      Tambah Manual
                    </Button>
                  </Box>
                </MobileDropDown>
              ) : (
                <Menu open={open} anchorEl={addAnchorEl} onClose={handleAddClose} id='user-view-overview-export'>
                  <MenuItem
                    onClick={() => {
                      setImportDialogOpen(true)
                      handleAddClose()
                    }}
                    className='flex h-12'
                  >
                    <i className='mdi-file-document-outline size-5' />
                    Impor dari Excel
                  </MenuItem>
                  <MenuItem
                    onClick={() => {
                      setAddItemOpen(true)
                      handleAddClose()
                    }}
                    className='flex h-12'
                  >
                    <i className='ic-baseline-add-circle-outline size-5' />
                    Tambah Manual
                  </MenuItem>
                </Menu>
              )}
            </div>
          )}
        </div>
        <Table
          table={table}
          emptyLabel={
            <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
              <Typography> Belum ada Barang/Jasa</Typography>
              <Typography className='text-sm text-gray-400'>
                Semua barang & jasa yang telah dibuat akan ditampilkan di sini
              </Typography>
            </td>
          }
          onRowsPerPageChange={pageSize => {
            if (pageSize > totalItems) {
              setItemsParams(prev => ({ ...prev, limit: totalItems, page: 1 }))
            } else {
              setPartialItemsParams('limit', pageSize)

              const maxPage = Math.ceil(totalItems / pageSize)
              if (page > maxPage) {
                setItemsParams(prev => ({ ...prev, page: maxPage }))
              }
            }
          }}
          onPageChange={pageIndex => setPartialItemsParams('page', pageIndex)}
        />
      </Card>
      <MobileDropDown className='z-1' open={actionBtn} onClose={() => setBtn(false)} onOpen={() => setBtn(true)}>
        <Typography sx={{ marginTop: 2 }} align='center' variant='h5'>
          Action
        </Typography>
        <Box className='flex gap-2 p-4 pb-2 flex-col'>{actionButton}</Box>
      </MobileDropDown>
    </>
  )
}

export default GoodsItemList
