import { DraftType } from '@/types/draftsTypes'
import { Button, Grid, IconButton, Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'

const columHelper = createColumnHelper<DraftType>()

type RowAction = {
  onLoadingDraft: (row: DraftType) => void
  onDeleteDraft: (id: string) => void
}

export const tableColumns = (rowAction: RowAction) => [
  columHelper.accessor('department.name', {
    header: 'Departemen',
    cell: ({ row }) => row.original?.department?.name ?? '-'
  }),
  columHelper.accessor('site.name', {
    header: 'LOKASI',
    cell: ({ row }) => row.original?.site?.name ?? '-'
  }),
  columHelper.accessor('createdByUser.fullName', {
    header: 'Dibuat Oleh',
    cell: ({ row }) => {
      return (
        <div>
          <Typography>{row.original?.createdByUser?.fullName}</Typography>
          <Typography className='text-xs font-light'>{row.original?.createdByUser?.title}</Typography>
        </div>
      )
    }
  }),
  columHelper.accessor('updatedAt', {
    header: 'Tgl Diperbarui',
    cell: ({ row }) => formatDate(new Date(row.original.updatedAt), 'eeee, dd/MM/yyyy HH:mm', { locale: id })
  }),
  columHelper.display({
    id: 'actions',
    header: 'Action',
    cell: ({ row }) => (
      <Grid container spacing={2}>
        <Grid item>
          <IconButton size='small' onClick={() => rowAction.onDeleteDraft(row.original.id)}>
            <i className='ri-delete-bin-7-line text-textSecondary' />
          </IconButton>
        </Grid>
        <Grid item>
          <IconButton size='small' onClick={() => rowAction.onLoadingDraft(row.original)}>
            <i className='ri-eye-line text-textSecondary' />
          </IconButton>
        </Grid>
      </Grid>
    )
  })
]
