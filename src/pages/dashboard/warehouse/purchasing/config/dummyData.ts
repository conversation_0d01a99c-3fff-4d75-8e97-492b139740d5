import { DepartmentType, ItemType, SiteType } from '@/types/companyTypes'
import { PurchaseTableColumns, PurchaseDocsTable } from '@/types/dashboardTypes'
import { UserType } from '@/types/userTypes'

export const dummyPurchaseData: PurchaseTableColumns[] = [
  {
    id: 'a3f1c9e2-7b4d-4f8a-9d3e-1a2b3c4d5e6f',
    item: {
      id: 'b4d2e3f4-5a6b-7c8d-9e0f-1a2b3c4d5e6f',
      number: 'ITM-001',
      vendorNumber: 'VND-001',
      name: 'Excavator',
      description: 'Ergonomic wireless mouse with adjustable DPI',
      brandName: 'LogiTech',
      largeUnit: 'Box',
      smallUnit: 'Piece',
      largeUnitQuantity: 10,
      categoryId: 'c5d6e7f8-9a0b-1c2d-3e4f-5a6b7c8d9e0f',
      companyId: 'd7e8f9a0-b1c2-d3e4-f5a6-b7c8d9e0f1a2',
      parentCompanyId: 'e9f0a1b2-c3d4-e5f6-a7b8-c9d0e1f2a3b4',
      createdAt: '2023-01-01T00:00:00Z',
      updatedAt: '2023-01-02T00:00:00Z',
      category: {
        id: 'c5d6e7f8-9a0b-1c2d-3e4f-5a6b7c8d9e0f',
        type: 'CategoryType1',
        code: 'CAT001',
        name: 'Computer Accessories',
        parentId: 'f1a2b3c4-d5e6-f7a8-b9c0-d1e2f3a4b5c6',
        companyId: 'd7e8f9a0-b1c2-d3e4-f5a6-b7c8d9e0f1a2',
        parentCompanyId: 'e9f0a1b2-c3d4-e5f6-a7b8-c9d0e1f2a3b4',
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-02T00:00:00Z'
      },
      images: [],
      createdByUser: {
        id: 'f2a3b4c5-d6e7-f8a9-b0c1-d2e3f4a5b6c7',
        name: 'Alice Johnson',
        fullName: 'Alice Johnson',
        title: 'Manager',
        email: '<EMAIL>',
        role: 'admin',
        country: 'USA',
        contact: '*********',
        position: 'Manager',
        username: 'alicej',
        avatar: '',
        avatarColor: 'info',
        department: undefined,
        division: undefined,
        divisionId: 'null',
        threshold: undefined,
        phoneNumber: '*********',
        status: 'active',
        companyId: 'd7e8f9a0-b1c2-d3e4-f5a6-b7c8d9e0f1a2',
        subCompanyId: null,
        departmentId: null,
        permissionGroupId: '',
        sites: [],
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-02T00:00:00Z'
      }
    } as unknown as ItemType,
    totalPurchase: 100,
    percentPurchase: 75,
    percentReceived: 60,
    received: 60,
    notReceived: 40,
    quantityUnit: 'pcs'
  },
  {
    id: 'b7c8d9e0-f1a2-3b4c-5d6e-7f8a9b0c1d2e',
    item: {
      id: 'c8d9e0f1-a2b3-4c5d-6e7f-8a9b0c1d2e3f',
      number: 'ITM-002',
      vendorNumber: 'VND-002',
      name: 'Drill Rig',
      description: 'RGB backlit mechanical keyboard with blue switches',
      brandName: 'KeyMaster',
      largeUnit: 'Box',
      smallUnit: 'Piece',
      largeUnitQuantity: 20,
      categoryId: 'd9e0f1a2-b3c4-5d6e-7f8a-9b0c1d2e3f4a',
      companyId: 'e0f1a2b3-c4d5-6e7f-8a9b-0c1d2e3f4a5b',
      parentCompanyId: 'f1a2b3c4-d5e6-f7a8-b9c0-d1e2f3a4b5c6',
      createdAt: '2023-02-01T00:00:00Z',
      updatedAt: '2023-02-02T00:00:00Z',
      category: {
        id: 'd9e0f1a2-b3c4-5d6e-7f8a-9b0c1d2e3f4a',
        type: 'CategoryType2',
        code: 'CAT002',
        name: 'Keyboards',
        parentId: 'a1b2c3d4-e5f6-a7b8-c9d0-e1f2a3b4c5d6',
        companyId: 'e0f1a2b3-c4d5-6e7f-8a9b-0c1d2e3f4a5b',
        parentCompanyId: 'f1a2b3c4-d5e6-f7a8-b9c0-d1e2f3a4b5c6',
        createdAt: '2023-02-01T00:00:00Z',
        updatedAt: '2023-02-02T00:00:00Z'
      },
      images: [],
      createdByUser: {
        id: 'a3b4c5d6-e7f8-a9b0-c1d2-e3f4a5b6c7d8',
        name: 'Bob Smith',
        fullName: 'Bob Smith',
        title: 'Staff',
        email: '<EMAIL>',
        role: 'user',
        country: 'Canada',
        contact: '*********',
        position: 'Staff',
        username: 'bobsmith',
        avatar: '',
        avatarColor: 'info',
        department: undefined,
        division: undefined,
        divisionId: 'null',
        threshold: undefined,
        phoneNumber: '*********',
        status: 'active',
        companyId: 'e0f1a2b3-c4d5-6e7f-8a9b-0c1d2e3f4a5b',
        subCompanyId: null,
        departmentId: null,
        permissionGroupId: '',
        sites: [],
        createdAt: '2023-02-01T00:00:00Z',
        updatedAt: '2023-02-02T00:00:00Z'
      }
    } as unknown as ItemType,
    totalPurchase: 200,
    percentPurchase: 50,
    percentReceived: 40,
    received: 80,
    notReceived: 120,
    quantityUnit: 'pcs'
  },
  {
    id: 'c9d0e1f2-a3b4-c5d6-e7f8-a9b0c1d2e3f4',
    item: {
      id: 'd0e1f2a3-b4c5-d6e7-f8a9-b0c1d2e3f4a5',
      number: 'ITM-003',
      vendorNumber: 'VND-003',
      name: 'Mining Helmet',
      description: 'High-resolution 27-inch LED monitor with HDMI and DisplayPort',
      brandName: 'ViewTech',
      largeUnit: 'Box',
      smallUnit: 'Piece',
      largeUnitQuantity: 15,
      categoryId: 'e1f2a3b4-c5d6-e7f8-a9b0-c1d2e3f4a5b6',
      companyId: 'f2a3b4c5-d6e7-f8a9-b0c1-d2e3f4a5b6c7',
      parentCompanyId: 'a3b4c5d6-e7f8-a9b0-c1d2-e3f4a5b6c7d8',
      createdAt: '2023-03-01T00:00:00Z',
      updatedAt: '2023-03-02T00:00:00Z',
      category: {
        id: 'e1f2a3b4-c5d6-e7f8-a9b0-c1d2e3f4a5b6',
        type: 'CategoryType3',
        code: 'CAT003',
        name: 'Monitors',
        parentId: 'b4c5d6e7-f8a9-b0c1-d2e3-f4a5b6c7d8e9',
        companyId: 'f2a3b4c5-d6e7-f8a9-b0c1-d2e3f4a5b6c7',
        parentCompanyId: 'a3b4c5d6-e7f8-a9b0-c1d2-e3f4a5b6c7d8',
        createdAt: '2023-03-01T00:00:00Z',
        updatedAt: '2023-03-02T00:00:00Z'
      },
      images: [],
      createdByUser: {
        id: 'b4c5d6e7-f8a9-b0c1-d2e3-f4a5b6c7d8e9',
        name: 'Carol White',
        fullName: 'Carol White',
        title: 'Editor',
        email: '<EMAIL>',
        role: 'editor',
        country: 'UK',
        contact: '1122334455',
        position: 'Editor',
        username: 'carolwhite',
        avatar: '',
        avatarColor: 'info',
        department: undefined,
        division: undefined,
        divisionId: 'null',
        threshold: undefined,
        phoneNumber: '*********',
        status: 'active',
        companyId: 'e0f1a2b3-c4d5-6e7f-8a9b-0c1d2e3f4a5b',
        subCompanyId: null,
        departmentId: null,
        permissionGroupId: '',
        sites: [],
        createdAt: '2023-03-01T00:00:00Z',
        updatedAt: '2023-03-02T00:00:00Z'
      }
    } as unknown as ItemType,
    totalPurchase: 150,
    percentPurchase: 80,
    percentReceived: 70,
    received: 105,
    notReceived: 45,
    quantityUnit: 'pcs'
  }
]
