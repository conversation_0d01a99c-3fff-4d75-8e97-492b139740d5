import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { FormControl, InputLabel, MenuItem, Select, Typography } from '@mui/material'
import { optionsPeriodic } from '../../config/utils'
import { StatisticParams } from '@/types/dashboardTypes'
import { CompanySiteType } from '@/types/companyTypes'
import { usePurchaseDashboard } from '../context/DashboardPurchaseContext'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'

type FilterProps = {
  onChangeFilter?: (filter: Partial<StatisticParams> & { period?: string }) => void
  filterData: StatisticParams & { period?: string }
}

const FilterData = (props: FilterProps) => {
  const { isWarehousePage, mgOutSummary } = usePurchaseDashboard()
  const dateNow = new Date()
  const endDate = new Date(dateNow)
  endDate.setDate(dateNow.getDate() + 1)

  const [filterData, setFilterData] = useState<StatisticParams & { period?: string }>(props.filterData)
  const { ownSiteList } = useAuth()

  const handleChangeSite = (event: React.ChangeEvent<HTMLInputElement>) => {
    props?.onChangeFilter?.({ siteIds: event.target.value })
    setFilterData(prev => ({ ...prev, siteIds: event.target.value }))
  }

  const handleChangePeriod = (event: React.ChangeEvent<HTMLInputElement>) => {
    let startDate: Date | string
    let endDate: Date | string = new Date()
    let period = 'TODAY'
    switch (event.target.value) {
      case 'ALL': {
        period = 'ALL'
        const currentYear = new Date().getFullYear()
        startDate = new Date(currentYear, 0, 1).toISOString()
        endDate = new Date(currentYear, 11, 31, 23, 59, 59).toISOString()
        break
      }
      case 'TODAY': {
        period = 'TODAY'
        // endDate is now, startDate is yesterday
        endDate = new Date()
        startDate = new Date(endDate)
        startDate.setDate(endDate.getDate() - 1)
        startDate = startDate.toISOString()
        endDate = endDate.toISOString()
        break
      }
      case 'WEEK': {
        period = 'WEEK'
        // endDate is now, startDate is 7 days ago
        endDate = new Date()
        startDate = new Date(endDate)
        startDate.setDate(endDate.getDate() - 7)
        startDate = startDate.toISOString()
        endDate = endDate.toISOString()
        break
      }
      case 'TWO_WEEK': {
        period = 'TWO_WEEK'
        // endDate is now, startDate is 14 days ago
        endDate = new Date()
        startDate = new Date(endDate)
        startDate.setDate(endDate.getDate() - 14)
        startDate = startDate.toISOString()
        endDate = endDate.toISOString()
        break
      }
      case 'MONTH': {
        period = 'MONTH'
        // endDate is now, startDate is first day of this month
        endDate = new Date()
        startDate = new Date(endDate.getFullYear(), endDate.getMonth(), 1)
        startDate = startDate.toISOString()
        endDate = endDate.toISOString()
        break
      }
      default:
        break
    }
    setFilterData(prev => ({ ...prev, startDate: startDate as string, endDate: endDate as string, period }))
    props?.onChangeFilter?.({ startDate: startDate as string, endDate: endDate as string, period })
  }

  return (
    <div className='flex justify-between items-baseline'>
      <div className='flex items-center w-2/3 gap-2'>
        <FormControl fullWidth size='small'>
          <InputLabel>Lokasi</InputLabel>
          <Select
            onChange={handleChangeSite}
            defaultValue={filterData?.siteIds}
            size='small'
            label='Lokasi'
            className='bg-white'
          >
            <MenuItem value=''>Semua Lokasi</MenuItem>
            {ownSiteList?.map(site => (
              <MenuItem key={site.id} value={site.id}>
                {site.name}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        <FormControl fullWidth size='small'>
          <InputLabel>Periode</InputLabel>
          <Select
            onChange={handleChangePeriod}
            defaultValue={filterData.period}
            size='small'
            label='Periode'
            className='bg-white'
          >
            {optionsPeriodic?.map(periodic => (
              <MenuItem key={periodic.value} value={periodic.value}>
                {periodic.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </div>
      <Typography>
        Last Update:{' '}
        {mgOutSummary?.updatedAt
          ? formatDate(new Date(mgOutSummary?.updatedAt), 'dd/MM/yyyy HH:mm', { locale: id })
          : '-'}
      </Typography>
    </div>
  )
}

export default FilterData
