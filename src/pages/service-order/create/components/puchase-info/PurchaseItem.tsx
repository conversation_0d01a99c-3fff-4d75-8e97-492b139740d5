import { ServiceOrderDiscountType, ServiceOrderItemPayload, ServiceOrderTaxType } from '@/types/serviceOrderTypes'
import { TAX_PERCENTAGE } from '@/utils/constants'
import { toCurrency } from '@/utils/helper'
import React, { useMemo } from 'react'

interface PurchaseItemProps {
  itemData: ServiceOrderItemPayload
}

export const PurchaseItem: React.FC<PurchaseItemProps> = ({ itemData }) => {
  const {
    item: { name },
    serviceName,
    quantity,
    pricePerUnit,
    taxType,
    discountType,
    discountValue
  } = itemData

  const { totalItemDiscount, taxAmount } = useMemo(() => {
    let totalItemDiscount = 0
    let taxAmount = 0

    if (pricePerUnit) {
      let subTotalPrice = pricePerUnit * quantity
      taxAmount = taxType === ServiceOrderTaxType.EXCLUDE_TAX ? Math.round(subTotalPrice * TAX_PERCENTAGE) / 100 : 0

      if (discountType && discountValue) {
        if (discountType === ServiceOrderDiscountType.PERCENTAGE) {
          totalItemDiscount = Math.round(subTotalPrice * discountValue) / 100
        } else if (discountType === ServiceOrderDiscountType.FLAT) {
          totalItemDiscount = discountValue
        }
        subTotalPrice = Math.round((subTotalPrice - totalItemDiscount) * 100) / 100
        if (taxType === ServiceOrderTaxType.EXCLUDE_TAX) {
          taxAmount = Math.round(subTotalPrice * TAX_PERCENTAGE) / 100
        }
      }
    }

    return { totalItemDiscount, taxAmount }
  }, [pricePerUnit, quantity, taxType, discountType, discountValue])

  return (
    <div className='flex flex-col'>
      <div className='flex flex-wrap justify-between items-end w-full text-sm tracking-normal leading-none max-md:max-w-full'>
        <div className='flex flex-col text-gray-600 dark:text-inherit text-opacity-6 gap-1'>
          <div className='font-semibold'>{name}</div>
          <div className='mt-1'>
            {serviceName} {quantity}x {toCurrency(pricePerUnit)}
          </div>
        </div>
        <div className='text-gray-600 text-opacity-90'>{toCurrency((pricePerUnit ?? 0) * quantity)}</div>
      </div>
      {totalItemDiscount ? (
        <div className='flex flex-wrap justify-between items-start mt-2 w-full text-sm tracking-normal leading-none max-md:max-w-full'>
          <div className='font-semibold text-gray-600 dark:text-inherit text-opacity-60'>Diskon Item</div>
          <div className='text-red-500'>- {toCurrency(totalItemDiscount)}</div>
        </div>
      ) : null}
      {taxAmount ? (
        <div className='flex flex-wrap justify-between mt-2 w-full text-sm tracking-normal leading-none max-md:max-w-full'>
          <div className='font-semibold text-gray-600 dark:text-inherit text-opacity-60'>
            Pajak Jasa {TAX_PERCENTAGE}%
          </div>
          <div className='self-end text-gray-600 dark:text-inherit text-opacity-90'>{toCurrency(taxAmount)}</div>
        </div>
      ) : null}
    </div>
  )
}
