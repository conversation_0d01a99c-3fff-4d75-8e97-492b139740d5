// MUI Imports
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'
import { WarehouseDataType } from '@/types/appTypes'
import { Controller, useFormContext } from 'react-hook-form'
import { TextField } from '@mui/material'
import { PoPayload } from '../../config/types'

type Props = {
  warehouseData?: WarehouseDataType
}

const AdditionalInfoCard = ({ warehouseData }: Props) => {
  const {
    control,
    formState: { errors }
  } = useFormContext<PoPayload>()
  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Detil Lainnya</Typography>
        </div>
        <div className='flex flex-col gap-6'>
          <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
            <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
              <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60'>Departemen</small>
            </label>
            <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
            <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%]'>
              {warehouseData?.department?.name ?? '-'}
            </p>
          </div>
          <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
            <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
              <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60'>Lokasi</small>
            </label>
            <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
            <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%]'>
              {warehouseData?.site?.name ?? '-'}
            </p>
          </div>
          <Controller
            name='note'
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label='Catatan'
                variant='outlined'
                placeholder='Masukkkan Catatan'
                className='mbe-5'
                InputLabelProps={{
                  shrink: !!field.value
                }}
                {...(errors.note && { error: true, helperText: 'Catatan wajib diisi.' })}
              />
            )}
          />
        </div>
      </CardContent>
    </Card>
  )
}

export default AdditionalInfoCard
