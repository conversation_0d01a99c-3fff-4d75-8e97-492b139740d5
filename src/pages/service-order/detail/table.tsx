import { Icon<PERSON>utton, Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'
import { PoItemType } from '../config/types'

type DataTypeWithAction = PoItemType & {
  action?: string
}

type RowActionType = {
  onEdit?: (item: PoItemType) => void
  onRemove: (item: PoItemType) => void
  onView?: (item: PoItemType) => void
  onViewTracker?: (item: PoItemType) => void
}

// Column Definitions
const columnHelper = createColumnHelper<DataTypeWithAction>()

export const tableColumns = (rowAction: RowActionType, canRemove?: boolean) => [
  columnHelper.accessor('item.number', {
    header: 'Kode Barang',
    cell: ({ row }) => <Typography>{row.original.item?.number}</Typography>
  }),
  columnHelper.accessor('item.name', {
    header: '<PERSON><PERSON>',
    cell: ({ row }) => <Typography>{row.original.item?.name}</Typography>
  }),
  columnHelper.accessor('item.brandName', {
    header: 'Merk Barang',
    cell: ({ row }) => <Typography>{row.original.item?.brandName}</Typography>
  }),
  columnHelper.accessor('quantity', {
    header: 'QTY',
    cell: ({ row }) => (
      <Typography>
        {row.original.quantity} {row.original.quantityUnit}
      </Typography>
    )
  }),
  columnHelper.accessor('receivedQuantity', {
    header: 'Qty Diterima',
    cell: ({ row }) => {
      const receivedQty = row.original.isLargeUnit
        ? (row.original.receivedQuantity ?? 0) / row.original.largeUnitQuantity
        : row.original.receivedQuantity ?? 0
      return (
        <Typography>
          {parseFloat(receivedQty.toFixed(2))} {row.original.quantityUnit}
        </Typography>
      )
    },
    enableSorting: false
  }),
  ...(canRemove
    ? [
        columnHelper.accessor('action', {
          header: 'Action',
          cell: ({ row }) => (
            <div className='flex items-center gap-0.5'>
              <IconButton size='small' onClick={() => rowAction.onRemove(row.original)}>
                <i className='ic-baseline-delete-forever text-red-500' />
              </IconButton>
              {!!rowAction.onEdit && (
                <IconButton size='small' onClick={() => rowAction.onEdit(row.original)}>
                  <i className='ic-baseline-edit text-secondary' />
                </IconButton>
              )}
              {!!rowAction.onView && (
                <IconButton size='small' onClick={() => rowAction.onView(row.original)}>
                  <i className='ri-eye-line text-secondary' />
                </IconButton>
              )}
            </div>
          ),
          enableSorting: false
        })
      ]
    : [])
]
