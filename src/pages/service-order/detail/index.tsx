// MUI Imports

import Grid from '@mui/material/Grid'

// Type Imports
import { Breadcrumbs, Button, Chip, Typography } from '@mui/material'

import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { statusChipColor } from '../list/config/table'
import { Link, useParams } from 'react-router-dom'
import OwnerCard from '@/pages/material-request/detail/components/OwnerCard'
import ApprovalDetailCard from '@/pages/material-request/detail/components/ApprovalDetailCard'
import ActivityLogCard from './components/ActivityLogCard'
import { useSo } from '../context/SoContext'
import { useState } from 'react'
import { useMenu } from '@/components/menu/contexts/menuContext'
import { useUploadImage } from '@/api/services/file/mutation'
import { CancellationType } from '@/types/apps/ledgerTypes'
import { toast } from 'react-toastify'
import CancellationSection from '@/pages/service-order/detail/components/CancellationSection'
import { fileNameFromUrl } from '@/utils/helper'
import { EditApproverInput } from '@/components/dialogs/edit-approver-dialog'
import DocNumberCard from '@/pages/material-request/mr-in/component/DocNumberCard'
import Permission from '@/core/components/Permission'
import { useAuth } from '@/contexts/AuthContext'
import CancelSoDialog from '@/components/dialogs/cancel-so-dialog'
import { useCancelSo, useCloseSo, useUpdateSoApprover } from '@/api/services/service-order/mutation'
import { serviceOrderStatusOptions } from '../list/config/utils'
import { ServiceOrderStatus } from '@/types/serviceOrderTypes'
import AdditionalInfoCard from './components/AdditionalInfoCard'
import SegmentCard from './components/SegmentCard'
import PurchaseDetailCard from './components/PurchaseDetailCard'
import RequestDetailCard from './components/RequestDetailCard'
import qrGenerator from '@/utils/qrGenerator'
import { pdf } from '@react-pdf/renderer'
import PdfDocument from './components/PdfDocument'
import { saveAs } from 'file-saver'
import { useQuery } from '@tanstack/react-query'
import CompanyQueryMethods, { VENDOR_QUERY_KEY } from '@/api/services/company/query'

const SoDetailPage = () => {
  const { ownSiteList } = useAuth()
  const { setConfirmState } = useMenu()
  const { srId } = useParams()
  const { soData, logList, fetchLogList, setSelectedSo, canView, fetchSoData, canRemove } = useSo()

  const { mutateAsync: uploadMutate, isLoading: uploadLoading } = useUploadImage()
  const { mutate: cancelMutate, isLoading: cancelLoading } = useCancelSo()
  const { mutate: closeMutate } = useCloseSo()

  const [cancellationOpen, setCancellationOpen] = useState(false)

  const { mutate: updateApproverMutate, isLoading: updateApproverLoading } = useUpdateSoApprover()

  const { data: vendorData } = useQuery({
    enabled: !!soData?.vendorId,
    queryKey: [VENDOR_QUERY_KEY, soData?.vendorId],
    queryFn: () => CompanyQueryMethods.getVendor(soData?.vendorId)
  })

  const handleUpdateApprover = (formData: EditApproverInput) => {
    updateApproverMutate(
      {
        soId: soData?.id,
        approvalId: formData.approvalId,
        ...formData
      },
      {
        onSuccess: () => {
          toast.success('Penerima Pengajuan berhasil diganti')
          fetchSoData()
          fetchLogList()
        },
        onError: error => {
          const message = error?.message
          if (message) {
            toast.error(message)
          } else {
            toast.error('Oops telah terjadi kesalahan, silahkan coba beberapa saat lagi.')
          }
        }
      }
    )
  }

  const handleSubmitSoCancelation = ({ proofFile, proofFileName, ...cancellationData }: CancellationType) => {
    uploadMutate(
      {
        fieldName: `item_image_so_cancelation_proof`,
        file: proofFile,
        scope: 'public-image',
        fileName: proofFileName
      },
      {
        onSuccess: response => {
          cancelMutate(
            {
              soId: soData?.id ?? '',
              cancelationProofUploadId: response.data.id,
              ...cancellationData
            },
            {
              onSuccess: () => {
                setSelectedSo({ soId: soData?.id ?? '', isCancelation: true })
                fetchSoData()
                fetchLogList()
                toast.success('Pembatalan Service Order berhasil diajukan')
                setCancellationOpen(false)
              }
            }
          )
        }
      }
    )
  }

  const handleCloseSo = () => {
    setConfirmState({
      open: true,
      title: 'Tutup Service Order',
      content: 'Apakah kamu yakin akan menutup Service Order ini? Action ini tidak bisa diubah',
      confirmText: 'Tutup Service Order',
      confirmColor: 'primary',
      onConfirm: () => {
        closeMutate(soData?.id, {
          onSuccess: () => {
            fetchSoData()
            toast.success('Service Order berhasil ditutup')
            setTimeout(() => {
              fetchLogList()
            }, 500)
          }
        })
      }
    })
  }

  const handleExport = async () => {
    qrGenerator(`${location.origin}/validate/so/${soData?.id}?signature=${soData?.signature}`).then(async qr => {
      const blob = await pdf(
        <PdfDocument
          soData={{
            ...soData,
            site: ownSiteList.find(site => site.id === soData?.siteId)
          }}
          vendorData={vendorData}
          qrCode={qr}
        />
      ).toBlob()
      const filename = soData?.number + '.pdf'
      saveAs(blob, filename)
    })
  }

  const handlePrint = async () => {
    qrGenerator(`${location.origin}/validate/service-order/${soData?.id}?signature=${soData?.signature}`).then(
      async qr => {
        const blob = await pdf(
          <PdfDocument
            vendorData={vendorData}
            soData={{
              ...soData,
              site: ownSiteList.find(site => site.id === soData?.siteId)
            }}
            qrCode={qr}
          />
        ).toBlob()
        const url = URL.createObjectURL(blob)
        const printWindow = window.open(url, '_blank')
        printWindow.onload = () => {
          printWindow.print()
          printWindow.onafterprint = () => {
            printWindow.close()
          }
        }
      }
    )
  }

  return (
    <>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          {srId ? (
            <Breadcrumbs aria-label='breadcrumb'>
              <Link to='#' replace>
                <Typography color='var(--mui-palette-text-disabled)'>Service Order</Typography>
              </Link>
              <Link to='/service-order/sr-list' replace>
                <Typography color='var(--mui-palette-text-disabled)'>Service Request Masuk</Typography>
              </Link>
              <Link to={`/service-order/sr-list/${srId}`} replace>
                <Typography color='var(--mui-palette-text-disabled)'>Detil Service Request</Typography>
              </Link>
              <Typography>Detil Service Order</Typography>
            </Breadcrumbs>
          ) : (
            <Breadcrumbs aria-label='breadcrumb'>
              <Link to='#' replace>
                <Typography color='var(--mui-palette-text-disabled)'>Service Order</Typography>
              </Link>
              <Link to='/service-order/list' replace>
                <Typography color='var(--mui-palette-text-disabled)'>Service Order Terbuat</Typography>
              </Link>
              <Typography>Detil Service Order</Typography>
            </Breadcrumbs>
          )}
        </Grid>
        <Grid item xs={12}>
          <div className='flex justify-between items-end flex-col sm:flex-row max-sm:items-center gap-2'>
            <div className='flex flex-col'>
              <div className='flex items-center gap-2 flex-col sm:flex-row max-sm:text-center'>
                <Typography variant='h4'>No. SO: {soData?.number}</Typography>
                <Chip
                  label={serviceOrderStatusOptions.find(option => option.value === soData?.status)?.label}
                  color={statusChipColor[soData?.status]?.color}
                  variant='tonal'
                  size='small'
                />
              </div>
              <Typography className='max-sm:text-center max-sm:mt-2'>
                {formatDate(soData?.createdAt ?? Date.now(), 'eeee, dd/MM/yyyy, HH:mm', { locale: id })}
              </Typography>
            </div>
            <div className='flex gap-2 flex-col sm:flex-row is-full sm:is-auto'>
              {soData?.vendorId &&
                (soData?.status === ServiceOrderStatus.APPROVED || soData?.status === ServiceOrderStatus.CLOSED) && (
                  <Permission permission={['service-order.create']}>
                    <Button
                      variant='contained'
                      onClick={handleExport}
                      startIcon={<i className='ri-download-2-line' />}
                      className='is-full sm:is-auto'
                    >
                      Unduh Service Order
                    </Button>
                    <Button
                      variant='outlined'
                      onClick={handlePrint}
                      startIcon={<i className='ri-printer-line' />}
                      className='is-full sm:is-auto'
                    >
                      Cetak Service Order
                    </Button>
                  </Permission>
                )}
              {soData?.status === ServiceOrderStatus.PROCESSED && canRemove && (
                <Button
                  color='error'
                  variant='contained'
                  className='is-full sm:is-auto'
                  onClick={() => setCancellationOpen(true)}
                >
                  Batalkan Service Order
                </Button>
              )}
              {soData?.status === ServiceOrderStatus.APPROVED && !soData?.isClosed && (
                <Permission permission={['service-order.create']}>
                  <Button color='warning' variant='contained' className='is-full sm:is-auto' onClick={handleCloseSo}>
                    Tutup Service Order
                  </Button>
                </Permission>
              )}
            </div>
          </div>
        </Grid>
        {soData?.items ? (
          <Grid item xs={12}>
            <SegmentCard />
          </Grid>
        ) : null}
        <Grid item xs={12} md={6}>
          <Grid container spacing={4}>
            <Grid item xs={12}>
              <DocNumberCard docType='SR' warehouseDoc={soData?.serviceRequisition} />
            </Grid>
            <Grid item xs={12}>
              <RequestDetailCard vendorData={vendorData} />
            </Grid>
            <Grid item xs={12}>
              <AdditionalInfoCard />
            </Grid>
          </Grid>
        </Grid>
        <Grid item xs={12} md={6}>
          <Grid container spacing={4}>
            {soData?.cancelationType ? (
              <Grid item xs={12}>
                <CancellationSection
                  cancellation={{
                    cancelationNote: soData?.cancelationNote,
                    cancelationType: soData?.cancelationType,
                    proofFile: soData?.cancelationProofUrl,
                    proofFileName: fileNameFromUrl(soData?.cancelationProofUrl ?? ''),
                    approvals: [],
                    approvers: soData?.approvals?.filter(approval => approval.isCancelation) ?? []
                  }}
                />
              </Grid>
            ) : null}
            {soData && canView ? (
              <Grid item xs={12}>
                <PurchaseDetailCard rawSoData={soData} />
              </Grid>
            ) : null}
            {soData?.status !== ServiceOrderStatus.CANCELED &&
            (soData?.approvals?.filter(approval => !approval.isCancelation).length ?? 0) > 0 ? (
              <Grid item xs={12}>
                <ApprovalDetailCard
                  approvalList={soData?.approvals ?? []}
                  handleUpdateApprover={handleUpdateApprover}
                  updateApproverLoading={updateApproverLoading}
                />
              </Grid>
            ) : null}
            <Grid item xs={12}>
              <ActivityLogCard logList={logList} />
            </Grid>
            <Grid item xs={12}>
              <OwnerCard user={soData?.createdByUser} />
            </Grid>
          </Grid>
        </Grid>
      </Grid>
      {cancellationOpen && (
        <CancelSoDialog
          open={cancellationOpen}
          setOpen={setCancellationOpen}
          onSubmit={handleSubmitSoCancelation}
          isLoading={uploadLoading || cancelLoading}
        />
      )}
    </>
  )
}

export default SoDetailPage
