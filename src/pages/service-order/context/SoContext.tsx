import { create<PERSON>ontext, ReactN<PERSON>, useContext, useEffect, useState } from 'react'
import {
  QueryObserverResult,
  RefetchOptions,
  RefetchQueryFilters,
  UseMutateAsyncFunction,
  UseMutateFunction,
  useQuery
} from '@tanstack/react-query'
import {
  ServiceOrderLogType,
  ServiceOrderParams,
  ServiceOrderPayload,
  ServiceOrder,
  ServiceOrderLog
} from '@/types/serviceOrderTypes'
import usePartialState from '@/core/hooks/usePartialState'
import { ApiResponse, ListResponse } from '@/types/api'
import { defaultListData } from '@/api/queryClient'
import { useParams } from 'react-router-dom'
import { usePathname } from '@/routes/hooks'
import { useAddSo } from '@/api/services/service-order/mutation'
import ServiceOrderQueryMethods, {
  SERVICE_ORDER_LIST_QUERY_KEY,
  SERVICE_ORDER_LOG_LIST_QUERY_KEY,
  SERVICE_ORDER_QUERY_KEY
} from '@/api/services/service-order/query'
import { useAuth } from '@/contexts/AuthContext'
import { useUploadImage } from '@/api/services/file/mutation'
import { FileType } from '@/types/fileTypes'
import { UploadPayload } from '@/types/payload'
import { DefaultApprovalScope } from '@/pages/user/default-approval/config/enum'

interface SoContextProps {
  showLoading: boolean
  soListResponse: ListResponse<ServiceOrder>
  sosParams: ServiceOrderParams
  setSosParams: React.Dispatch<React.SetStateAction<ServiceOrderParams>>
  fetchSoList: <TPageData>(
    options?: RefetchOptions & RefetchQueryFilters<TPageData>
  ) => Promise<QueryObserverResult<ListResponse<ServiceOrder>, unknown>>
  soData: ServiceOrder
  clearSoData: () => void
  selectedSoId?: string
  setSelectedSo: React.Dispatch<
    React.SetStateAction<{
      soId?: string
      isCancelation?: boolean
    }>
  >
  setPartialSosParams: (fieldName: string, value: any) => void
  fetchSoData: <TPageData>(
    options?: RefetchOptions & RefetchQueryFilters<TPageData>
  ) => Promise<QueryObserverResult<ServiceOrder, unknown>>
  uploadMutate: UseMutateAsyncFunction<ApiResponse<FileType>, Error, UploadPayload, void>
  createSoMutate: UseMutateFunction<ApiResponse<ServiceOrder>, Error, ServiceOrderPayload, void>
  logList: ServiceOrderLog[]
  fetchLogList: <TPageData>(
    options?: RefetchOptions & RefetchQueryFilters<TPageData>
  ) => Promise<QueryObserverResult<ServiceOrderLog[], unknown>>
  refreshData: () => void
  canUpdate: boolean
  canView: boolean
  canRemove: boolean
}

export const SoContext = createContext<SoContextProps>({} as SoContextProps)

interface SoContextProviderProps {
  children: ReactNode
}

export const useSo = () => {
  return useContext(SoContext)
}

export function SoContextProvider({ children }: SoContextProviderProps) {
  const pathname = usePathname()
  const { accountPermissions, userProfile, ownSiteList } = useAuth()
  const { soId, srId } = useParams()
  const [{ soId: selectedSoId, isCancelation }, setSelectedSo] = useState<{ soId?: string; isCancelation?: boolean }>({
    soId
  })

  const [sosParams, setPartialSosParams, setSosParams] = usePartialState<ServiceOrderParams>({
    limit: 10,
    page: 1
  })

  const { mutateAsync: uploadMutate, isLoading: uploadLoading } = useUploadImage()
  const { mutate: createSoMutate, isLoading: createSoLoading } = useAddSo()

  const canUpdate = accountPermissions.includes(`${DefaultApprovalScope.ServiceOrder}.update`)
  const canView = accountPermissions.some(permission => permission.startsWith('service-order'))
  const isApprovalPage = pathname.includes('/service-order/approval')

  const {
    data: soData,
    refetch: fetchSoData,
    isFetching: fetchSoDataLoading,
    remove: removeSoData
  } = useQuery({
    enabled: !!selectedSoId,
    queryKey: [SERVICE_ORDER_QUERY_KEY, selectedSoId, isCancelation],
    queryFn: () => ServiceOrderQueryMethods.getSo(selectedSoId, isCancelation)
  })

  const canRemove = soData?.isEditable && canUpdate

  const {
    data: logList,
    refetch: fetchLogList,
    remove: removeSoLogList
  } = useQuery({
    enabled: !!selectedSoId,
    queryKey: [SERVICE_ORDER_LOG_LIST_QUERY_KEY, selectedSoId],
    queryFn: () => ServiceOrderQueryMethods.getSoLogList(selectedSoId, { limit: 10000 })
  })

  const {
    data: soListResponse,
    refetch: fetchSoList,
    isFetching: fetchSosLoading
  } = useQuery({
    enabled: (accountPermissions?.length ?? 0) > 0 && !pathname.includes('sr-list'),
    queryKey: [SERVICE_ORDER_LIST_QUERY_KEY, JSON.stringify(sosParams), canUpdate, isApprovalPage],
    queryFn: () => {
      const siteIdsUser = userProfile?.sites
        ?.filter(site => site.type === 'WORKSHOP')
        .map(site => site.id)
        .join(',')
      const { search, status, startDate, endDate, siteIds, userStatus, ...params } = sosParams
      const payload = {
        ...(search && { search }),
        ...(status && !isApprovalPage && { status }),
        ...(userStatus && { userStatus }),
        ...(siteIds ? { siteIds } : { siteIds: siteIdsUser }),
        ...(!!startDate && !!endDate && { startDate, endDate }),
        ...params
      }
      if (isApprovalPage) {
        return ServiceOrderQueryMethods.getToMeSoList(payload)
      }
      if (canUpdate) {
        return ServiceOrderQueryMethods.getSoList(payload)
      } else {
        return ServiceOrderQueryMethods.getByMeSoList(payload)
      }
    },
    placeholderData: defaultListData as ListResponse<ServiceOrder>
  })

  const clearSoData = () => {
    removeSoData()
    removeSoLogList()
    setSelectedSo({ soId: undefined })
  }

  const refreshData = () => {
    fetchSoData()
    fetchLogList()
    fetchSoList()
  }

  useEffect(() => {
    setSosParams({
      ...sosParams,
      siteIds: ownSiteList.map(site => site.id).join(',')
    })
  }, [ownSiteList])

  const value = {
    soListResponse: soListResponse ?? defaultListData,
    showLoading: uploadLoading || createSoLoading,
    fetchSoList,
    sosParams,
    setPartialSosParams,
    setSosParams,
    soData,
    selectedSoId,
    setSelectedSo,
    clearSoData,
    fetchSoData,
    uploadMutate,
    createSoMutate,
    logList,
    fetchLogList,
    refreshData,
    canUpdate,
    canView,
    canRemove
  }

  return (
    <SoContext.Provider value={value}>
      <>{children}</>
    </SoContext.Provider>
  )
}
