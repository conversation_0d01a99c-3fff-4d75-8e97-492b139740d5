import { create<PERSON>ontext, ReactN<PERSON>, useContext, useEffect, useState } from 'react'
import { Query, QueryObserverResult, RefetchOptions, RefetchQueryFilters, useQuery } from '@tanstack/react-query'
import usePartialState from '@/core/hooks/usePartialState'
import { ListResponse } from '@/types/api'
import { defaultListData } from '@/api/queryClient'
import { useParams } from 'react-router-dom'
import { usePathname } from '@/routes/hooks'
import {
  ServiceRequisition,
  ServiceRequisitionLog,
  ServiceRequisitionParams,
  ServiceRequisitionStatus
} from '@/types/serviceRequisitionsTypes'
import { QueryFn } from '@/types/alias'
import { WorkOrderType } from '@/types/woTypes'
import ServiceRequisitionQueryMethods, { SR_LOG_LIST_QUERY_KEY } from '@/api/services/service-requisitions/query'
import { SR_LIST_QUERY_KEY, SR_QUERY_KEY } from '@/api/services/sr/query'
import { ServiceOrder, ServiceOrderItem, ServiceOrderParams } from '@/types/serviceOrderTypes'
import ServiceOrderQueryMethods, { SERVICE_ORDER_LIST_QUERY_KEY } from '@/api/services/service-order/query'
import { WO_QUERY_DETAIL_KEY } from '@/api/services/rnm/service'
import RnMQueryMethods from '@/api/services/rnm/query'
import { useAuth } from '@/contexts/AuthContext'

interface SrListContextProps {
  showLoading: boolean
  srListResponse: ListResponse<ServiceRequisition>
  srsParams: ServiceRequisitionParams
  setSrsParams: React.Dispatch<React.SetStateAction<ServiceRequisitionParams>>
  fetchSrList: <TPageData>(
    options?: RefetchOptions & RefetchQueryFilters<TPageData>
  ) => Promise<QueryObserverResult<ListResponse<ServiceRequisition>, unknown>>
  srData: ServiceRequisition
  clearSrData: () => void
  selectedSrId?: string
  setSelectedSrId: React.Dispatch<React.SetStateAction<string>>
  setPartialSrsParams: (fieldName: string, value: any) => void
  fetchSrData: <TPageData>(
    options?: RefetchOptions & RefetchQueryFilters<TPageData>
  ) => Promise<QueryObserverResult<ServiceRequisition, unknown>>
  logList: ServiceRequisitionLog[]
  fetchLogList: <TPageData>(
    options?: RefetchOptions & RefetchQueryFilters<TPageData>
  ) => Promise<QueryObserverResult<ServiceRequisitionLog[], unknown>>
  refreshData: () => void
  fetchSoList: <TPageData>(
    options?: RefetchOptions & RefetchQueryFilters<TPageData>
  ) => Promise<QueryObserverResult<ListResponse<ServiceOrder>, unknown>>
  soListResponse: ListResponse<ServiceOrder>
  setPartialSosParams: (fieldName: string, value: any) => void
  workOrderData?: WorkOrderType
  fetchWorkOrderData: QueryFn<WorkOrderType>
  sosParams: ServiceOrderParams
  selectedSrList: ServiceRequisition[]
  setSelectedSrList: React.Dispatch<React.SetStateAction<ServiceRequisition[]>>
  srDataList?: ServiceRequisition[]
  fetchSrDataList: <TPageData>(
    options?: RefetchOptions & RefetchQueryFilters<TPageData>
  ) => Promise<QueryObserverResult<ServiceRequisition[], unknown>>
  soItems: ServiceOrderItem[]
  setSoItems: React.Dispatch<React.SetStateAction<ServiceOrderItem[]>>
}

export const SrListContext = createContext<SrListContextProps>({} as SrListContextProps)

interface SrListContextProviderProps {
  children: ReactNode
}

export const useSrList = () => {
  return useContext(SrListContext)
}

export function SrListContextProvider({ children }: SrListContextProviderProps) {
  const { srId: srIdParams } = useParams()
  const { ownSiteList } = useAuth()
  const [soItems, setSoItems] = useState<ServiceOrderItem[]>([])
  const [selectedSrId, setSelectedSrId] = useState<string>(srIdParams)

  const [selectedSrList, setSelectedSrList] = useState<ServiceRequisition[]>([])

  const [srsParams, setPartialSrsParams, setSrsParams] = usePartialState<ServiceRequisitionParams>({
    limit: 10,
    page: 1
  })

  const [sosParams, setPartialSosParams, setSosParams] = usePartialState<ServiceOrderParams>({
    limit: 10,
    page: 1,
    serviceRequisitionId: srIdParams
  })

  const {
    data: logList,
    refetch: fetchLogList,
    remove: removeSrLogList
  } = useQuery({
    enabled: !!selectedSrId,
    queryKey: [SR_LOG_LIST_QUERY_KEY, selectedSrId],
    queryFn: () => ServiceRequisitionQueryMethods.getSrLogList(selectedSrId, { limit: 10000 })
  })

  const {
    data: srData,
    refetch: fetchSrData,
    isFetching: fetchSrDataLoading,
    remove: removeSrData
  } = useQuery({
    enabled: !!selectedSrId,
    queryKey: [SR_QUERY_KEY, selectedSrId],
    queryFn: () => ServiceRequisitionQueryMethods.getSr(selectedSrId)
  })

  const {
    data: workOrderData,
    refetch: fetchWorkOrderData,
    isFetching: fetchWorkOrderDataLoading,
    remove: removeWorkOrderData
  } = useQuery({
    enabled: !!srData?.workOrderId,
    queryKey: [WO_QUERY_DETAIL_KEY, srData?.workOrderId],
    queryFn: () => RnMQueryMethods.getWoDetail(srData?.workOrderId)
  })

  const {
    data: srListResponse,
    refetch: fetchSrList,
    isFetching: fetchSrsLoading
  } = useQuery({
    queryKey: [SR_LIST_QUERY_KEY, JSON.stringify(srsParams), 'sr-list'],
    queryFn: () => {
      const { search, status, startDate, endDate, ...params } = srsParams
      const payload = {
        status: ServiceRequisitionStatus.APPROVED,
        isClosed: false,
        ...(search && { search }),
        ...(!!startDate && !!endDate && { startDate, endDate }),
        ...params
      }
      return ServiceRequisitionQueryMethods.getSrList(payload)
    },
    placeholderData: defaultListData as ListResponse<ServiceRequisition>
  })

  const {
    data: soListResponse,
    refetch: fetchSoList,
    isFetching: fetchSosLoading
  } = useQuery({
    enabled: !!selectedSrId,
    queryKey: [SERVICE_ORDER_LIST_QUERY_KEY, JSON.stringify(sosParams), selectedSrId],
    queryFn: () => {
      return ServiceOrderQueryMethods.getSoList({
        ...sosParams,
        serviceRequisitionId: selectedSrId
      })
    },
    placeholderData: defaultListData as ListResponse<ServiceOrder>
  })

  const {
    data: srDataList,
    refetch: fetchSrDataList,
    remove: removeSrDataList
  } = useQuery({
    enabled: selectedSrList?.length > 0,
    queryKey: [SR_QUERY_KEY, JSON.stringify(selectedSrList)],
    queryFn: () => {
      return Promise.all(
        selectedSrList?.map(async sr => {
          return await ServiceRequisitionQueryMethods.getSr(sr.id)
        })
      )
    }
  })

  const showLoading = fetchSrDataLoading || fetchSrsLoading || fetchSosLoading

  const clearSrData = () => {
    removeSrData()
    removeSrLogList()
    // removeWorkOrderData()
    removeSrDataList()
  }

  const refreshData = () => {
    fetchSrList()
    fetchSrData()
    fetchLogList()
    // fetchWorkOrderData()
    fetchSoList()
  }

  useEffect(() => {
    if (srIdParams) {
      setSelectedSrId(srIdParams)
    }
  }, [srIdParams])

  useEffect(() => {
    setSrsParams({
      ...srsParams,
      siteIds: ownSiteList.map(site => site.id).join(',')
    })
  }, [ownSiteList])

  return (
    <SrListContext.Provider
      value={{
        showLoading,
        srListResponse,
        srsParams,
        setSrsParams,
        fetchSrList,
        srData,
        clearSrData,
        selectedSrId,
        setSelectedSrId,
        setPartialSrsParams,
        fetchSrData,
        logList,
        fetchLogList,
        refreshData,
        fetchSoList,
        soListResponse,
        setPartialSosParams,
        workOrderData,
        fetchWorkOrderData,
        sosParams,
        selectedSrList,
        setSelectedSrList,
        srDataList,
        fetchSrDataList,
        soItems,
        setSoItems
        // multiSrSoList,
        // fetchMultiSrSoList
      }}
    >
      {children}
    </SrListContext.Provider>
  )
}
