import { ServiceOrderApprovalStatus, ServiceOrderStatus } from '@/types/serviceOrderTypes'

export const serviceOrderStatusOptions = [
  {
    value: ServiceOrderStatus.PROCESSED,
    label: 'Diproses'
  },
  {
    value: ServiceOrderStatus.APPROVED,
    label: 'Disetuju<PERSON>'
  },
  {
    value: ServiceOrderStatus.REJECTED,
    label: '<PERSON><PERSON><PERSON>'
  },
  {
    value: ServiceOrderStatus.CANCELED,
    label: '<PERSON><PERSON><PERSON><PERSON>'
  },
  {
    value: ServiceOrderStatus.CLOSED,
    label: '<PERSON><PERSON><PERSON>'
  },
  {
    value: ServiceOrderStatus.CANCEL_REQUESTED,
    label: 'Permintaan Pembatalan'
  }
]

export const serviceOrderUserStatusOptions = [
  {
    value: ServiceOrderApprovalStatus.WAITING,
    label: 'Menunggu'
  },
  {
    value: ServiceOrderApprovalStatus.APPROVED,
    label: 'Disetujui'
  },
  {
    value: ServiceOrderApprovalStatus.REJECTED,
    label: '<PERSON><PERSON><PERSON>'
  }
]
