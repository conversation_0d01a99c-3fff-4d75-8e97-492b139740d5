import { ServiceOrderCancelationType } from '@/types/serviceOrderTypes'
import {
  PurchaseOrderStatus,
  PurchaseOrderQuantityUnit,
  PurchaseOrderApprovalStatus,
  PurchaseOrderLogType,
  PurchaseOrderLogStatus,
  PurchaseOrderCancelationType,
  PurchaseOrderTaxType,
  PurchaseOrderDiscountType,
  PurchaseOrderPaymentMethod
} from './enum'

export const poStatusOptions = [
  { value: PurchaseOrderStatus.PROCESSED, label: 'Diproses' },
  { value: PurchaseOrderStatus.APPROVED, label: 'Disetujui' },
  { value: PurchaseOrderStatus.REJECTED, label: '<PERSON><PERSON><PERSON>' },
  { value: PurchaseOrderStatus.CANCELED, label: '<PERSON><PERSON><PERSON><PERSON>' },
  { value: PurchaseOrderStatus.CANCEL_REQUESTED, label: '<PERSON><PERSON><PERSON><PERSON>' },
  { value: PurchaseOrderStatus.CLOSED, label: 'Selesai' }
]

export const quantityUnitOptions = [
  { value: PurchaseOrderQuantityUnit.PCS, label: 'Pcs' },
  { value: PurchaseOrderQuantityUnit.BOX, label: 'Box' },
  { value: PurchaseOrderQuantityUnit.GALON, label: 'Galon' },
  { value: PurchaseOrderQuantityUnit.LITER, label: 'Liter' },
  { value: PurchaseOrderQuantityUnit.DRUM, label: 'Drum' },
  { value: PurchaseOrderQuantityUnit.METER, label: 'Meter' },
  { value: PurchaseOrderQuantityUnit.CENTIMETER, label: 'Centimeter' },
  { value: PurchaseOrderQuantityUnit.MILIMETER, label: 'Milimeter' }
]

export const approvalStatusOptions = [
  { value: PurchaseOrderApprovalStatus.PENDING, label: 'Pending' },
  { value: PurchaseOrderApprovalStatus.WAITING, label: 'Menunggu' },
  { value: PurchaseOrderApprovalStatus.APPROVED, label: 'Disetujui' },
  { value: PurchaseOrderApprovalStatus.REJECTED, label: 'Ditolak' }
]

export const logTypeOptions = [
  { value: PurchaseOrderLogType.ACTIVITY, label: 'Aktivitas' },
  { value: PurchaseOrderLogType.ITEM_FLOW, label: 'Alur Item' }
]

export const taxTypeOptions = [
  { value: PurchaseOrderTaxType.NON_TAX, label: 'Non Pajak' },
  { value: PurchaseOrderTaxType.INCLUDE_TAX, label: 'Termasuk Pajak' },
  { value: PurchaseOrderTaxType.EXCLUDE_TAX, label: 'Tidak Termasuk Pajak' }
]

export const discountTypeOptions = [
  { value: PurchaseOrderDiscountType.PERCENTAGE, label: 'Persentase' },
  { value: PurchaseOrderDiscountType.FLAT, label: 'Nominal' }
]

export const paymentMethodOptions = [
  { value: PurchaseOrderPaymentMethod.TERM, label: 'Tempo' },
  { value: PurchaseOrderPaymentMethod.CASH, label: 'Tunai' }
]

export const cancelationTypeOptions = [
  { value: ServiceOrderCancelationType.CANNOT_REPAIR, label: 'Barang Tidak Dapat Diperbaiki' }
]
