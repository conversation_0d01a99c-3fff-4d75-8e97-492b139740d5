import { useMemo, useState } from 'react'
import DebouncedInput from '@/components/DebounceInput'
import { Card, CardContent, Button, Typography } from '@mui/material'
import { useAsset } from '../../context/AssetContext'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import { tableColumns } from '../config/table'
import Table from '@/components/table'
import ImportDialog from '@/components/dialogs/import-dialog'
import { ExportImportScope } from '@/types/exportImportTypes'
import { useRouter } from '@/routes/hooks'

const AssetList = () => {
  const {
    assetParams,
    setAssetParams,
    setPartialAssetParams,
    assetResponse: { items, totalItems, totalPages },
    handleDelete,
    handleDetail
  } = useAsset()
  const router = useRouter()
  const { search, limit, page } = assetParams

  const [importDialogOpen, setImportDialogOpen] = useState(false)

  const tableOptions = useMemo(
    () => ({
      data: items ?? [],
      columns: tableColumns({
        detail: data => router.push(`/accounting/assets/list/${data.id}`),
        delete: id => handleDelete(id)
      }),
      initialState: {
        pagination: {
          pageSize: limit ?? 10,
          pageIndex: page - 1
        }
      },
      manualPagination: true,
      rowCount: totalItems,
      pageCount: totalPages,
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [items, totalItems, totalPages, limit, page]
  )

  const table = useReactTable<any>(tableOptions)

  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-2'>
          <div className='flex flex-col md:flex-row gap-4 justify-between items-center'>
            <div className='flex gap-4 items-center flex-col sm:flex-row is-full sm:is-auto'>
              <DebouncedInput
                value={search}
                onChange={value => setAssetParams(prev => ({ ...prev, page: 1, search: value as string }))}
                placeholder='Cari'
                className='is-full sm:is-auto md:max-w-[240px]'
              />
            </div>
            <div className='space-x-2 is-full sm:is-auto'>
              {/* <Button
                className='is-full sm:is-auto'
                startIcon={<i className='ri-upload-2-line' />}
                color='secondary'
                variant='outlined'
              >
                Ekspor
              </Button>
              <Button
                color='primary'
                variant='outlined'
                startIcon={<i className='mdi-file-document-outline' />}
                className='is-full sm:is-auto'
                onClick={() => setImportDialogOpen(true)}
              >
                Impor List
              </Button> */}
              <Button
                className='is-full sm:is-auto'
                startIcon={<i className='ri-add-circle-line' />}
                color='primary'
                variant='contained'
                onClick={() => router.push('/accounting/assets/form')}
              >
                Tambah Aset
              </Button>
            </div>
          </div>
        </CardContent>
        <Table
          table={table}
          emptyLabel={
            <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
              <Typography>Belum ada Aset</Typography>
              <Typography className='text-sm text-gray-400'>
                Semua aset milik perusahaan kamu akan ditampilkan di sini
              </Typography>
            </td>
          }
          onRowsPerPageChange={pageSize => {
            if (pageSize > totalItems) {
              setAssetParams(prev => ({ ...prev, limit: totalItems, page: 1 }))
            } else {
              setPartialAssetParams('limit', pageSize)
              const maxPage = Math.ceil(totalItems / pageSize)
              if (page > maxPage) {
                setAssetParams(prev => ({ ...prev, page: maxPage }))
              }
            }
          }}
          onPageChange={pageIndex => setAssetParams(prev => ({ ...prev, page: pageIndex }))}
        />
      </Card>
      {importDialogOpen && (
        <ImportDialog
          open={importDialogOpen}
          scope={ExportImportScope.UNIT}
          onSubmit={console.log}
          setOpen={() => setImportDialogOpen(!importDialogOpen)}
          type='ITEM'
        />
      )}
    </>
  )
}

export default AssetList
