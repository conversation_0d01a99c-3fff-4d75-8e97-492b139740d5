// MUI Imports
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'
import { Grid, Box, Button } from '@mui/material'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'

import { AssetType } from '@/types/assetTypes'
import { toCurrency } from '@/utils/helper'
import { AssetDepreciationMethods, AssetStatus, depreciationMethodOptions } from '../../config/enum'
import { useMenu } from '@/components/menu/contexts/menuContext'
import { useDisposeAsset } from '@/api/services/asset-management/mutation'
import LoadingButton from '@mui/lab/LoadingButton'
import { useAsset } from '../../context/AssetContext'
import { toast } from 'react-toastify'
import { useState } from 'react'
import AssetValueLogsDialog from '@/components/dialogs/asset-value-logs-dialog'

interface ValueCardProps {
  assetData: AssetType
}

const ValueCard = ({ assetData }: ValueCardProps) => {
  const { setConfirmState } = useMenu()
  const { refetchAssetDetail } = useAsset()
  const { mutateAsync, isLoading } = useDisposeAsset()

  const [openValueLogDialog, setOpenValueLogDialog] = useState(false)

  const handleDispose = () => {
    setConfirmState({
      open: true,
      title: 'Tandai Untuk Dispose',
      content:
        'Apakah kamu yakin akan menandai aset ini untuk Dispose? Perhitungan penyusutan akan dihentikan sampai penanda dibatalkan atau aset dijual.',
      onConfirm: async () => {
        await mutateAsync({
          id: assetData?.id
        })
        toast.success('Aset berhasil didispose')
        setConfirmState(current => ({ ...current, open: false }))
        refetchAssetDetail()
      },
      confirmText: 'Tandai untuk Dispose',
      confirmColor: 'error'
    })
  }
  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Card>
          <CardContent className='flex flex-col gap-6'>
            <>
              <div className='flex justify-between items-center'>
                <Typography variant='h5'>Nilai Aset</Typography>
                <Button size='small' variant='outlined' onClick={() => setOpenValueLogDialog(true)}>
                  Riwayat Perubahan
                </Button>
              </div>
              <div className='flex flex-col gap-6'>
                <div className='bg-gray-100 p-4 flex rounded-lg'>
                  <div className='flex flex-col gap-1 flex-1'>
                    <Typography className='text-textPrimary'>Nilai Aset Saat Ini</Typography>
                    <Typography className='text-primary text-lg font-medium'>
                      {toCurrency(assetData?.value, false, assetData?.currency?.code)}
                    </Typography>
                  </div>
                </div>

                <div className='flex items-center'>
                  <div className='flex flex-col gap-1 items-start relative w-full bg-transparent flex-1'>
                    <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                      <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                        Metode Penyusutan
                      </small>
                    </label>
                    <div className='rounded-[10px] pt-0.5 flex flex-col gap-0 items-start relative bg-transparent' />
                    <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                      {depreciationMethodOptions.find(option => option.value === assetData?.depreciationMethod)
                        ?.label ?? '-'}
                    </p>
                  </div>
                  {assetData?.status !== AssetStatus.DISPOSED && (
                    <LoadingButton
                      startIcon={<></>}
                      loading={isLoading}
                      loadingPosition='start'
                      variant='outlined'
                      color='error'
                      size='small'
                      onClick={handleDispose}
                      className='px-8 is-full !ml-0 sm:is-auto'
                    >
                      Tandai Untuk Dispose
                    </LoadingButton>
                  )}
                </div>
              </div>
              {assetData?.depreciationMethod &&
                assetData?.depreciationMethod !== AssetDepreciationMethods.NON_DEPRECIABLE && (
                  <>
                    <div className='flex gap-2'>
                      <div className='flex flex-1 flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
                        <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                          <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                            Masa Manfaat (Bulan)
                          </small>
                        </label>
                        <div className='rounded-[10px] pt-0.5 flex flex-col gap-0 items-start relative bg-transparent' />
                        <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                          {assetData?.usefulLifeMonths ?? '-'}
                        </p>
                      </div>
                      <div className='flex flex-1 flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
                        <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                          <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                            Nilai Residu
                          </small>
                        </label>
                        <div className='rounded-[10px] pt-0.5 flex flex-col gap-0 items-start relative bg-transparent' />
                        <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                          {toCurrency(assetData?.residualValue ?? 0, false, assetData?.currency?.code)}
                        </p>
                      </div>
                    </div>
                  </>
                )}
              <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
                <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                  <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                    Diperbarui Tanggal
                  </small>
                </label>
                <div className='rounded-[10px] pt-0.5 flex flex-col gap-0 items-start relative bg-transparent' />
                <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                  {formatDate(assetData?.updatedAt ?? new Date(), 'dd MMM yyyy', { locale: id })}
                </p>
              </div>
              {openValueLogDialog && (
                <AssetValueLogsDialog open={openValueLogDialog} setOpen={setOpenValueLogDialog} assetData={assetData} />
              )}
            </>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  )
}

export default ValueCard
