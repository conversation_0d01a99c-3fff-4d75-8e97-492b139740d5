// MUI Imports
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'
import { Grid, Box } from '@mui/material'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'

import { AssetType } from '@/types/assetTypes'
import {
  assetTypeOptions,
  AssetTypes,
  currentAssetTypeOptions,
  fixedAssetTypeOptions,
  nonCurrentAssetTypeOptions
} from '../../config/enum'
import { DEFAULT_CATEGORY } from '@/data/default/category'
import { toCurrency } from '@/utils/helper'

interface DetailCardProps {
  assetData: AssetType
}

const DetailCard = ({ assetData }: DetailCardProps) => {
  let assetSubTypeLabel = '-'
  if (assetData?.type === AssetTypes.CURRENT_ASSETS) {
    assetSubTypeLabel = currentAssetTypeOptions.find(option => option.value === assetData?.subType)?.label ?? '-'
  }
  if (assetData?.type === AssetTypes.NON_CURRENT_ASSETS) {
    assetSubTypeLabel = nonCurrentAssetTypeOptions.find(option => option.value === assetData?.subType)?.label ?? '-'
  }
  if (assetData?.type === AssetTypes.FIXED_ASSETS) {
    assetSubTypeLabel = fixedAssetTypeOptions.find(option => option.value === assetData?.subType)?.label ?? '-'
  }

  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Card>
          <CardContent className='flex flex-col gap-6'>
            <div className='flex justify-between items-center'>
              <Typography variant='h5'>Detil Aset</Typography>
            </div>
            <div className='flex flex-col gap-6'>
              <Grid container spacing={3}>
                <Grid item xs={6}>
                  <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
                    <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                      <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                        Tipe Aset
                      </small>
                    </label>
                    <div className='rounded-[10px] pt-0.5 flex flex-col gap-0 items-start relative bg-transparent' />
                    <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                      {assetTypeOptions.find(option => option.value === assetData?.type)?.label ?? '-'}
                    </p>
                  </div>
                </Grid>
                <Grid item xs={6}>
                  <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
                    <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                      <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                        Jenis {assetTypeOptions.find(option => option.value === assetData?.type)?.label ?? '-'}
                      </small>
                    </label>
                    <div className='rounded-[10px] pt-0.5 flex flex-col gap-0 items-start relative bg-transparent' />
                    <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                      {assetSubTypeLabel}
                    </p>
                  </div>
                </Grid>
              </Grid>

              {/* Deskripsi Aset */}
              <div className='flex flex-col gap-4 items-start self-stretch relative w-full bg-gray-100 p-4'>
                <div className='flex flex-col gap-1'>
                  <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                    <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                      Deskripsi Aset
                    </small>
                  </label>
                  <div className='rounded-[10px] pt-0.5 flex flex-col gap-0 items-start relative bg-transparent' />
                  <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                    {assetData?.note || '-'}
                  </p>
                </div>
                {assetData?.unit && (
                  <div className='grid grid-cols-3 gap-2 w-full'>
                    <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
                      <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                        <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                          Kode Unit
                        </small>
                      </label>
                      <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
                      <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                        {assetData?.unit?.number}
                      </p>
                    </div>
                    <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
                      <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                        <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                          Tipe Unit
                        </small>
                      </label>
                      <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
                      <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                        {assetData?.unit?.type ?? '-'}
                      </p>
                    </div>
                    <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
                      <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                        <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                          Merk Unit
                        </small>
                      </label>
                      <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
                      <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                        {assetData?.unit?.brandName ?? '-'}
                      </p>
                    </div>
                  </div>
                )}
              </div>

              <Grid container spacing={3}>
                <Grid item xs={6}>
                  <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
                    <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                      <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                        Aset Milik
                      </small>
                    </label>
                    <div className='rounded-[10px] pt-0.5 flex flex-col gap-0 items-start relative bg-transparent' />
                    <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                      {assetData?.ownerCompany?.name || '-'}
                    </p>
                  </div>
                </Grid>
                <Grid item xs={6}>
                  <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
                    <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                      <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                        Lokasi Aset
                      </small>
                    </label>
                    <div className='rounded-[10px] pt-0.5 flex flex-col gap-0 items-start relative bg-transparent' />
                    <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                      {assetData?.ownerSite?.name || '-'}
                    </p>
                  </div>
                </Grid>
              </Grid>

              {/* Tanggal Beli and Tanggal Pakai in a row */}
              <Grid container spacing={3}>
                <Grid item xs={6}>
                  <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
                    <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                      <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                        Tanggal Beli
                      </small>
                    </label>
                    <div className='rounded-[10px] pt-0.5 flex flex-col gap-0 items-start relative bg-transparent' />
                    <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                      {assetData?.purchasedDate
                        ? formatDate(new Date(assetData.purchasedDate), 'dd/MM/yyyy', { locale: id })
                        : '-'}
                    </p>
                  </div>
                </Grid>
                <Grid item xs={6}>
                  <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
                    <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                      <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                        Tanggal Pakai
                      </small>
                    </label>
                    <div className='rounded-[10px] pt-0.5 flex flex-col gap-0 items-start relative bg-transparent' />
                    <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                      {assetData?.usedDate
                        ? formatDate(new Date(assetData.usedDate), 'dd/MM/yyyy', { locale: id })
                        : '-'}
                    </p>
                  </div>
                </Grid>
              </Grid>
              <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
                <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                  <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                    Nilai Perolehan
                  </small>
                </label>
                <div className='rounded-[10px] pt-0.5 flex flex-col gap-0 items-start relative bg-transparent' />
                <p className='tracking-[0.2px] leading-6 text-primary font-medium'>
                  {toCurrency(assetData?.baseValue, false, assetData?.currency?.code)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  )
}

export default DetailCard
