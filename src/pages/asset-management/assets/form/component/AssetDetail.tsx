import { <PERSON><PERSON>, <PERSON><PERSON>ield, <PERSON>u<PERSON><PERSON>, <PERSON>, Card<PERSON>ontent, Typo<PERSON>, ListSubheader, ListItemText } from '@mui/material'
import { Controller, useFormContext, useWatch } from 'react-hook-form'
import { useEffect } from 'react'
import { useQuery } from '@tanstack/react-query'
import usePartialState from '@/core/hooks/usePartialState'
import { AssetDtoType } from '../config/schema'
import AppReactDatepicker from '@/components/libs/styles/AppReactDatepicker'
import {
  assetTypeOptions,
  AssetTypes,
  currentAssetTypeOptions,
  fixedAssetTypeOptions,
  FixedAssetTypes,
  nonCurrentAssetTypeOptions,
  NonCurrentAssetTypes
} from '../../config/enum'
import { UnitType } from '@/types/companyTypes'
import UnitAutocomplete from '@/components/UnitAutocomplete'
import CompanyQueryMethods, { COMPANY_QUERY_KEY } from '@/api/services/company/query'
import { useAuth } from '@/contexts/AuthContext'
import { ProjectType } from '@/types/projectTypes'

const AssetDetail = () => {
  const { userProfile, allSites } = useAuth()
  const { control, setValue } = useFormContext<AssetDtoType>()
  const [selectedUnit, setPartialSelectedUnit, setSelectedUnit] = usePartialState<UnitType | null>(null)

  // Fetch company list
  const { data: companyData, isLoading: isLoadingCompanies } = useQuery({
    enabled: !!userProfile?.companyId,
    queryKey: [COMPANY_QUERY_KEY],
    queryFn: () => CompanyQueryMethods.getCompany(userProfile?.companyId)
  })

  const typeWatch = useWatch({
    control,
    name: 'type',
    defaultValue: AssetTypes.FIXED_ASSETS
  })

  const subTypeWatch = useWatch({
    control,
    name: 'subType',
    defaultValue: ''
  })

  const unitWatch = useWatch({ control, name: 'unit' })

  const isUnitAsset = subTypeWatch === FixedAssetTypes.MACHINERY || subTypeWatch === FixedAssetTypes.VEHICLES

  const groupedSiteList = allSites
    ?.reduce(
      (acc, site) => {
        const existingGroup = acc.find(group => group.projectId === site.projectId)

        if (existingGroup) {
          existingGroup.sites.push(site)
        } else {
          acc.push({
            projectId: site.projectId,
            project: site.project,
            sites: [site]
          })
        }

        return acc
      },
      [] as { projectId?: string; project?: ProjectType; sites: typeof allSites }[]
    )
    .sort((a, b) => {
      if (a.projectId === null) return 1
      if (b.projectId === null) return -1
      return 0
    })

  useEffect(() => {
    if (unitWatch) {
      setSelectedUnit(unitWatch as UnitType)
    } else {
      setSelectedUnit(null)
    }
  }, [unitWatch])

  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Detil Aset</Typography>
        </div>
        <Grid container spacing={4}>
          {/* Kode Aset - Full width */}
          <Grid item xs={12}>
            <Controller
              control={control}
              name='code'
              render={({ field, fieldState: { error } }) => (
                <TextField
                  {...field}
                  fullWidth
                  label='Kode Aset'
                  placeholder='Masukkan kode aset'
                  error={!!error}
                  helperText={error?.message}
                  InputLabelProps={{
                    shrink: !!field.value
                  }}
                />
              )}
            />
          </Grid>

          {/* Nama Aset - Full width */}
          <Grid item xs={12}>
            <Controller
              control={control}
              name='name'
              render={({ field, fieldState: { error } }) => (
                <TextField
                  {...field}
                  fullWidth
                  label='Nama Aset'
                  placeholder='Masukkan nama aset'
                  error={!!error}
                  helperText={error?.message}
                  InputLabelProps={{
                    shrink: !!field.value
                  }}
                />
              )}
            />
          </Grid>

          {/* Tipe Aset and Jenis Aset Tidak Lancar - Side by side */}
          <Grid item xs={12} md={6}>
            <Controller
              control={control}
              name='type'
              disabled
              render={({ field, fieldState: { error } }) => (
                <TextField {...field} select fullWidth label='Tipe Aset' error={!!error} helperText={error?.message}>
                  {assetTypeOptions.map(option => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </TextField>
              )}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <Controller
              control={control}
              name='subType'
              render={({ field, fieldState: { error } }) => (
                <TextField
                  {...field}
                  select={!!typeWatch}
                  fullWidth
                  disabled={!typeWatch}
                  label='Jenis Aset'
                  error={!!error}
                  helperText={error?.message}
                >
                  {(typeWatch === AssetTypes.CURRENT_ASSETS
                    ? currentAssetTypeOptions
                    : typeWatch === AssetTypes.NON_CURRENT_ASSETS
                      ? nonCurrentAssetTypeOptions
                      : fixedAssetTypeOptions
                  ).map(asset => (
                    <MenuItem key={asset.value} value={asset.value}>
                      {asset.label}
                    </MenuItem>
                  ))}
                </TextField>
              )}
            />
          </Grid>

          <Grid item xs={12}>
            <Grid container spacing={4}>
              {isUnitAsset && (
                <>
                  <Grid item xs={12} md={6}>
                    <Controller
                      control={control}
                      name='unitId'
                      rules={{
                        validate: value => {
                          if (isUnitAsset) {
                            if (!value) {
                              return 'Wajib diisi'
                            }
                          }
                          return true
                        }
                      }}
                      render={({ fieldState: { error } }) => (
                        <UnitAutocomplete
                          value={selectedUnit}
                          onChange={unit => {
                            if (unit) {
                              setValue('unitId', unit.id)
                              setValue('unit', unit)
                              setSelectedUnit(unit)
                            } else {
                              setValue('unitId', undefined)
                              setValue('unit', undefined)
                              setSelectedUnit(null)
                            }
                          }}
                          error={!!error}
                          helperText={error?.message}
                          disabled={!isUnitAsset}
                        />
                      )}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label='Merk Unit'
                      variant='outlined'
                      value={selectedUnit?.brandName ?? ''}
                      disabled
                      InputLabelProps={{ shrink: !!selectedUnit?.brandName }}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label='Tipe Unit'
                      variant='outlined'
                      value={selectedUnit?.type ?? ''}
                      disabled
                      InputLabelProps={{ shrink: !!selectedUnit?.type }}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label='Nomor Lambung'
                      variant='outlined'
                      value={selectedUnit?.hullNumber ?? ''}
                      disabled
                      InputLabelProps={{ shrink: !!selectedUnit?.hullNumber }}
                    />
                  </Grid>
                </>
              )}

              <Grid item xs={12}>
                <Controller
                  control={control}
                  name='note'
                  render={({ field, fieldState: { error } }) => (
                    <TextField
                      {...field}
                      fullWidth
                      multiline
                      rows={3}
                      label='Deskripsi aset'
                      placeholder='Masukkan deskripsi aset (opsional)'
                      error={!!error}
                      helperText={error?.message}
                      InputProps={{
                        className: 'bg-white'
                      }}
                      InputLabelProps={{
                        shrink: !!field.value
                      }}
                    />
                  )}
                />
              </Grid>
            </Grid>
          </Grid>

          {/* Aset Milik - Full width */}
          <Grid item xs={12}>
            <Controller
              control={control}
              name='ownerCompanyId'
              render={({ field, fieldState: { error } }) => (
                <TextField
                  {...field}
                  key={`${companyData?.subsidiaries?.length}_${field.value}`}
                  select
                  fullWidth
                  label='Aset Milik'
                  error={!!error}
                  helperText={error?.message}
                  disabled={isLoadingCompanies}
                >
                  {isLoadingCompanies ? (
                    <MenuItem disabled>Loading companies...</MenuItem>
                  ) : (
                    [
                      <MenuItem key={companyData?.id} value={companyData?.id}>
                        {companyData?.name}
                      </MenuItem>,
                      ...companyData?.subsidiaries?.map(company => (
                        <MenuItem key={company.id} value={company.id}>
                          {company.name}
                        </MenuItem>
                      ))
                    ]
                  )}
                </TextField>
              )}
            />
          </Grid>

          {/* Lokasi Aset - Full width */}
          <Grid item xs={12}>
            <Controller
              control={control}
              name='ownerSiteId'
              render={({ field, fieldState: { error } }) => (
                <TextField
                  {...field}
                  key={`${allSites?.length}_${field.value}`}
                  select
                  fullWidth
                  label='Lokasi Aset'
                  error={!!error}
                  helperText={error?.message}
                >
                  {groupedSiteList.map(group => {
                    let children = []
                    children.push(
                      <ListSubheader
                        className='bg-green-50 text-primary font-semibold'
                        key={group.projectId ?? 'no_project'}
                      >
                        {group.project?.name || 'Tanpa Proyek'}
                      </ListSubheader>
                    )
                    group.sites.forEach(site => {
                      children.push(
                        <MenuItem key={site.id} value={site.id}>
                          <ListItemText primary={site.name} />
                        </MenuItem>
                      )
                    })
                    return children
                  })}
                </TextField>
              )}
            />
          </Grid>

          {/* Tanggal Beli and Tanggal Pakai - Side by side */}
          <Grid item xs={12} md={6}>
            <Controller
              control={control}
              name='purchasedDate'
              render={({ field, fieldState: { error } }) => (
                <AppReactDatepicker
                  selected={field.value ? new Date(field.value) : null}
                  onChange={(date: Date | null) => field.onChange(date?.toISOString().split('T')[0])}
                  placeholderText='Pilih tanggal beli'
                  dateFormat='dd/MM/yyyy'
                  customInput={
                    <TextField
                      fullWidth
                      label='Tanggal Beli'
                      error={!!error}
                      helperText={error?.message}
                      InputProps={{
                        readOnly: true
                      }}
                    />
                  }
                />
              )}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <Controller
              control={control}
              name='usedDate'
              render={({ field, fieldState: { error } }) => (
                <AppReactDatepicker
                  selected={field.value ? new Date(field.value) : null}
                  onChange={(date: Date | null) => field.onChange(date?.toISOString().split('T')[0])}
                  placeholderText='Pilih tanggal pakai'
                  dateFormat='dd/MM/yyyy'
                  customInput={
                    <TextField
                      fullWidth
                      label='Tanggal Pakai'
                      error={!!error}
                      helperText={error?.message}
                      InputProps={{
                        readOnly: true
                      }}
                    />
                  }
                />
              )}
            />
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  )
}

export default AssetDetail
