import { <PERSON>rid, <PERSON>F<PERSON>, Card, CardContent, Typography, Autocomplete, CircularProgress } from '@mui/material'
import { Controller, useFormContext } from 'react-hook-form'
import { useQuery } from '@tanstack/react-query'
import { AssetDtoType } from '../config/schema'
import AccountsQueryMethods, { ACCOUNT_LIST_QUERY_KEY, ACCOUNT_TYPE_QUERY_KEY } from '@/api/services/account/query'
import { AccountType } from '@/types/accountTypes'

const AccountingDetail = () => {
  const { control } = useFormContext<AssetDtoType>()

  // Fetch account list
  const { data: accountList, isLoading: isLoadingAccounts } = useQuery({
    queryKey: [ACCOUNT_LIST_QUERY_KEY],
    queryFn: () =>
      AccountsQueryMethods.getAccountList({
        limit: Number.MAX_SAFE_INTEGER,
        level: 1,
        accountTypeIds: [
          'FIXED_ASSET',
          'OTHER_ASSET',
          'OTHER_CURRENT_ASSET',
          'ACCUMULATED_DEPRECIATION',
          'EXPENSE',
          'OTHER_EXPENSE',
          'COGS'
        ]
      })
  })

  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Detil Pencatatan</Typography>
        </div>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='assetAccountId'
              render={({ field, fieldState: { error } }) => (
                <Autocomplete
                  {...field}
                  options={
                    accountList?.items?.filter(account =>
                      ['FIXED_ASSET', 'OTHER_ASSET', 'OTHER_CURRENT_ASSET'].includes(account.accountType?.id ?? '')
                    ) || []
                  }
                  getOptionLabel={(option: AccountType) => `${option.code} - ${option.name}`}
                  isOptionEqualToValue={(option: AccountType, value: AccountType) => option.id === value.id}
                  loading={isLoadingAccounts}
                  onChange={(_, newValue) => field.onChange(newValue?.id || '')}
                  value={accountList?.items?.find(account => account.id === field.value) || null}
                  renderInput={params => (
                    <TextField
                      {...params}
                      label='Kode Akun Aset'
                      error={!!error}
                      helperText={error?.message}
                      InputProps={{
                        ...params.InputProps,
                        endAdornment: (
                          <>
                            {isLoadingAccounts ? <CircularProgress color='inherit' size={20} /> : null}
                            {params.InputProps.endAdornment}
                          </>
                        )
                      }}
                    />
                  )}
                  filterOptions={(options, { inputValue }) => {
                    return options.filter(
                      option =>
                        option.name.toLowerCase().includes(inputValue.toLowerCase()) ||
                        option.code.toLowerCase().includes(inputValue.toLowerCase())
                    )
                  }}
                />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='depreciationAccountId'
              render={({ field, fieldState: { error } }) => (
                <Autocomplete
                  {...field}
                  options={
                    accountList?.items?.filter(account =>
                      ['ACCUMULATED_DEPRECIATION'].includes(account.accountType?.id ?? '')
                    ) || []
                  }
                  getOptionLabel={(option: AccountType) => `${option.code} - ${option.name}`}
                  isOptionEqualToValue={(option: AccountType, value: AccountType) => option.id === value.id}
                  loading={isLoadingAccounts}
                  onChange={(_, newValue) => field.onChange(newValue?.id || '')}
                  value={accountList?.items?.find(account => account.id === field.value) || null}
                  renderInput={params => (
                    <TextField
                      {...params}
                      label='Akun Akumulasi Penyusutan'
                      error={!!error}
                      helperText={error?.message}
                      InputProps={{
                        ...params.InputProps,
                        endAdornment: (
                          <>
                            {isLoadingAccounts ? <CircularProgress color='inherit' size={20} /> : null}
                            {params.InputProps.endAdornment}
                          </>
                        )
                      }}
                    />
                  )}
                  filterOptions={(options, { inputValue }) => {
                    return options.filter(
                      option =>
                        option.name.toLowerCase().includes(inputValue.toLowerCase()) ||
                        option.code.toLowerCase().includes(inputValue.toLowerCase())
                    )
                  }}
                />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='expenseAccountId'
              render={({ field, fieldState: { error } }) => (
                <Autocomplete
                  {...field}
                  options={
                    accountList?.items?.filter(account =>
                      ['EXPENSE', 'OTHER_EXPENSE', 'COGS'].includes(account.accountType?.id ?? '')
                    ) || []
                  }
                  getOptionLabel={(option: AccountType) => `${option.code} - ${option.name}`}
                  isOptionEqualToValue={(option: AccountType, value: AccountType) => option.id === value.id}
                  loading={isLoadingAccounts}
                  onChange={(_, newValue) => field.onChange(newValue?.id || '')}
                  value={accountList?.items?.find(account => account.id === field.value) || null}
                  renderInput={params => (
                    <TextField
                      {...params}
                      label='Akun Beban Penyusutan'
                      error={!!error}
                      helperText={error?.message}
                      InputProps={{
                        ...params.InputProps,
                        endAdornment: (
                          <>
                            {isLoadingAccounts ? <CircularProgress color='inherit' size={20} /> : null}
                            {params.InputProps.endAdornment}
                          </>
                        )
                      }}
                    />
                  )}
                  filterOptions={(options, { inputValue }) => {
                    return options.filter(
                      option =>
                        option.name.toLowerCase().includes(inputValue.toLowerCase()) ||
                        option.code.toLowerCase().includes(inputValue.toLowerCase())
                    )
                  }}
                />
              )}
            />
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  )
}

export default AccountingDetail
