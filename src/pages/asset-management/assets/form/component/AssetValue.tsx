import { Grid, TextField, MenuItem, Card, CardContent, Typography } from '@mui/material'
import { Controller, useFormContext } from 'react-hook-form'
import { AssetDtoType } from '../config/schema'
import { useAuth } from '@/contexts/AuthContext'
import { useState } from 'react'
import { CurrenciesType } from '@/types/currenciesTypes'
import CurrencyField from '@/components/numeric/CurrencyField'
import { AssetDepreciationMethods, depreciationMethodOptions } from '../../config/enum'
import NumberField from '@/components/numeric/NumberField'

const AssetValue = () => {
  const { control, setValue, watch } = useFormContext<AssetDtoType>()
  const { currenciesList } = useAuth()
  const depreciationMethod = watch('depreciationMethod')
  const valueWatch = watch('value')

  const [currentCurrency, setCurrentCurrency] = useState<CurrenciesType>()

  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Nilai Aset</Typography>
        </div>
        <Grid container spacing={4}>
          <Grid item xs={12} md={5}>
            <Controller
              control={control}
              name='currencyId'
              render={({ field, fieldState: { error } }) => (
                <TextField
                  {...field}
                  key={`${currenciesList?.length}_${field.value}`}
                  select
                  fullWidth
                  label='Mata Uang'
                  error={!!error}
                  helperText={error?.message}
                  onChange={e => {
                    const currentCurr = currenciesList?.find(currency => currency.id === e.target.value)
                    setCurrentCurrency(currentCurr)
                    field.onChange(e.target.value)
                    if (currentCurr?.isDefault) {
                      setValue('exchangeRate', 1)
                    }
                  }}
                >
                  {currenciesList?.map(currency => (
                    <MenuItem key={currency.id} value={currency.id}>
                      {currency.code} - {currency.name}
                    </MenuItem>
                  ))}
                </TextField>
              )}
            />
          </Grid>
          <Grid item xs={12} md={7}>
            <Controller
              control={control}
              name='exchangeRate'
              render={({ field, fieldState: { error } }) => (
                <TextField
                  disabled={!!currentCurrency?.isDefault || !currentCurrency}
                  fullWidth
                  aria-readonly
                  label='Konversi ke Rupiah'
                  value={field.value || ''}
                  InputLabelProps={{
                    shrink: !!field.value
                  }}
                  InputProps={{
                    inputComponent: CurrencyField as any,
                    inputProps: {
                      prefix: 'Rp',
                      name: 'exchangeRate',
                      onChange: (e: any) => {
                        field.onChange(e.target.value)
                      },
                      value: field.value,
                      allowLeadingZeros: false
                    }
                  }}
                  {...(!!error && { error: true, helperText: 'Wajib diisi' })}
                />
              )}
            />
          </Grid>
          <Grid item xs={12} md={5}>
            <Controller
              control={control}
              name='baseValue'
              render={({ field, fieldState: { error } }) => (
                <TextField
                  fullWidth
                  label='Nilai Awal Aset'
                  error={!!error}
                  helperText={error?.message}
                  value={field.value || ''}
                  InputProps={{
                    inputComponent: CurrencyField as any,
                    inputProps: {
                      prefix: currentCurrency?.symbol ?? 'Rp',
                      name: 'baseValue',
                      onChange: (e: any) => {
                        field.onChange(e.target.value)
                      },
                      value: field.value,
                      allowLeadingZeros: false
                    }
                  }}
                />
              )}
            />
          </Grid>
          <Grid item xs={12} md={7}>
            <Controller
              control={control}
              name='depreciationMethod'
              render={({ field, fieldState: { error } }) => (
                <TextField
                  {...field}
                  key={`${depreciationMethodOptions.length}_${field.value}`}
                  select
                  fullWidth
                  label='Metode Penyusutan'
                  error={!!error}
                  helperText={error?.message}
                >
                  {depreciationMethodOptions.map(option => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </TextField>
              )}
            />
          </Grid>
          {depreciationMethod && depreciationMethod !== AssetDepreciationMethods.NON_DEPRECIABLE && (
            <>
              <Grid item xs={12} md={5}>
                <Controller
                  control={control}
                  name='usefulLifeMonths'
                  rules={{
                    required: !depreciationMethod || depreciationMethod !== AssetDepreciationMethods.NON_DEPRECIABLE
                  }}
                  render={({ field, fieldState: { error } }) => (
                    <TextField
                      fullWidth
                      label='Masa Manfaat (Bulan)'
                      error={!!error}
                      helperText={error?.message}
                      value={field.value || ''}
                      InputLabelProps={{
                        shrink: !!field.value
                      }}
                      InputProps={{
                        inputComponent: NumberField as any,
                        inputProps: {
                          name: 'usefulLifeMonths',
                          onChange: (e: any) => {
                            field.onChange(e.target.value)
                          },
                          value: field.value,
                          allowLeadingZeros: false
                        }
                      }}
                    />
                  )}
                />
              </Grid>
              <Grid item xs={12} md={7}>
                <Controller
                  control={control}
                  name='residualValue'
                  rules={{
                    required: !depreciationMethod || depreciationMethod !== AssetDepreciationMethods.NON_DEPRECIABLE
                  }}
                  render={({ field, fieldState: { error } }) => (
                    <TextField
                      fullWidth
                      label='Nilai Residu'
                      error={!!error}
                      helperText={error?.message}
                      defaultValue={0}
                      value={field.value || ''}
                      InputLabelProps={{
                        shrink: true
                      }}
                      InputProps={{
                        inputComponent: CurrencyField as any,
                        inputProps: {
                          prefix: currentCurrency?.symbol ?? 'Rp',
                          name: 'residualValue',
                          onChange: (e: any) => {
                            field.onChange(e.target.value)
                          },
                          value: field.value,
                          allowLeadingZeros: false
                          // isAllowed: ({ floatValue }) => floatValue <= valueWatch || floatValue === undefined
                        }
                      }}
                    />
                  )}
                />
              </Grid>
            </>
          )}
        </Grid>
      </CardContent>
    </Card>
  )
}

export default AssetValue
