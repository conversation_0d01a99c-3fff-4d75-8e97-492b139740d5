import truncateString from '@/core/utils/truncate'
import { DocumentType, DocumentUnitType } from '@/types/companyTypes'
import { downloadFile } from '@/utils/downloadFile'
import { extractNameFromUrl } from '@/utils/string'
import { Typography, Button } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'
import { NavigateFunction } from 'react-router-dom'

const columnHelper = createColumnHelper<DocumentUnitType>()

const getLabelType = (type: DocumentType) => {
  switch (type) {
    case DocumentType.BPKB:
    case DocumentType.STNK:
    case DocumentType.KIR:
    case DocumentType.SIO:
      return type
    default:
      return 'Asuransi'
  }
}

export const tableColumns = (navigate: NavigateFunction) => [
  columnHelper.accessor('unit.number', {
    header: 'Kode Unit',
    cell: ({ row }) => (
      <Typography
        onClick={() => navigate(`/company-data/assets/unit/${row.original.unitId}`)}
        color='primary'
        sx={{ cursor: 'pointer' }}
        role='button'
      >
        {row.original.unit.number}
      </Typography>
    )
  }),
  columnHelper.accessor('unit.brandName', {
    header: 'Nama Unit',
    cell: ({ row }) => `${row.original.unit.brandName} ${row.original.unit.type}`
  }),
  columnHelper.accessor('type', {
    header: 'Tipe Dokumen',
    cell: ({ row }) => <Typography>{getLabelType(row.original.type)}</Typography>
  }),
  columnHelper.accessor('effectiveDate', {
    header: 'Tgl Mulai Berlaku',
    cell: ({ row }) => row.original.effectiveDate ?? '-'
  }),
  columnHelper.accessor('expirationDate', {
    header: 'Tgl Habis Berlaku',
    cell: ({ row }) => row.original.expirationDate ?? '-'
  }),
  columnHelper.accessor('renewalDate', {
    header: 'Tgl Diperbarui',
    cell: ({ row }) => row.original.renewalDate ?? '-'
  }),
  columnHelper.display({
    id: 'action',
    header: 'Action',
    cell: ({ row }) =>
      !!row.original.url ? (
        <Button variant='outlined' size='small' target='_blank' href={row.original.url} download>
          Lihat Dokumen
        </Button>
      ) : (
        '-'
      )
  })
]

export const tableColumnsInsurance = (navigate: NavigateFunction) => [
  columnHelper.accessor('unit.number', {
    header: 'Kode Unit',
    cell: ({ row }) => (
      <Typography
        onClick={() => navigate(`/company-data/assets/unit/${row.original.unitId}`)}
        color='primary'
        sx={{ cursor: 'pointer' }}
        role='button'
      >
        {row.original.unit.number}
      </Typography>
    )
  }),
  columnHelper.accessor('unit.brandName', {
    header: 'Nama Unit',
    cell: ({ row }) => `${row.original.unit.brandName} ${row.original.unit.type}`
  }),
  columnHelper.accessor('name', {
    header: 'Nama Asuransi',
    cell: ({ row }) => truncateString(row.original.name, 20)
  }),
  columnHelper.accessor('effectiveDate', {
    header: 'Tgl Mulai Berlaku',
    cell: ({ row }) => row.original.effectiveDate ?? '-'
  }),
  columnHelper.accessor('expirationDate', {
    header: 'Tgl Habis Berlaku',
    cell: ({ row }) => row.original.expirationDate ?? '-'
  }),
  columnHelper.accessor('renewalDate', {
    header: 'Tgl Diperbarui',
    cell: ({ row }) => row.original.renewalDate ?? '-'
  }),
  columnHelper.display({
    id: 'action',
    header: 'Action',
    cell: ({ row }) =>
      !!row.original.url ? (
        <Button variant='outlined' size='small' target='_blank' href={row.original.url} download>
          Lihat Dokumen
        </Button>
      ) : (
        '-'
      )
  })
]
