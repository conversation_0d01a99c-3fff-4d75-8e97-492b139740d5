import { Card, CardContent, Button, Typography, FormControl, InputLabel, MenuItem, Select } from '@mui/material'
import DebouncedInput from '@/components/DebounceInput'
import Table from '@/components/table'
import { useDocument } from '../../context/DocumentContext'
import {
  useReactTable,
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel
} from '@tanstack/react-table'
import { tableColumns, tableColumnsInsurance } from '../config/table'
import { useMemo } from 'react'
import { useLocation } from 'react-router-dom'
import { insuranceList, documentList as documentOptions } from '../config/utils'

const DocumentList = () => {
  const locations = useLocation()
  const { navigate, documentParams, setPartialDocumentParams, setDocumentParams, documentList } = useDocument()
  const { items, totalItems, totalPages, limit: limitItems, page: pageItems } = documentList
  const { search, limit, page } = documentParams

  const isInsurancePage = locations.pathname.includes('insurance')

  const tableOptions = useMemo(
    () => ({
      data: items ?? [],
      columns: isInsurancePage ? tableColumnsInsurance(navigate) : tableColumns(navigate),
      initialState: {
        pagination: {
          pageSize: limit ?? 10,
          pageIndex: page - 1
        }
      },
      state: {
        pagination: {
          pageSize: limitItems,
          pageIndex: pageItems - 1
        }
      },
      manualPagination: true,
      rowCount: totalItems,
      pageCount: totalPages,
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [items, totalItems, totalPages, limit, page, isInsurancePage]
  )

  const table = useReactTable<any>(tableOptions)
  return (
    <Card>
      <CardContent className='flex flex-col gap-2'>
        <div className='flex justify-between items-center'>
          <div className='flex gap-4 items-center flex-col sm:flex-row is-full sm:is-auto'>
            <DebouncedInput
              value={search}
              onChange={value => setDocumentParams(prev => ({ ...prev, page: 1, search: value as string }))}
              placeholder='Cari'
              className='is-full sm:is-auto md:max-w-[240px]'
            />
            {!isInsurancePage && (
              <>
                <Typography>Filter</Typography>
                <FormControl size='small' className='w-[240px]'>
                  <Select
                    fullWidth
                    size='small'
                    labelId='select-type'
                    id='select-type'
                    defaultValue=''
                    inputProps={{ placeholder: 'Pilih Tipe Dokumen' }}
                    onChange={e => setPartialDocumentParams('types', [e.target.value as string])}
                    value={documentParams.types[0]}
                  >
                    <MenuItem
                      value={isInsurancePage ? 'INSURANCE_FIRST,INSURANCE_SECOND,INSURANCE_THIRD' : 'BPKB,STNK,KIR,SIO'}
                    >
                      Semua
                    </MenuItem>
                    {isInsurancePage
                      ? insuranceList.map(insurance => <MenuItem value={insurance.value}>{insurance.name}</MenuItem>)
                      : documentOptions.map(document => <MenuItem value={document.value}>{document.name}</MenuItem>)}
                  </Select>
                </FormControl>
              </>
            )}
          </div>
          <div className='space-x-2'>
            {/* <Button
            className='is-full sm:is-auto'
            startIcon={<i className='ri-upload-2-line' />}
            color='secondary'
            variant='outlined'
          >
            Ekspor
          </Button>
          <Button
            color='primary'
            variant='outlined'
            startIcon={<i className='mdi-file-document-outline' />}
            className='is-full sm:is-auto'
            onClick={() => setImportDialogOpen(true)}
          >
            Impor List
          </Button> */}
            {/* <Button
            className='is-full sm:is-auto'
            startIcon={<i className='ri-add-circle-line' />}
            color='primary'
            variant='contained'
            onClick={() => setDialogActiva({ state: true, asset: null })}
          >
            Tambah Kode
          </Button> */}
          </div>
        </div>
      </CardContent>
      <Table
        table={table}
        emptyLabel={
          <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
            <Typography> Belum ada Dokumen</Typography>
            <Typography className='text-sm text-gray-400'>
              Semua dokumen terkait unit akan ditampilkan di sini
            </Typography>
          </td>
        }
        onRowsPerPageChange={pageSize => {
          if (pageSize > totalItems) {
            setDocumentParams(prev => ({ ...prev, limit: totalItems, page: 1 }))
          } else {
            setPartialDocumentParams('limit', pageSize)
            const maxPage = Math.ceil(totalItems / pageSize)
            if (page > maxPage) {
              setDocumentParams(prev => ({ ...prev, page: maxPage }))
            }
          }
        }}
        onPageChange={pageIndex => setDocumentParams(prev => ({ ...prev, page: pageIndex }))}
      />
    </Card>
  )
}

export default DocumentList
