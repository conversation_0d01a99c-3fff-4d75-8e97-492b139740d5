import { useState, useMemo } from 'react'
import { But<PERSON>, Card, CardContent, InputLabel, Select, Typography } from '@mui/material'
import DebouncedInput from '@/components/DebounceInput'
import { useUnit } from '../context/UnitContext'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import { tableColumns } from '../config/table'
import Table from '@/components/table'
import { useNavigate } from 'react-router-dom'
import ImportDialog from '@/components/dialogs/import-dialog'
import { ExportImportScope } from '@/types/exportImportTypes'
import Permission from '@/core/components/Permission'

const UnitList = () => {
  const navigate = useNavigate()
  const { unitParams, setPartialUnitParams, setUnitParams } = useUnit()

  const [importDialogOpen, setImportDialogOpen] = useState(false)

  const { page, search, limit } = unitParams

  const tableOptions = useMemo(
    () => ({
      data: [],
      columns: tableColumns({}),
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    []
  )

  const table = useReactTable<any>(tableOptions)

  return (
    <>
      <ImportDialog
        open={importDialogOpen}
        scope={ExportImportScope.UNIT}
        onSubmit={console.log}
        setOpen={() => setImportDialogOpen(!importDialogOpen)}
        type='ITEM'
      />
      <Card>
        <CardContent className='flex flex-col gap-2'>
          <div className='flex justify-between items-center'>
            <div className='flex gap-4 items-center flex-col sm:flex-row is-full sm:is-auto'>
              <DebouncedInput
                value={search}
                onChange={value => setUnitParams(prev => ({ ...prev, page: 1, search: value as string }))}
                placeholder='Cari'
                className='is-full sm:is-auto md:max-w-[240px]'
              />
            </div>
            <div className='space-x-2'>
              {/* <Button
                className='is-full sm:is-auto'
                startIcon={<i className='ri-upload-2-line' />}
                color='secondary'
                variant='outlined'
              >
                Ekspor
              </Button> */}
              <Button
                color='primary'
                variant='outlined'
                startIcon={<i className='mdi-file-document-outline' />}
                className='is-full sm:is-auto'
                onClick={() => setImportDialogOpen(true)}
              >
                Impor List
              </Button>
              <Permission permission='unit.write'>
                <Button
                  className='is-full sm:is-auto'
                  startIcon={<i className='ri-add-circle-line' />}
                  color='primary'
                  variant='contained'
                  onClick={() => navigate('/company-data/assets/unit/new')}
                >
                  Tambah Unit
                </Button>
              </Permission>
            </div>
          </div>
        </CardContent>
        <Table
          table={table}
          emptyLabel={
            <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
              <Typography> Belum ada Unit</Typography>
              <Typography className='text-sm text-gray-400'>
                Semua unit yang telah dibuat akan ditampilkan di sini
              </Typography>
            </td>
          }
        />
      </Card>
    </>
  )
}

export default UnitList
