import { StatusChipColorType } from '@/types/appTypes'
import { MrStatus, MrType } from '@/types/mrTypes'
import { Chip, colors, IconButton, Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { mrStatusOptions } from './utils'
import { MrPriority, mrPriorityOptions } from '../../create/config/enum'

export const statusChipColor: { [key: string]: StatusChipColorType } = {
  [MrStatus.PROCESSED]: { color: 'warning' },
  [MrStatus.APPROVED]: { color: 'success' },
  [MrStatus.CANCELED]: { color: 'error' },
  [MrStatus.REJECTED]: { color: 'error' },
  [MrStatus.CLOSED]: { color: 'info' }
}

type MrTypeWithAction = MrType & {
  action?: string
}

type RowActionType = {
  showDetail: (id: string) => void
}

// Column Definitions
const columnHelper = createColumnHelper<MrTypeWithAction>()

export const tableColumns = (rowAction: RowActionType) => [
  columnHelper.accessor('number', {
    header: 'No. MR',
    cell: ({ row }) => (
      <Typography
        color={colors.green.A400}
        className='cursor-pointer'
        onClick={() => rowAction.showDetail(row.original.id)}
      >
        {row.original.number}
      </Typography>
    )
  }),
  columnHelper.accessor('status', {
    header: 'Status',
    cell: ({ row }) => (
      <Chip
        label={mrStatusOptions.find(option => option.value === row.original.status)?.label}
        color={statusChipColor[row.original.status].color}
        variant='tonal'
        size='small'
      />
    )
  }),
  columnHelper.accessor('unit', {
    header: 'Unit',
    cell: ({ row }) =>
      row.original.unit ? (
        <div className='flex flex-col'>
          <Typography>{row.original.unit?.brandName}</Typography>
          <Typography variant='caption'>{row.original.unit?.number}</Typography>
        </div>
      ) : (
        '-'
      )
  }),
  columnHelper.accessor('site', {
    header: 'Lokasi',
    cell: ({ row }) => <Typography>{row.original.site?.name}</Typography>
  }),
  columnHelper.accessor('priority', {
    header: 'Prioritas',
    cell: ({ row }) => {
      const priority = mrPriorityOptions.find(option => option.value === String(row.original.priority ?? MrPriority.P4))
      return (
        <div className='flex items-center gap-2'>
          <div className={`size-2 ${priority.color}`} />
          <Typography>{priority.label}</Typography>
        </div>
      )
    }
  }),
  columnHelper.accessor('createdAt', {
    header: 'Tanggal Dibuat',
    cell: ({ row }) => (
      <Typography>{formatDate(row.original.createdAt ?? Date.now(), 'eeee, dd/MM/yyyy', { locale: id })}</Typography>
    )
  }),
  columnHelper.accessor('action', {
    header: 'Action',
    cell: ({ row }) => (
      <div className='flex items-center gap-0.5'>
        <IconButton size='small' onClick={() => rowAction.showDetail(row.original.id)}>
          <i className='ri-eye-line text-textSecondary' />
        </IconButton>
      </div>
    ),
    enableSorting: false
  })
]
