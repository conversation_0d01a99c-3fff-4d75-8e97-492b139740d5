import { <PERSON><PERSON><PERSON><PERSON>, MrUserStatus } from '@/types/mrTypes'
import { MrPriority } from '../../create/config/enum'

export const mrStatusOptions = [
  {
    value: MrStatus.PROCESSED,
    label: 'Diproses'
  },
  {
    value: MrStatus.APPROVED,
    label: '<PERSON>setuju<PERSON>'
  },
  {
    value: MrStatus.REJECTED,
    label: '<PERSON><PERSON><PERSON>'
  },
  {
    value: MrStatus.CANCELED,
    label: '<PERSON><PERSON><PERSON><PERSON>'
  },
  {
    value: MrStatus.CLOSED,
    label: '<PERSON><PERSON><PERSON>'
  }
]

export const mrUserStatusOptions = [
  {
    value: MrUserStatus.WAITING,
    label: '<PERSON>unggu'
  },
  {
    value: MrUserStatus.APPROVED,
    label: '<PERSON>setuju<PERSON>'
  },
  {
    value: MrUserStatus.REJECTED,
    label: '<PERSON><PERSON><PERSON>'
  }
]

export const mrPriorityOptions = [
  {
    value: MrPriority.P1,
    label: 'P1',
    color: 'bg-red-600'
  },
  {
    value: MrPriority.P2,
    label: 'P2',
    color: 'bg-orange-500'
  },
  {
    value: MrPriority.P3,
    label: 'P3',
    color: 'bg-yellow-400'
  },
  {
    value: MrPriority.P4,
    label: 'P4',
    color: 'bg-gray-300'
  }
]
