import React, { useCallback, useEffect, useRef, useState } from 'react'
import P<PERSON><PERSON>nstall from '@khmyznikov/pwa-install/react-legacy'
import '@khmyznikov/pwa-install'

type AnyCustomEvent = CustomEvent & { detail?: { message?: string } }

export default function InstallPrompt() {
  const installRef = useRef<any>(null)
  const [externalEvent, setExternalEvent] = useState<BeforeInstallPromptEvent | undefined>(undefined)
  const [available, setAvailable] = useState(false)

  useEffect(() => {
    // pick up the deferred prompt saved at HTML boot
    setExternalEvent(window.promptEvent)
  }, [])

  // Listen for deferred prompt that may arrive after React mount
  useEffect(() => {
    const handler = (evt: Event) => {
      try {
        const custom = evt as CustomEvent<{ event?: BeforeInstallPromptEvent }>
        const e = custom?.detail?.event || (window as any).promptEvent
        if (e) setExternalEvent(e)
      } catch {
        const e = (window as any).promptEvent as BeforeInstallPromptEvent | undefined
        if (e) setExternalEvent(e)
      }
    }
    window.addEventListener('pwa:beforeinstallprompt', handler as EventListener)
    return () => window.removeEventListener('pwa:beforeinstallprompt', handler as EventListener)
  }, [])

  // Event callbacks (declared before binding useEffect to avoid "used before declaration" TS errors)
  const onAvailable = useCallback((e: AnyCustomEvent) => {
    setAvailable(true)
    console.debug('[PWA] install available:', e?.detail?.message)
  }, [])
  const onSuccess = useCallback((e: AnyCustomEvent) => {
    console.info('[PWA] install success:', e?.detail?.message)
  }, [])
  const onFail = useCallback((e: AnyCustomEvent) => {
    console.warn('[PWA] install fail:', e?.detail?.message)
  }, [])
  const onChoice = useCallback((e: AnyCustomEvent) => {
    console.debug('[PWA] user choice:', e?.detail?.message)
  }, [])
  const onHowTo = useCallback((e: AnyCustomEvent) => {
    console.debug('[PWA] how to:', e?.detail?.message)
  }, [])
  const onGallery = useCallback((e: AnyCustomEvent) => {
    console.debug('[PWA] gallery:', e?.detail?.message)
  }, [])
  // Bind native CustomEvent listeners directly on the web component to avoid
  // any react-legacy wrapper naming mismatches.
  useEffect(() => {
    const el = installRef.current as
      | (HTMLElement & EventTarget & { externalPromptEvent?: BeforeInstallPromptEvent })
      | null
    if (!el) return

    el.addEventListener('pwa-install-available-event', onAvailable as EventListener)
    el.addEventListener('pwa-install-success-event', onSuccess as EventListener)
    el.addEventListener('pwa-install-fail-event', onFail as EventListener)
    el.addEventListener('pwa-user-choice-result-event', onChoice as EventListener)
    el.addEventListener('pwa-install-how-to-event', onHowTo as EventListener)
    el.addEventListener('pwa-install-gallery-event', onGallery as EventListener)

    return () => {
      el.removeEventListener('pwa-install-available-event', onAvailable as EventListener)
      el.removeEventListener('pwa-install-success-event', onSuccess as EventListener)
      el.removeEventListener('pwa-install-fail-event', onFail as EventListener)
      el.removeEventListener('pwa-user-choice-result-event', onChoice as EventListener)
      el.removeEventListener('pwa-install-how-to-event', onHowTo as EventListener)
      el.removeEventListener('pwa-install-gallery-event', onGallery as EventListener)
    }
  }, [onAvailable, onSuccess, onFail, onChoice, onHowTo, onGallery])

  // Ensure the externalPromptEvent is reflected on the underlying element even if it changes after mount.
  useEffect(() => {
    const el = installRef.current as
      | (HTMLElement & { externalPromptEvent?: BeforeInstallPromptEvent; _init?: () => void })
      | null
    if (el) {
      el.externalPromptEvent = externalEvent
      // Re-init the component so it can re-evaluate availability with the injected event
      el._init?.()
    }
  }, [externalEvent])
  // If we have a deferred prompt event, mark install as available (fallback path)
  useEffect(() => {
    if (externalEvent) {
      setAvailable(true)
    }
  }, [externalEvent])

  const openInstallDialog = useCallback(() => {
    try {
      if (installRef.current?.showDialog) {
        installRef.current.showDialog()
      } else if (installRef.current?.install) {
        installRef.current.install()
      } else if (externalEvent?.prompt) {
        // Fallback: call the saved native event directly
        externalEvent.prompt()
      }
    } catch (err) {
      console.error('[PWA] showDialog/install error', err)
    }
  }, [externalEvent])

  const isStandalone = window.matchMedia('(display-mode: standalone)').matches || (navigator as any).standalone
  const showButton = !isStandalone && (available || !!externalEvent)

  // Auto-open the install dialog once when eligibility is detected
  const autoOpenedRef = useRef(false)
  useEffect(() => {
    if (showButton && !autoOpenedRef.current) {
      autoOpenedRef.current = true
      // Defer to ensure the web component is ready
      setTimeout(() => {
        try {
          openInstallDialog()
        } catch (e) {
          console.warn('[PWA] auto open install failed', e)
        }
      }, 0)
    }
  }, [showButton, openInstallDialog])
  return (
    <>
      <PWAInstall
        ref={installRef}
        // Recommended: rely on your real manifest for name/icon/etc.
        manifestUrl='/manifest.json'
        useLocalStorage
        name='Equalindo 360 Staging'
        description=' '
        disableClose={false}
        installDescription='Install aplikasi untuk akses Equalindo 360 yang lebih mudah di device kamu.'
        externalPromptEvent={externalEvent}
        onPwaInstallAvailableEvent={onAvailable}
        onPwaInstallSuccessEvent={onSuccess}
        onPwaInstallFailEvent={onFail}
        onPwaUserChoiceResultEvent={onChoice}
        onPwaInstallHowToEvent={onHowTo}
        onPwaInstallGalleryEvent={onGallery}
      />
      {/* 
      {showButton && (
        <button
          type='button'
          onClick={openInstallDialog}
          style={{
            position: 'fixed',
            right: '16px',
            bottom: '16px',
            zIndex: 9999,
            padding: '10px 14px',
            borderRadius: 8,
            backgroundColor: '#1976d2',
            color: '#fff',
            border: 'none',
            boxShadow: '0 2px 8px rgba(0,0,0,0.2)',
            cursor: 'pointer'
          }}
          aria-label='Install app'
        >
          Install App
        </button>
      )} */}
    </>
  )
}
