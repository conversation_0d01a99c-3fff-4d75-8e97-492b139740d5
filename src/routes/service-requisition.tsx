import { SrContextProvider } from '@/pages/service-requisition/context/SrContext'
import { SrFormProvider } from '@/pages/service-requisition/context/SrFormContext'
import { retryDynamicImport } from '@/utils/retryDynamicImport'
import { Outlet, RouteObject } from 'react-router-dom'

const SrApprovalListPage = retryDynamicImport(() => import('@/pages/service-requisition/approval'))
const SrDetailPage = retryDynamicImport(() => import('@/pages/service-requisition/detail'))
const SrListPage = retryDynamicImport(() => import('@/pages/service-requisition/list'))
const SrApprovalDetailPage = retryDynamicImport(() => import('@/pages/service-requisition/approval-detail'))
const SrCreatePage = retryDynamicImport(() => import('@/pages/service-requisition/create'))

export const serviceRequisitionRoutes = [
  {
    path: '/service-request',
    element: (
      <SrContextProvider>
        <Outlet />
      </SrContextProvider>
    ),
    children: [
      {
        path: 'create',
        element: (
          <SrFormProvider>
            <SrCreatePage />
          </SrFormProvider>
        )
      },
      {
        path: 'list',
        children: [
          {
            element: <SrListPage />,
            index: true
          },
          {
            path: ':srId',
            element: <SrDetailPage />
          }
        ]
      },
      {
        path: 'approval',
        children: [
          {
            element: <SrApprovalListPage />,
            index: true
          },
          {
            path: ':srId',
            element: <SrApprovalDetailPage />
          }
        ]
      }
    ]
  }
] as RouteObject[]
