import { CashBankReportProvider } from '@/pages/accounting/reports/cashbank/context/CashBankReportContext'
import { BalanceSheetProvider } from '@/pages/accounting/reports/context/BalanceSheetContext'
import { CashFlowProvider } from '@/pages/accounting/reports/context/CashFlowContext'
import { FinanceReportProvider } from '@/pages/accounting/reports/context/FinanceReportContext'
import { FinancialFocusProvider } from '@/pages/accounting/reports/context/FinancialFocusContext'
import { LiquidityRatioProvider } from '@/pages/accounting/reports/context/LiquidityRatioContext'
import { NetAssetProvider } from '@/pages/accounting/reports/context/NetAssetContext'
import { OwnerEquityProvider } from '@/pages/accounting/reports/context/OwnerEquityContext'
import { ProfitLossProvider } from '@/pages/accounting/reports/context/ProfitLossContext'
import { RetainedEarningsProvider } from '@/pages/accounting/reports/context/RetainedEarningsContext'
import { RevenueExpenseProvider } from '@/pages/accounting/reports/context/RevenueExpenseContext'
import { DebtSuppliersReportProvider } from '@/pages/accounting/reports/debt-suppliers/context/DebtSuppliersContext'
import { LedgerReportProvider } from '@/pages/accounting/reports/ledger/context/LedgerReportContext'
import { ReceivablesCustomersReportProvider } from '@/pages/accounting/reports/receivables-customers/context/ReceivablesCustomerContext'
import { retryDynamicImport } from '@/utils/retryDynamicImport'
import { Outlet, RouteObject } from 'react-router-dom'

const FinanceReport = retryDynamicImport(() => import('@/pages/accounting/reports/finance'))
const FinancialFocusReport = retryDynamicImport(() => import('@/pages/accounting/reports/financial-focus'))
const ProfitLostReport = retryDynamicImport(() => import('@/pages/accounting/reports/profit-loss'))
const CashFlowReport = retryDynamicImport(() => import('@/pages/accounting/reports/cash-flow'))
const RevenueExpenseReport = retryDynamicImport(() => import('@/pages/accounting/reports/revenue-expense'))
const NetAssetReport = retryDynamicImport(() => import('@/pages/accounting/reports/net-asset'))
const LiquidityRatioReport = retryDynamicImport(() => import('@/pages/accounting/reports/liquidity-ratio'))
const OwnerEquityReport = retryDynamicImport(() => import('@/pages/accounting/reports/owner-equity'))
const BalanceSheetReport = retryDynamicImport(() => import('@/pages/accounting/reports/balance-sheet'))
const RetainedEarningsReport = retryDynamicImport(() => import('@/pages/accounting/reports/retained-earnings'))

const LedgerReports = retryDynamicImport(() => import('@/pages/accounting/reports/ledger'))

const CashBankReports = retryDynamicImport(() => import('@/pages/accounting/reports/cashbank'))

const ReceivablesCustomers = retryDynamicImport(() => import('@/pages/accounting/reports/receivables-customers'))

const DebtSuppliers = retryDynamicImport(() => import('@/pages/accounting/reports/debt-suppliers'))

export const reportsRoutes = [
  {
    path: '/report',
    children: [
      {
        path: 'finance',
        element: (
          <FinanceReportProvider>
            <Outlet />
          </FinanceReportProvider>
        ),
        children: [
          {
            index: true,
            element: <FinanceReport />
          },
          {
            path: 'financial-focus',
            element: (
              <FinancialFocusProvider>
                <FinancialFocusReport />
              </FinancialFocusProvider>
            )
          },
          {
            path: 'profit-loss',
            element: (
              <ProfitLossProvider>
                <ProfitLostReport />
              </ProfitLossProvider>
            )
          },
          {
            path: 'cash-flow',
            element: (
              <CashFlowProvider>
                <CashFlowReport />
              </CashFlowProvider>
            )
          },
          {
            path: 'revenue-expense',
            element: (
              <RevenueExpenseProvider>
                <RevenueExpenseReport />
              </RevenueExpenseProvider>
            )
          },
          {
            path: 'net-asset',
            element: (
              <NetAssetProvider>
                <NetAssetReport />
              </NetAssetProvider>
            )
          },
          {
            path: 'liquidity-ratio',
            element: (
              <LiquidityRatioProvider>
                <LiquidityRatioReport />
              </LiquidityRatioProvider>
            )
          },
          {
            path: 'owner-equity',
            element: (
              <OwnerEquityProvider>
                <OwnerEquityReport />
              </OwnerEquityProvider>
            )
          },
          {
            path: 'balance-sheet',
            element: (
              <BalanceSheetProvider>
                <BalanceSheetReport />
              </BalanceSheetProvider>
            )
          },
          {
            path: 'retained-earnings',
            element: (
              <RetainedEarningsProvider>
                <RetainedEarningsReport />
              </RetainedEarningsProvider>
            )
          }
        ]
      },
      {
        path: 'ledger',
        element: (
          <LedgerReportProvider>
            <Outlet />
          </LedgerReportProvider>
        ),
        children: [
          {
            index: true,
            element: <LedgerReports />
          }
        ]
      },
      {
        path: 'cash-bank',
        element: (
          <CashBankReportProvider>
            <Outlet />
          </CashBankReportProvider>
        ),
        children: [
          {
            index: true,
            element: <CashBankReports />
          }
        ]
      },
      {
        path: 'receivables-customers',
        element: (
          <ReceivablesCustomersReportProvider>
            <Outlet />
          </ReceivablesCustomersReportProvider>
        ),
        children: [
          {
            index: true,
            element: <ReceivablesCustomers />
          }
        ]
      },
      {
        path: 'debt-suppliers',
        element: (
          <DebtSuppliersReportProvider>
            <Outlet />
          </DebtSuppliersReportProvider>
        ),
        children: [
          {
            index: true,
            element: <DebtSuppliers />
          }
        ]
      }
    ]
  }
] as RouteObject[]
