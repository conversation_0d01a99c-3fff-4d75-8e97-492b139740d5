import { SoContextProvider } from '@/pages/service-order/context/SoContext'
import { SrListContextProvider } from '@/pages/service-order/context/SrListContext'
import { retryDynamicImport } from '@/utils/retryDynamicImport'
import { Outlet, RouteObject } from 'react-router-dom'

const SoApprovalPage = retryDynamicImport(() => import('@/pages/service-order/approval'))
const SoApprovalDetailPage = retryDynamicImport(() => import('@/pages/service-order/approval-detail'))
const CreateSoPage = retryDynamicImport(() => import('@/pages/service-order/create'))
const SoDetailPage = retryDynamicImport(() => import('@/pages/service-order/detail'))
const SoListPage = retryDynamicImport(() => import('@/pages/service-order/list'))
const SrListPage = retryDynamicImport(() => import('@/pages/service-order/sr-list'))
const SrDetailPage = retryDynamicImport(() => import('@/pages/service-order/sr-detail'))

export const serviceOrderRoutes = [
  {
    path: '/service-order',
    element: (
      <SoContextProvider>
        <Outlet />
      </SoContextProvider>
    ),
    children: [
      {
        path: 'sr-list',
        element: (
          <SrListContextProvider>
            <Outlet />
          </SrListContextProvider>
        ),
        children: [
          {
            element: <SrListPage />,
            index: true
          },
          {
            path: ':srId',
            children: [
              {
                element: <SrDetailPage />,
                index: true
              },
              {
                path: 'so',
                children: [
                  {
                    path: 'create',
                    element: <CreateSoPage />
                  },
                  {
                    path: ':soId',
                    element: <SoDetailPage />
                  }
                ]
              }
            ]
          }
        ]
      },
      {
        path: 'list',
        children: [
          {
            element: <SoListPage />,
            index: true
          },
          {
            path: ':soId',
            element: <SoDetailPage />
          }
        ]
      },
      {
        path: 'approval',
        children: [
          {
            element: <SoApprovalPage />,
            index: true
          },
          {
            path: ':soId',
            element: <SoApprovalDetailPage />
          }
        ]
      }
    ]
  }
] as RouteObject[]
