import { SalesInvoiceTaxTypes } from '@/types/salesInvoiceTypes'

export const calculateTax = (price: number, taxType?: string, taxPercentage?: number): number => {
  let taxAmount = 0
  if (!taxType || !taxPercentage) return taxAmount

  if (taxType === SalesInvoiceTaxTypes.EXCLUDE_TAX) {
    taxAmount = price * taxPercentage
  } else if (taxType === SalesInvoiceTaxTypes.INCLUDE_TAX) {
    taxAmount = price * ((taxPercentage / (100 + taxPercentage)) * 100)
  }

  return Math.round(taxAmount) / 100
}
