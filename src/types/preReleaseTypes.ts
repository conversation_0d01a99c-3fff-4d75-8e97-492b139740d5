import { CodeNameType } from './common'
import { SiteOutlineType, UnitType } from './companyTypes'
import { CheckPointType, ParameterType } from './parameterTypes'
import { ListParams } from './payload'
import { UserOutlineType } from './userTypes'
import { WorkOrderType } from './woTypes'

export enum PreReleaseStatusType {
  ASSIGNED = 'ASSIGNED',
  PROCESSED = 'PROCESSED',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  CANCELED = 'CANCELED'
}

export type PreReleaseDtoType = {
  workOrderId: string
  templateId: string
  assignedTo: string
  checkPoints: {
    templateCheckPointId: string
    isChecked?: boolean
    note?: string
  }[]
  approvals: {
    userId: string
  }[]
  workName: string
  workType: 'SCHEDULED' | 'UNSCHEDULED'
  note: string
}

export type PreReleaseChecklistDto = {
  checkPoints: {
    id: number
    isChecked: boolean
    note: string
  }[]
  note: string
}

export type ReleaseDtoType = {
  takenBy: string
  takenImageUploadId: string
  takenImageName?: string
}

export type PreReleaseApprovalDtoType = {
  status: 'APPROVED' | 'REJECTED'
  rejectionNote?: string
}

export type PreReleaseParams = {
  workOrderId?: string
  status?: PreReleaseStatusType
} & ListParams

export type PreReleaseChecklistType = {
  createdAt: string
  id: number
  isChecked: boolean
  note: string
  parameter: ParameterType
  parameterId: string
  preReleaseId: string
  updatedAt: string
  templateCheckPoint: CheckPointType
  templateCheckPointId: string
}

export type PreReleaseApprovalType = {
  id: number
  level: number
  preReleaseId: string
  userId: string
  note: string
  status: string
  waitingAt: string
  isRead: boolean
  readAt: string
  respondedAt: string
  rejectionNote: string
  responseTime: string
  createdAt: string
  updatedAt: string
  user: UserOutlineType
}

export type PreReleaseType = {
  id: string
  number: string
  checklistCount: number
  approvalsCount: number
  assignedTo: string
  assignedToUser: UserOutlineType
  checkedBy: string
  checkedByUser: UserOutlineType
  checkedAt: string
  checkPointsCount: number
  note: string
  cancelationNote: string
  status: PreReleaseStatusType
  companyId: string
  parentCompanyId: string
  departmentId: string
  siteId: string
  createdAt: string
  updatedAt: string
  createdBy: string
  workOrder: WorkOrderType
  workName: string
  workType: string
  unit: UnitType
  template: {
    id: string
    title: string
  }
  checkPoints: PreReleaseChecklistType[]
  approvals: PreReleaseApprovalType[]
  priority: number
  department: CodeNameType
  site: SiteOutlineType
  createdByUser: UserOutlineType
}
