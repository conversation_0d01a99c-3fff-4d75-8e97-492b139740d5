import { CodeNameType } from './common'
import { UnitType } from './companyTypes'
import { ListParams } from './payload'
import { UserOutlineType } from './userTypes'

export enum FieldReportStatus {
  CREATED = 'CREATED',
  PROCESSED = 'PROCESSED',
  CLOSED = 'CLOSED'
}

export type FrType = {
  id: string
  companyId: string
  closeReason: string
  priority: number
  parentCompanyId: string
  departmentId: string
  siteId: string
  destinationSiteId: string
  unitId: string
  unitKm: number
  unitHm: number
  number: string
  initialReport: string
  locationPit: number
  locationNote: string
  symptom: string
  cause: string
  status: string
  scheduleDate: string
  scheduleReminderDate: string
  scheduleReminderType: string
  isEditable: boolean
  breakdownTime: string
  department: CodeNameType
  site: CodeNameType
  destinationSite: CodeNameType
  unit: UnitType
  createdByUser: UserOutlineType
  createdAt: string
}

export type FrParams = {
  status?: FieldReportStatus
  isScheduled?: boolean
  destinationSiteIds?: string
} & ListParams

export type FrLogType = {
  id: number
  fieldReportId: string
  userId: string
  type: string
  status: string
  changes: string
  createdAt: string
  user: UserOutlineType
}
