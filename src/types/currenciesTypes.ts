import { AccountType } from '@/types/accountTypes'

export type CurrenciesType = {
  id: string
  code: string
  name: string
  symbol: string
  isDefault?: boolean
  payableAccountId?: string
  receivableAccountId?: string
  purchaseDownPaymentAccountId?: string
  salesDownPaymentAccountId?: string
  companyId?: string
  parentCompanyId?: string
  createdAt?: string
  updatedAt?: string
  payableAccount?: AccountType
  receivableAccount?: AccountType
  purchaseDownPaymentAccount?: AccountType
  salesDownPaymentAccount?: AccountType
  purchaseDiscountAccount?: AccountType
  salesDiscountAccount?: AccountType
  transitAssetAccount?: AccountType
}

export type CurrenciesPayload = {
  code: string
  name: string
  symbol: string
  isDefault?: boolean
  companyId?: string
  payableAccountId: string
  receivableAccountId: string
  purchaseDownPaymentAccountId: string
  salesDownPaymentAccountId: string
}
