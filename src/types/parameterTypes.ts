import { CategoryType } from './companyTypes'
import { ListParams } from './payload'
import { UserOutlineType } from './userTypes'

export type ParameterParams = {
  unitSubCategoryId?: string
  unitCategoryId?: string
  companyId?: string
} & ListParams

export type PreReleaseTemplateDto = {
  title: string
  description: string
  checkPoints: {
    name: string
    description: string
  }[]
  unitCategoryId: string
  unitSubCategoryId: string
  companyId: string
}

export type CheckPointType = {
  id: string
  name: string
  description: string
}

export type ParameterType = {
  id: string
  title: string
  description: string
  checkPointsCount: number
  companyId: string
  parentCompanyId: string
  createdAt: string
  updatedAt: string
  createdBy: string
  checkPoints: CheckPointType[]
  unitCategory: CategoryType
  unitSubCategory: CategoryType
  createdByUser: UserOutlineType
}

export type ActivityLogsParams = {
  resourceId: string
  resourceType: 'item' | 'vendor' | 'unit'
} & ListParams
