import { CodeNameType } from './common'
import { ItemType, UnitType } from './companyTypes'
import { ImagePayload, ListParams, UserIdPayload } from './payload'
import { SerialNumberType } from './serialNumber'
import { UserOutlineType } from './userTypes'
import { WorkOrderType, WoSegmentType } from './woTypes'

export type PartSwapStatus = 'PROCESSED' | 'APPROVED' | 'REJECTED' | 'CANCELED' | 'CLOSED'

export enum PartSwapLogStatus {
  CREATED = 'CREATED',
  UPDATED = 'UPDATED',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  CLOSED = 'CLOSED',
  CANCELED = 'CANCELED',
  ITEM_UPDATED = 'ITEM_UPDATED',
  APPROVAL_APPROVED = 'APPROVAL_APPROVED',
  APPROVAL_REJECTED = 'APPROVAL_REJECTED',
  APPROVAL_UPDATED = 'APPROVAL_UPDATED'
}

export type PartSwapParams = {
  workOrderSegmentId?: string
  siteIds?: string
  status?: PartSwapStatus
  isApproved?: boolean
  isClosed?: boolean
} & ListParams

export type PartSwapItemType = {
  swapType: 'EXCHANGED' | 'MOVED'
  originItem?: ItemType
  originItemId: string
  originSerialNumber: string
  destinationItemId: string
  destinationSerialNumberId: number
  quantity: number
  quantityUnit: string
  largeUnitQuantity: number
  note: string
  images: ImagePayload[]
}

export type WoPartSwapDtoType = {
  workOrderSegmentId: string
  items: PartSwapItemType[]
  approvals: UserIdPayload[]
  note: string
  priority: number
  originUnitSiteId: string
  originUnitId: string
  originUnitKm: number
  originUnitHm: number
  destinationUnitSiteId: string
  destinationUnitId: string
  destinationUnitKm: number
  destinationUnitHm: number
}

export type PartSwapApproval = {
  id: number
  level: number
  partSwapId: string
  userId: string
  note: string
  status: string
  waitingAt: string
  isRead: boolean
  readAt: string
  respondedAt: string
  rejectionNote: string
  responseTime: string
  createdAt: string
  updatedAt: string
  user: UserOutlineType
}

export type PartSwapItem = {
  id: number
  partSwapId: string
  swapType: string
  originItemId: string
  originSerialNumberId: number
  destinationItemId: string
  destinationSerialNumberId: number
  quantity: number
  quantityUnit: string
  isLargeUnit: boolean
  largeUnitQuantity: number
  smallQuantity: number
  note: string
  workOrderSegmentItemId: number
  isClosed: boolean
  createdAt: string
  updatedAt: string
  originItem: ItemType
  originSerialNumber: SerialNumberType
  destinationItem: ItemType
  destinationSerialNumber: SerialNumberType
  images: string[]
}

export type PartSwapType = {
  id: string
  number: string
  itemsCount: number
  approvalsCount: number
  originUnitSiteId: string
  originUnitId: string
  originUnitKm: number
  originUnitHm: number
  destinationUnitSiteId: string
  destinationUnitId: string
  destinationUnitKm: number
  destinationUnitHm: number
  note: string
  cancelationNote: string
  status: string
  priority: number
  companyId: string
  parentCompanyId: string
  departmentId: string
  siteId: string
  workOrderId: string
  workOrderSegmentId: string
  isClosed: boolean
  isEditable: boolean
  createdAt: string
  updatedAt: string
  approvedAt: string
  createdBy: string
  items: PartSwapItem[]
  approvals: PartSwapApproval[]
  workOrder: WorkOrderType
  workOrderSegment: WoSegmentType
  department: CodeNameType
  originUnitSite: CodeNameType
  originUnit: UnitType
  destinationUnitSite: CodeNameType
  destinationUnit: UnitType
  site: CodeNameType
  createdByUser: UserOutlineType
}

export type PartSwapLogType = {
  id: number
  type: string
  status: string
  changes: string
  attachmentUrl: string
  attachmentMimeType: string
  partSwapId: string
  userId: string
  partSwapItemId: number
  createdAt: string
  user: UserOutlineType
  partSwapItem: {
    id: number
    partSwapId: string
    swapType: string
    originItemId: string
    originSerialNumberId: number
    destinationItemId: string
    destinationSerialNumberId: number
    quantity: number
    quantityUnit: string
    isLargeUnit: boolean
    largeUnitQuantity: number
    smallQuantity: number
    note: string
    createdAt: string
    updatedAt: string
    originItem: ItemType
    originSerialNumber: SerialNumberType
    destinationItem: ItemType
    destinationSerialNumber: SerialNumberType
    images: ImagePayload[]
  }
}
