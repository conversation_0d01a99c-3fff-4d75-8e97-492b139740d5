import { CodeNameType } from './common'
import { SiteType } from './companyTypes'
import { ListParams } from './payload'
import { UserOutlineType } from './userTypes'

export enum DraftScope {
  'MATERIAL-REQUEST' = 'material-request',
  'PURCHASE-REQUISITION' = 'purchase-requisition',
  'PURCHASE-ORDER' = 'purchase-order',
  'OUTGOING-MATERIAL' = 'outgoing-material',
  RMA = 'rma',
  'STOCK-RETURN' = 'stock-return',
  'STOCK-MOVEMENT' = 'stock-movement',
  'MATERIAL-TRANSFER' = 'material-transfer',
  'MATERIAL-BORROW' = 'material-borrow',
  'PAYMENT' = 'payment',
  'CASH-RECEIPT' = 'cash-receipt',
  'PURCHASE-INVOICE' = 'purchase-invoice',
  'SALES-INVOICE' = 'sales-invoice',
  'GENERAL-LEDGER' = 'general-journal'
}

export type DraftPayload = {
  scope: DraftScope
  payload: string
  siteId: string
}

export type DraftParams = {
  scope?: DraftScope
} & ListParams

export type DraftType = {
  id: string
  scope: string
  payload: string
  companyId: string
  parentCompanyId: string
  departmentId: string
  siteId: string
  createdAt: string
  updatedAt: string
  expiredAt: string
  department: CodeNameType
  site: SiteType
  createdByUser: UserOutlineType
}
