import { PoType } from '@/pages/purchase-order/config/types'
import { WarehouseItemType } from './appTypes'
import { CompanyType, DepartmentType, ImageType, ItemType, SiteType, UnitType, VendorType } from './companyTypes'
import { ApproverType, UserOutlineType } from './userTypes'
import { ServiceRequisition } from './serviceRequisitionsTypes'
import { WorkOrderType, WoSegmentType } from './woTypes'
import { ListParams, UserIdPayload } from './payload'
import { PurchaseOrderCancelationType } from '@/pages/purchase-order/config/enum'

export enum SerialNumberCondition {
  NEW = 'NEW',
  EX_REPAIR = 'EX_REPAIR'
}

export enum SerialNumberStatus {
  IN_STOCK = 'IN_STOCK'
}

export enum ServiceOrderType {
  INTERNAL = 'INTERNAL',
  EXTERNAL = 'EXTERNAL',
  VENDOR = 'VENDOR'
}

export enum ServiceOrderStatus {
  PROCESSED = 'PROCESSED',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  CANCELED = 'CANCELED',
  CANCEL_REQUESTED = 'CANCEL_REQUESTED',
  CLOSED = 'CLOSED'
}

export enum ServiceOrderPriority {
  P1 = 1,
  P2 = 2,
  P3 = 3,
  P4 = 4
}

export enum ServiceOrderApprovalStatus {
  PENDING = 'PENDING',
  WAITING = 'WAITING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED'
}

export enum ServiceOrderLogType {
  ACTIVITY = 'ACTIVITY'
}

export enum ServiceOrderLogStatus {
  CREATED = 'CREATED',
  UPDATED = 'UPDATED',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  CANCEL_REQUESTED = 'CANCEL_REQUESTED',
  CANCEL_APPROVED = 'CANCEL_APPROVED',
  CANCEL_REJECTED = 'CANCEL_REJECTED',
  CLOSED = 'CLOSED',
  CANCELED = 'CANCELED',
  ITEM_UPDATED = 'ITEM_UPDATED',
  APPROVAL_APPROVED = 'APPROVAL_APPROVED',
  APPROVAL_REJECTED = 'APPROVAL_REJECTED',
  APPROVAL_UPDATED = 'APPROVAL_UPDATED'
}

export enum ServiceOrderCancelationType {
  ITEM_UNAVAILABLE = 'ITEM_UNAVAILABLE'
}

export enum ServiceOrderTaxType {
  NON_TAX = 'NON_TAX',
  INCLUDE_TAX = 'INCLUDE_TAX',
  EXCLUDE_TAX = 'EXCLUDE_TAX'
}

export enum ServiceOrderDiscountType {
  PERCENTAGE = 'PERCENTAGE',
  FLAT = 'FLAT'
}

export enum ServiceOrderPaymentMethod {
  TERM = 'TERM',
  CASH = 'CASH'
}

export enum ServiceOrderCurrency {
  IDR = 'IDR',
  USD = 'USD'
}

export enum ServiceOrderSortFields {
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
  APPROVED_AT = 'approvedAt',
  PRIORITY = 'priority'
}

export enum ServiceOrderCancelationType {
  CANNOT_REPAIR = 'CANNOT_REPAIR'
}

export interface ISummary {
  totalServiceFee: number
  discountAmount: number
  totalDiscount: number
  grandTotal: number
  lcGrandTotal: number
}

export interface IItemSummary {
  subtotalServiceFee: number
  taxAmount: number
  subtotalDiscount: number
  totalServiceFee: number
  lcTotalServiceFee: number
}

export type SerialNumber = {
  id: number
  number: string
  condition: SerialNumberCondition
  note?: string
  status: SerialNumberStatus
  itemId: string
  purchaseOrderId?: string
  purchaseOrderItemId?: number
  companyId: string
  parentCompanyId?: string
  siteId: string
  createdAt: string
  updatedAt: string
  deletedAt?: string
  createdBy?: string
  updatedBy?: string
  deletedBy?: string

  // Relations
  purchaseOrder?: PoType
  item?: ItemType
  site?: SiteType
  company?: CompanyType
  parentCompany?: CompanyType
}

export type ServiceOrderItem = {
  id: number
  serviceOrderId: string
  itemId: string
  serialNumberId?: number
  quantity: number
  quantityUnit: string
  isLargeUnit: boolean
  largeUnitQuantity: number
  smallQuantity: number
  note?: string
  serviceName?: string
  serviceDescription?: string
  serviceFee: number
  subtotalServiceFee: number
  taxType: string
  taxPercentage: number
  taxAmount: number
  discountType?: string
  discountValue?: number
  isDiscountAfterTax?: boolean
  subtotalDiscount?: number
  totalServiceFee: number
  lcTotalServiceFee: number
  serviceRequisitionItemId?: number
  workOrderSegmentItemId?: number
  isClosed?: boolean
  createdAt: string
  updatedAt: string
  deletedAt?: string
  createdBy?: string
  updatedBy?: string
  deletedBy?: string
  item?: ItemType
  serialNumber?: SerialNumber
  images?: ImageType[]
} & WarehouseItemType

export type ServiceOrderApproval = {
  id: number
  level: number
  serviceOrderId: string
  userId: string
  note?: string
  isCancelation: boolean
  status: ServiceOrderApprovalStatus
  waitingAt?: string
  isRead: boolean
  readAt?: string
  respondedAt?: string
  rejectionNote?: string
  responseTime?: number
  createdAt: string
  updatedAt: string
  deletedAt?: string
  createdBy?: string
  updatedBy?: string
  deletedBy?: string
  user?: UserOutlineType
}

export type ServiceOrderLog = {
  id: number
  type: ServiceOrderLogType
  status: ServiceOrderLogStatus
  changes?: string
  attachmentUrl?: string
  attachmentMimeType?: string
  serviceOrderId: string
  userId?: string
  serviceOrderItemId?: number
  createdAt: string
  user?: UserOutlineType
  serviceOrderItem?: ServiceOrderItem
}

export type ServiceOrder = {
  id: string
  number: string
  type: string
  itemsCount: number
  approvalsCount: number
  note?: string
  cancelationType?: string
  cancelationProofUrl?: string
  cancelationProofMimeType?: string
  cancelationNote?: string
  vendorId: string
  documentNumber?: string
  documentUrl?: string
  documentContent?: string
  documentName?: string
  documentMimeType?: string
  documentNote?: string
  originSiteId?: string
  currency: ServiceOrderCurrency
  exchangeRate: number
  totalServiceFee: number
  discountType?: string
  discountValue?: number
  discountAmount?: number
  totalDiscount: number
  shippingCost: number
  grandTotal: number
  lcGrandTotal: number
  paymentMethod?: string
  paymentDueDate?: string
  estimateCompletedTime?: string
  status: ServiceOrderStatus
  priority: ServiceOrderPriority
  companyId: string
  parentCompanyId?: string
  departmentId?: string
  siteId?: string
  unitId?: string
  unitKm?: number
  unitHm?: number
  serviceRequisitionId?: string
  workOrderId?: string
  workOrderSegmentId?: string
  isClosed: boolean
  isEditable: boolean
  createdAt: string
  updatedAt: string
  approvedAt?: string
  deletedAt?: string
  createdBy?: string
  updatedBy?: string
  deletedBy?: string
  signature?: string
  items?: ServiceOrderItem[]
  approvals?: ApproverType[]
  vendor?: VendorType
  serviceRequisition?: ServiceRequisition
  workOrder?: WorkOrderType
  workOrderSegment?: WoSegmentType
  company?: CompanyType
  parentCompany?: CompanyType
  department?: DepartmentType
  site?: SiteType
  originSite?: SiteType
  unit?: UnitType
  createdByUser?: UserOutlineType
}

export type ServiceOrderItemPayload = {
  id?: number
  originalItemId?: string
  itemId?: string
  serialNumberId?: number
  quantity?: number
  quantityUnit?: string
  largeUnitQuantity?: number
  serviceName?: string
  serviceDescription?: string
  serviceFee?: number
  taxType?: string
  taxPercentage?: number
  discountType?: string
  discountValue?: number
  isDiscountAfterTax?: boolean
  note?: string
} & WarehouseItemType

export type ServiceOrderPayload = {
  soId?: string
  serviceRequisitionId?: string
  vendorId?: string
  documentNumber?: string
  documentUploadId?: string
  documentNote?: string
  documentName?: string
  documentContent?: string
  currency?: ServiceOrderCurrency
  exchangeRate?: number
  items?: ServiceOrderItemPayload[]
  approvals?: {
    userId: string
  }[]
  note?: string
  priority?: ServiceOrderPriority
  discountType?: ServiceOrderDiscountType
  discountValue?: number
  shippingCost?: number
  paymentMethod?: ServiceOrderPaymentMethod
  paymentDueDate?: string
  estimateCompletedTime?: string
  totalTax?: number
  totalPrice?: number
  discountAmount?: number
  subTotalItems?: number
  totalDiscount?: number
  grandTotal?: number
}

export type ServiceOrderParams = {
  endDate?: string
  startDate?: string
  isClosed?: boolean
  status?: ServiceOrderStatus
  materialRequestId?: string
  priority?: string
  workOrderSegmentId?: string
  serviceRequisitionId?: string
} & ListParams

export type CancelServiceOrderPayload = {
  soId?: string
  cancelationType?: string
  approvals?: UserIdPayload[]
  cancelationProofUploadId?: string
  cancelationNote?: string
}

export type ServiceOrderApprovalPayload = {
  soId?: string
  approvalId?: number
  userId?: string
  note?: string
  status?: ServiceOrderApprovalStatus
  isRead?: boolean
}
