import { CodeNameType } from './common'
import { SiteOutlineType } from './companyTypes'
import { ListParams } from './payload'
import { UserOutlineType } from './userTypes'

export enum ProjectStatus {
  ACTIVE = 'ACTIVE',
  FINISHED = 'FINISHED'
}

export type ProjectType = {
  id: string
  companyId: string
  parentCompanyId: string
  siteIds: string[]
  code: string
  name: string
  status: string
  materialRequestsCount: number
  purchaseOrdersCount: number
  inboundDocumentsCount: number
  outboundDocumentsCount: number
  requirementAmount: number
  inboundAmount: number
  outboundAmount: number
  isEditable: boolean
  createdAt: string
  updatedAt: string
  finishedAt: string
  createdBy: string
  sites?: SiteOutlineType[]
  createdByUser: UserOutlineType
}

export type ProjectParams = {
  status?: ProjectStatus
  siteId?: string
  projectId?: string
  projectLabelId?: number
} & ListParams

export type ProjectPayload = {
  code: string
  name: string
  siteIds: string[]
}

export type ProjectLabelType = {
  id: number
  projectId: string
  name: string
  description: string
  createdAt: string
  updatedAt: string
  createdBy: string
  createdByUser: UserOutlineType
}

export type ProjectLabelPayload = {
  name: string
  description: string
}
