import { DepartmentType, ImageType, ItemType, SiteType, UnitType, VendorType } from './companyTypes'
import { ApproverType, UserOutlineType } from './userTypes'
import { WorkOrderType, WoSegmentType } from './woTypes'
import { ImagePayload, ListParams, UserIdPayload } from './payload'
import { PurchaseOrderCancelationType } from '@/pages/purchase-order/config/enum'
import { WarehouseItemType } from './appTypes'
import { SerialNumber } from './serviceOrderTypes'

export enum ServiceRequisitionType {
  INTERNAL = 'INTERNAL',
  EXTERNAL = 'EXTERNAL',
  VENDOR = 'VENDOR'
}

export enum ServiceRequisitionStatus {
  PROCESSED = 'PROCESSED',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  CANCELED = 'CANCELED',
  CLOSED = 'CLOSED'
}

export enum ServiceRequisitionPriority {
  P1 = 1,
  P2 = 2,
  P3 = 3,
  P4 = 4
}

export enum ServiceRequisitionApprovalStatus {
  PENDING = 'PENDING',
  WAITING = 'WAITING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED'
}

export enum ServiceRequisitionLogType {
  ACTIVITY = 'ACTIVITY'
}

export enum ServiceRequisitionLogStatus {
  CREATED = 'CREATED',
  UPDATED = 'UPDATED',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  CLOSED = 'CLOSED',
  CANCELED = 'CANCELED',
  ITEM_UPDATED = 'ITEM_UPDATED',
  APPROVAL_APPROVED = 'APPROVAL_APPROVED',
  APPROVAL_REJECTED = 'APPROVAL_REJECTED',
  APPROVAL_UPDATED = 'APPROVAL_UPDATED'
}

export enum ServiceRequisitionSortFields {
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
  APPROVED_AT = 'approvedAt',
  PRIORITY = 'priority'
}

export type ServiceRequisitionItem = {
  id: number
  serviceRequisitionId: string
  itemId: string
  serialNumber?: SerialNumber
  serialNumberId?: number
  quantity: number
  quantityUnit: string
  isLargeUnit: boolean
  largeUnitQuantity: number
  smallQuantity: number
  note?: string
  remainingQuantity: number
  workOrderSegmentItemId?: number
  isClosed?: boolean
  createdAt: string
  updatedAt: string
  deletedAt?: string
  createdBy?: string
  updatedBy?: string
  deletedBy?: string
  item?: ItemType
  images?: ImageType[]
} & WarehouseItemType

export type ServiceRequisitionApproval = {
  id: number
  level: number
  serviceRequisitionId: string
  userId: string
  note?: string
  status: ServiceRequisitionApprovalStatus
  waitingAt?: string
  isRead: boolean
  readAt?: string
  respondedAt?: string
  rejectionNote?: string
  responseTime?: number
  createdAt: string
  updatedAt: string
  deletedAt?: string
  createdBy?: string
  updatedBy?: string
  deletedBy?: string
  user?: UserOutlineType
} & ApproverType

export type ServiceRequisition = {
  id: string
  number: string
  type: string
  itemsCount: number
  approvalsCount: number
  note?: string
  cancelationNote?: string
  vendorId?: string
  documentNumber?: string
  documentUrl?: string
  documentMimeType?: string
  documentNote?: string
  originSiteId?: string
  status: string
  priority: ServiceRequisitionPriority
  serviceOrdersCount: number
  companyId: string
  parentCompanyId?: string
  departmentId?: string
  siteId?: string
  unitId?: string
  unitKm?: number
  unitHm?: number
  workOrderId?: string
  workOrderSegmentId?: string
  isClosed: boolean
  isEditable: boolean
  createdAt: string
  updatedAt: string
  approvedAt?: string
  closedAt?: string
  deletedAt?: string
  createdBy?: string
  closedBy?: string
  updatedBy?: string
  deletedBy?: string
  items?: ServiceRequisitionItem[]
  approvals?: ServiceRequisitionApproval[]
  workOrder?: WorkOrderType
  workOrderSegment?: WoSegmentType
  department?: DepartmentType
  site?: SiteType
  unit?: UnitType
  vendor?: VendorType
  originSite?: SiteType
  createdByUser?: UserOutlineType
}

export type ServiceRequisitionItemPayload = {
  id?: string
  itemId: string
  quantity: number
  quantityUnit: string
  largeUnitQuantity: number
  note: string
  images: ImagePayload[]
}

export type ServiceRequisitionPayload = {
  srId?: string
  type: 'INTERNAL' | 'EXTERNAL' | 'VENDOR'
  workOrderSegmentId: string
  items: ServiceRequisitionItemPayload[]
  approvals: ApproverType[]
  note: string
  priority: number
  documentNumber: string
  documentUploadId: string
  documentNote: string
  vendorId: string
  siteId?: string
  originSiteId: string
}

export type ServiceRequisitionLog = {
  id: number
  type: ServiceRequisitionLogType
  status: ServiceRequisitionLogStatus
  changes?: string
  attachmentUrl?: string
  attachmentMimeType?: string
  serviceRequisitionId: string
  userId?: string
  serviceRequisitionItemId?: number
  createdAt: string
  serviceRequisition?: ServiceRequisition
  user?: UserOutlineType
  serviceRequisitionItem?: ServiceRequisitionItem
}

export type ServiceRequisitionApprovalPayload = {
  srId?: string
  approvalId?: number
  userId?: string
  note?: string
  status?: ServiceRequisitionApprovalStatus
  isRead?: boolean
}

export type ServiceRequisitionParams = {
  endDate?: string
  startDate?: string
  isClosed?: boolean
  status?: ServiceRequisitionStatus
  materialRequestId?: string
  priority?: string
  workOrderSegmentId?: string
} & ListParams

export type CancelServiceRequisitionPayload = {
  srId?: string
  cancelationType?: PurchaseOrderCancelationType
  approvals?: UserIdPayload[]
  cancelationProofUploadId?: string
  cancelationNote?: string
}
