import { CodeType } from './codes'
import { CodeNameType } from './common'
import { DepartmentType, ItemType, SiteType, UnitType } from './companyTypes'
import { FrType } from './frTypes'
import { ImagePayload, ListParams } from './payload'
import { SerialNumberType } from './serialNumber'
import { UserOutlineType } from './userTypes'

export enum WoStatus {
  ACTIVE = 'ACTIVE',
  PRE_RELEASED = 'PRE_RELEASED',
  READY_TO_RELEASE = 'READY_TO_RELEASE',
  COMPLETED = 'COMPLETED'
}

export enum WoSegmentStatus {
  CREATED = 'CREATED',
  PROCESSED = 'PROCESSED',
  COMPLETED = 'COMPLETED'
}

export enum WorkOrderLogStatus {
  CREATED = 'CREATED',
  UPDATED = 'UPDATED',
  PRE_RELEASED = 'PRE_RELEASED',
  PRE_RELEASE_REJECTED = 'PRE_RELEASE_REJECTED',
  READY_TO_RELEASE = 'READY_TO_RELEASE',
  COMPLETED = 'COMPLETED',
  SEGMENT_ADDED = 'SEGMENT_ADDED',
  SEGMENT_UPDATED = 'SEGMENT_UPDATED',
  SEGMENT_PROCESSED = 'SEGMENT_PROCESSED',
  SEGMENT_COMPLETED = 'SEGMENT_COMPLETED',
  SEGMENT_ITEM_ADDED = 'SEGMENT_ITEM_ADDED',
  SEGMENT_ITEM_UPDATED = 'SEGMENT_ITEM_UPDATED'
}

export type WorkSegmentDtoType = {
  items: {
    itemId: string
    type: 'COMPONENT' | 'MISCELLANEOUS'
    serialNumber: string
    quantity: number
    quantityUnit: string
    largeUnitQuantity: number
    note: string
    images: ImagePayload[]
  }[]
  divisionId?: string
  jobCodeId: number
  componentCodeId: number
  modifierCodeId: number
}

export type WorkOrderType = {
  id: string
  companyId: string
  createdSegmentsCount: number
  parentCompanyId: string
  departmentId: string
  siteId: string
  unitId: string
  fieldReportId: string
  unitKm: number
  unitHm: number
  number: string
  diagnosis: string
  status: string
  createdDocumentsCount: number
  isAllSegmentsCompleted: boolean
  isEditable: boolean
  createdAt: string
  updatedAt: string
  createdBy: string
  segments: string[]
  priority: number
  fieldReport: FrType
  department: DepartmentType
  site: SiteType
  destinationSite: SiteType
  unit: UnitType
  createdByUser: UserOutlineType
  takenAt: string
  takenBy: string
  takenImageUrl: string
  note: string
}

export type WoSegmentItem = {
  id: number
  type: string
  quantity: number
  usedQuantity: number
  quantityUnit: string
  isLargeUnit: boolean
  largeUnitQuantity: number
  smallQuantity: number
  note: string
  item: ItemType
  serialNumber: SerialNumberType
  images: { uploadId: string }[]
}

export type WoSegmentType = {
  WorkProcessSegment: {
    workOrderSegmentId: string
    workProcessId: string
  }
  id: string
  companyId: string
  parentCompanyId: string
  divisionId: string
  workOrderId: string
  jobCodeId: number
  componentCodeId: number
  modifierCodeId: number
  number: string
  componentsCount: number
  miscellaneousCount: number
  createdDocumentsCount: number
  status: string
  isEditable: true
  createdAt: string
  updatedAt: string
  createdBy: string
  division: CodeNameType
  items: WoSegmentItem[]
  jobCode: CodeType
  componentCode: CodeType
  modifierCode: CodeType
}

export type WoParams = {
  status?: WoStatus
  isAllSegmentsCompleted?: boolean
} & ListParams

export type WoLogType = {
  id: number
  workOrderId: string
  workOrderSegmentId: string
  userId: string
  type: string
  status: string
  changes: string
  createdAt: string
  user: {
    id: string
    fullName: string
    title: string
    profilePictureUrl: string
  }
}
