import { PoType } from '@/pages/purchase-order/config/types'
import { ItemType } from './companyTypes'
import { ListParams } from './payload'

export type SnParams = {
  itemId?: string
  status?: 'IN_STOCK'
} & ListParams

export type SerialNumberType = {
  id: number
  number: string
  condition: string
  note: string
  status: string
  item: ItemType
  itemId: string
  purchaseOrder: PoType
  purchaseOrderId: string
  purchaseOrderItemId: number
  companyId: string
  parentCompanyId: string
  siteId: string
}
