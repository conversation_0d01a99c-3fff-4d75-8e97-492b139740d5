import { AccountType } from './accountTypes'

export enum JournalReportType {
  STANDARD = 'STANDARD',
  MULTI_PERIOD = 'MULTI_PERIOD',
  PERIOD_COMPARISON = 'PERIOD_COMPARISON',
  CONSOLIDATION = 'CONSOLIDATION',
  PERIOD_BUDGET = 'PERIOD_BUDGET'
}

export type JournalReportParams = {
  includeHeaderBalances?: boolean
  includeZeroAccounts?: boolean
  startDate?: string
  endDate?: string
  type?: JournalReportType
  companyId?: string
}

export type ReportSubSection = {
  headerId: string
  headerCode: string
  headerName: string
  accounts: (AccountType & { amount: number; isContra: boolean })[]
  subTotal: number
}

export type JournalProfitLoss = {
  params: JournalReportParams
  sections: {
    title: string
    subSections: ReportSubSection[]
    subTotal: number
  }[]
  summary: {
    totalRevenue: number
    totalExpense: number
    netIncome: number
  }
}
