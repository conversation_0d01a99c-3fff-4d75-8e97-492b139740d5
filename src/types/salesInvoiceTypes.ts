import { AccountType } from './accountTypes'
import { DepartmentOutlineType } from './assetTypes'
import { ItemType, SiteOutlineType } from './companyTypes'
import { CurrenciesType } from './currenciesTypes'
import { CustomerType } from './customerTypes'
import { ListParams } from './payload'
import { ProjectLabelType } from './projectTypes'
import { TaxType } from './taxTypes'
import { UserOutlineType, UserType } from './userTypes'

export enum SalesInvoiceStatuses {
  PROCESSED = 'PROCESSED',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  CLOSED = 'CLOSED',
  WAITING_RECEIPT = 'WAITING_RECEIPT',
  PAID = 'PAID'
}

export enum SalesInvoiceTaxTypes {
  NON_TAX = 'NON_TAX',
  INCLUDE_TAX = 'INCLUDE_TAX',
  EXCLUDE_TAX = 'EXCLUDE_TAX'
}

export enum SalesInvoiceDiscountTypes {
  PERCENTAGE = 'PERCENTAGE',
  FLAT = 'FLAT'
}

export enum SalesInvoicePaymentTerms {
  COD = 'COD',
  NET = 'NET',
  CBD = 'CBD'
}

export enum SalesInvoiceApprovalStatuses {
  PENDING = 'PENDING',
  WAITING = 'WAITING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED'
}

export enum SalesInvoiceLogMethods {
  ADD_LOG = 'addLog',
  ADD_APPROVAL_LOG = 'addApprovalLog'
}

export enum SalesInvoiceLogTypes {
  ACTIVITY = 'ACTIVITY'
}

export enum SalesInvoiceLogStatuses {
  CREATED = 'CREATED',
  UPDATED = 'UPDATED',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  CLOSED = 'CLOSED',
  APPROVAL_APPROVED = 'APPROVAL_APPROVED',
  APPROVAL_REJECTED = 'APPROVAL_REJECTED',
  APPROVAL_UPDATED = 'APPROVAL_UPDATED'
}

export enum SalesInvoiceSortFields {
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
  APPROVED_AT = 'approvedAt'
}

export type SalesInvoiceApproval = {
  id: number
  salesInvoiceId: string
  userId: string
  level: number
  note?: string
  status: SalesInvoiceApprovalStatuses
  rejectionNote?: string
  responseTime?: number
  isRead: boolean
  createdAt: string
  updatedAt: string
  waitingAt?: string
  readAt?: string
  respondedAt?: string
  createdBy?: string
  approvedAt?: string
  rejectedAt?: string
  user?: UserType
}

export type SalesInvoiceItem = {
  id: number
  salesInvoiceId: string
  itemId: string
  pricePerUnit: number
  quantity: number
  quantityUnit: string
  isLargeUnit: boolean
  largeUnitQuantity: number
  smallQuantity: number
  subTotalAmount: number
  taxType: string
  taxId?: string
  taxPercentage: number
  taxAmount: number
  discountType?: string
  discountValue?: number
  isDiscountAfterTax?: boolean
  subTotalDiscount: number
  totalAmount: number
  lcTotalAmount: number
  item?: ItemType
  tax?: TaxType
  note?: string
}

export type SalesInvoiceOtherExpense = {
  id: number
  salesInvoiceId: string
  accountId: string
  amount: number
  lcAmount: number
  note?: string
  account?: AccountType
  departmentId?: string
  department?: DepartmentOutlineType
  siteId?: string
  site?: SiteOutlineType
}

export type SalesInvoiceLog = {
  id: number
  salesInvoiceId: string
  userId?: string
  type: string
  status: string
  changes?: string
  createdAt: string
  user?: UserOutlineType
}

export type SalesInvoice = {
  id: string
  companyId: string
  parentCompanyId?: string
  departmentId?: string
  siteId?: string
  projectLabelId?: number
  customerId: string
  currencyId?: string
  receivableAccountId?: string
  salesDownPaymentAccountId?: string
  number: string
  subTotalAmount: number
  otherAmount: number
  discountType?: string
  discountValue?: number
  discountAmount: number
  totalAmount: number
  lcTotalAmount: number
  status: string
  note?: string
  exchangeRate: number
  approvalsCount: number
  documentUrl: string
  documentMimeType: string
  paymentTerms?: string
  paymentDueDays?: number
  paymentDueDate?: string
  invoiceDate?: string
  journalId?: string
  taxInvoiceDate?: string
  taxInvoiceNumber?: string
  createdAt: string
  updatedAt: string
  approvedAt?: string
  approvals?: SalesInvoiceApproval[]
  items?: SalesInvoiceItem[]
  otherExpense?: SalesInvoiceOtherExpense[]
  customer?: CustomerType
  department?: DepartmentOutlineType
  site?: SiteOutlineType
  projectLabel?: ProjectLabelType
  currency?: CurrenciesType
  receivableAccount?: AccountType
  salesDownPaymentAccount?: AccountType
  createdByUser?: UserOutlineType
}

export type CreateSalesInvoiceApprovalPayload = {
  userId: string
}

export type CreateSalesInvoiceItemPayload = {
  itemId: string
  quantity: number
  quantityUnit: string
  largeUnitQuantity?: number
  pricePerUnit: number
  taxType: string
  taxId?: string
  taxPercentage?: number
  discountType?: string
  discountValue?: number
  isDiscountAfterTax?: boolean
  note?: string
}

export type CreateSalesInvoiceOtherExpensePayload = {
  accountId: string
  amount: number
  note?: string
}

export type CreateSalesInvoicePayload = {
  customerId: string
  invoiceDate: string
  note?: string
  currencyId?: string
  receivableAccountId?: string
  exchangeRate?: number
  documentUploadId?: string
  approvals: CreateSalesInvoiceApprovalPayload[]
  items: CreateSalesInvoiceItemPayload[]
  otherExpenses?: CreateSalesInvoiceOtherExpensePayload[]
  discountType?: string
  discountValue?: number
  paymentTerms: string
  paymentDueDays?: number
  taxInvoiceNumber?: string
  taxInvoiceDate?: string
  departmentId?: string
  siteId?: string
  projectLabelId?: number
}

export type UpdateSalesInvoicePayload = {
  status?: string
  journalId?: string
}

export type UpdateSalesInvoiceApprovalPayload = {
  userId: string
  note?: string
}

export type UpdateSalesInvoiceApprovalStatusPayload = {
  status: string
  rejectionNote?: string
}

export type UpdateSalesInvoiceApprovalReadPayload = {
  isRead: boolean
}

export type SalesInvoiceParams = Omit<ListParams, 'siteIds'> & {
  search?: string
  status?: SalesInvoiceStatuses
  customerId?: string
  companyId?: string
  departmentId?: string
  siteIds?: string[]
  startDate?: string
  endDate?: string
  userStatus?: string
  sort?: string[]
}

export type SalesInvoiceApprovalPayload = {
  siId: string
  approvalId: number
  payload: UpdateSalesInvoiceApprovalPayload
}
