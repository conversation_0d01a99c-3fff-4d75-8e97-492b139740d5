import { CodeNameType } from './common'
import { UnitType } from './companyTypes'
import { ListParams } from './payload'
import { ApproverType, UserOutlineType } from './userTypes'
import { WorkOrderType, WoSegmentType } from './woTypes'

export type WpStatuses = 'PENDING' | 'IN_PROCESS' | 'IN_REVIEW' | 'APPROVED' | 'REJECTED'

export enum WorkProcessLogStatus {
  CREATED = 'CREATED',
  UPDATED = 'UPDATED',
  REPORT_CREATED = 'REPORT_CREATED',
  REPORT_UPDATED = 'REPORT_UPDATED',
  REPORT_DELETED = 'REPORT_DELETED',
  LABOR_SET = 'LABOR_SET',
  PROCESS_STARTED = 'PROCESS_STARTED',
  PROCESS_ENDED = 'PROCESS_ENDED',
  PROCESS_APPROVED = 'PROCESS_APPROVED',
  PROCESS_REJECTED = 'PROCESS_REJECTED',
  TIMER_STARTED = 'TIMER_STARTED',
  TIMER_ENDED = 'TIMER_ENDED',
  TAKE_STUFF_REQUESTED = 'TAKE_STUFF_REQUESTED',
  RETURN_STUFF_REQUESTED = 'RETURN_STUFF_REQUESTED'
}

export type WpParams = {
  workOrderSegmentId?: string
  workOrderId?: string
  status?: WpStatuses
} & ListParams

export type WpDto = {
  workProcessId: string
  reportId?: string
  note?: string
}

export type WpStartDto = {
  workProcessId: string
  laborUserIds: string[]
}

export type WpStartTimerDto = {
  workProcessId: string
  laborUserIds: string[]
}

export type WpReportDto = {
  name: string
  uploadId: string
  note: string
  images: { uploadId: string }[]
}

export type CreateWpDto = {
  workOrderId: string
  workOrderSegmentId: string
  workOrderSegmentIds: string[]
  laborIds: string[]
  estimatedEndedAt: string
  estimatedHourEndedAt?: string
}

export type LaborType = {
  WorkProcessLabor: {
    userId: string
    workProcessId: string
  }
} & UserOutlineType

export type WpSessionType = {
  id: number
  laborsCount: number
  durationInSec: number
  note: string | null
  startedAt: string
  stoppedAt: string
  startedBy: string
  stoppedBy: string
  labors: UserOutlineType[]
}

export type WpType = {
  id: string
  companyId: string
  parentCompanyId: string
  departmentId: string
  siteId: string
  unitId: string
  priority: number
  workOrderId: string
  workOrderSegmentId: string
  number: string
  status: WpStatuses
  rejectReason: string
  durationInSec: number
  reportsCount: number
  laborsCount: number
  isEditable: boolean
  segmentsCount: number
  createdAt: string
  updatedAt: string
  timerStartedAt: string
  estimatedEndedAt: string
  startedAt: string
  endedAt: string
  approvedAt: string
  rejectedAt: string
  createdBy: string
  department: CodeNameType
  site: CodeNameType
  unit: UnitType
  workOrder: WorkOrderType
  workOrderSegment: WoSegmentType
  workOrderSegments: WoSegmentType[]
  labors: string[] | LaborType[]
  createdByUser: UserOutlineType
  sessions: WpSessionType[]
}

export type WpLogType = {
  id: number
  workProcessId: string
  workProcessReportId: string
  userId: string
  type: string
  status: string
  changes: string
  createdAt: string
  user: UserOutlineType
}

export type WpReportType = {
  id: string
  workProcessId: string
  name: string
  note: string
  reportUrl: string
  reportMimeType: string
  imagesCount: number
  createdAt: string
  updatedAt: string
  createdBy: string
  createdByUser: UserOutlineType
  images: { id: string; url: string }[]
}

export type StuffRequestParams = {
  status?: 'PROCESSED' | 'APPROVED' | 'REJECTED' | 'CANCELED'
  type?: 'TAKE' | 'RETURN'
  workProcessId?: string
} & ListParams

export type StuffRequestItem = {
  id: number
  stuffRequestId: string
  itemId: string
  serialNumberId: string | null
  quantity: number
  quantityUnit: string
  isLargeUnit: boolean
  largeUnitQuantity: number
  smallQuantity: number
  returnedQuantity: number
  note: string
  workOrderSegmentId: string
  createdAt: string
  updatedAt: string
  item: {
    id: string
    number: string
    name: string
    brandName: string
    largeUnit: string
    smallUnit: string
    largeUnitQuantity: number
    parentCode: string
    stocks: Array<{
      id: number
      siteId: string
      stock: number
      totalStock: number
    }>
  }
  workOrderSegment: {
    id: string
    number: string
    status: string
  }
}

export type StuffRequestType = {
  id: string
  type: string
  number: string
  itemsCount: 0
  approvalsCount: 0
  note: string
  doneAt: string
  doneBy: string
  imageProofUrl: string
  status: string
  companyId: string
  parentCompanyId: string
  departmentId: string
  siteId: string
  journalId: string
  createdAt: string
  updatedAt: string
  createdBy: string
  workProcess: WpType
  workOrder: WorkOrderType
  items: StuffRequestItem[]
  approvals: ApproverType[]
  department: CodeNameType
  site: CodeNameType
  createdByUser: UserOutlineType
}

export type UpdateApprovalStuffRequestPayload = {
  stuffId?: string
  approvalId?: number
  status: 'APPROVED' | 'REJECTED'
  rejectionNote?: string
  doneBy?: string
  imageProofUploadId?: string
}
