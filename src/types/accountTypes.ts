import { ReconciliationType } from '@/pages/cash-bank/reconsiliation/config/types'
import { CodeNameType } from './common'
import { ListParams } from './payload'
import { ProjectLabelType, ProjectType } from './projectTypes'
import { UserOutlineType } from './userTypes'
import { DepartmentType, SiteType, UnitType } from './companyTypes'
import { CurrenciesType } from './currenciesTypes'

export type AccountParams = {
  accountTypeIds?: string | string[]
  companyId?: string
  level?: number
  status?: 'ACTIVE' | 'INACTIVE'
} & ListParams

export type JournalLineParams = {
  accountId: string
  isReconciliated?: boolean
  bankReconciliationId?: string
} & ListParams

export type AllGeneralLedgerParams = {
  periodId?: number
  projectLabelId?: string
  type?:
    | 'GENERAL'
    | 'SALES_TRANSACTION'
    | 'PURCHASE_TRANSACTION'
    | 'CASH_RECEIPT'
    | 'CASH_PAYMENT'
    | 'ADJUSTING_ENTRIES'
    | 'OTHER_TRANSACTION'
} & ListParams

export type GeneralLedgerParams = {
  isEndPeriodEntry?: boolean
  periodId?: number
} & ListParams

export type AccountType = {
  id: string
  companyId: string
  parentCompanyId: string
  accountTypeId: string
  accountType?: AccountMasterType
  parentId: string
  code: string
  name: string
  level: number
  balance: number
  note: string
  status: string
  balanceAt: string
  createdAt: string
  updatedAt: string
  parent: CodeNameType
}

export type AccountMasterType = {
  id: string
  companyId: string
  parentCompanyId: string
  code: string
  name: string
  createdAt: string
  updatedAt: string
}

export type GeneralLedgerLineType = {
  id: number
  journalId: string
  accountId: string
  debit: number
  credit: number
  description: string
  createdAt: string
  department: DepartmentType
  updatedAt: string
  createdBy: string
  account: AccountType
}

export type GeneralLedgerType = {
  id: string
  companyId: string
  parentCompanyId: string
  number: string
  type: string
  description: string
  totalDebit: number
  totalCredit: number
  transactionDate: string
  reference: string
  refDocId: string
  refDocNumber: string
  refDocType: string
  voucherNumber: string
  siteId: string
  projectId: string
  exchangeRate: number
  projectLabelId?: number
  currencyId?: string
  currency?: CurrenciesType
  createdAt: string
  updatedAt: string
  createdBy: string
  lines: GeneralLedgerLineType[]
  createdByUser: UserOutlineType
  site?: SiteType
  department?: DepartmentType
  projectLabel?: ProjectLabelType
  unitId?: string
  unit: UnitType
}

export type GeneralLedgerLogType = {
  id: number
  journalId: string
  userId: string
  status: string
  changes: string
  createdAt: string
  user: UserOutlineType
}

export type JournalLineType = {
  id: number
  journalId: string
  accountId: string
  debit: number
  credit: number
  description: string
  accountBalance: number
  voucherNumber: string
  bankReconciliationId: string
  bankReconciliationNote: string
  createdAt: string
  updatedAt: string
  createdBy: string
  account: AccountType
  journal: {
    id: string
    companyId: string
    parentCompanyId: string
    number: string
    type: string
    description: string
    linesCount: number
    totalDebit: number
    totalCredit: number
    transactionDate: string
    status: string
    refDocType: string
    refDocId: string
    refDocNumber: string
    voucherNumber: string
    departmentId: string
    siteId: string
    projectId: string
    projectLabelId: number
    createdAt: string
    updatedAt: string
    createdBy: string
    lines: string
    department: DepartmentType
    site: SiteType
    project: ProjectType
    createdByUser: UserOutlineType
  }
  bankReconciliation: ReconciliationType
}

export type PaymentTermsType = {
  id: string
  name: string
  discountDays: number
  discountPercent: number
  dueDays: number
  remarks: string
  isDefault: boolean
  companyId: string
  parentCompanyId: string
  createdAt: string
  updatedAt: string
}

export type ExchangeRateType = {
  id: number
  periodId: number
  currencyId: string
  value: number
  createdAt: string
  currency: CurrenciesType
}

export type EndPeriodType = {
  id: number
  number: string
  year: number
  month: number
  note: string
  status: string
  companyId: string
  journalId: string
  parentCompanyId: string
  createdAt: string
  exchangeRates: ExchangeRateType[]
  createdBy: string
  createdByUser: UserOutlineType
}

export type EndPeriodLogType = {
  id: number
  periodId: number
  userId: string
  status: string
  changes: string
  createdAt: string
  user: UserOutlineType
}
