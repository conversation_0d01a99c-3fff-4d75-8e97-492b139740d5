self.addEventListener('install', () => {
  // Activate updated SW immediately
  self.skipWaiting()
})

self.addEventListener('activate', event => {
  // Take control of all clients as soon as we're active
  event.waitUntil(self.clients.claim())
})

// Optional: pass-through fetch (no caching). Uncomment to debug network behavior.
// self.addEventListener('fetch', (event) => {
//   event.respondWith(fetch(event.request));
// });
