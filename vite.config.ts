import { sentryVitePlugin } from '@sentry/vite-plugin'
import path from 'path'
import react from '@vitejs/plugin-react' // Switched from react-swc to avoid segfault
import { defineConfig } from 'vite'

export default defineConfig({
  plugins: [
    react({
      babel: {
        compact: true,
        // Only include essential babel plugins for faster compilation
        plugins: []
      }
    }),
    // Only include Sentry plugin in production builds
    ...(process.env.NODE_ENV === 'production'
      ? [
          sentryVitePlugin({
            org: 'equalindo360',
            project: 'javascript-react',
            // Optimize Sentry plugin for faster builds
            silent: true,
            debug: false,
            sourceMaps: {
              include: ['./dist'],
              ignore: ['node_modules']
            }
          })
        ]
      : [])
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src')
    }
  },
  server: {
    port: 5000,
    // Enable faster HMR
    hmr: {
      overlay: false // Disable error overlay for faster dev experience
    }
  },

  // Optimized dependency handling
  optimizeDeps: {
    // Include commonly used dependencies for faster initial load
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      '@mui/material',
      '@emotion/react',
      '@emotion/styled',
      'axios',
      '@tanstack/react-query'
    ],
    // Remove the exclude array - it was counterproductive
    force: false // Only force when dependencies change
  },

  build: {
    // Faster minification
    minify: 'esbuild', // Much faster than terser

    // Disable source maps in production for faster builds (enable only when needed)
    sourcemap: process.env.NODE_ENV === 'development',

    // Target modern browsers for smaller bundles
    target: 'esnext',

    // Optimize chunk splitting
    rollupOptions: {
      output: {
        // Manual chunking for better caching
        manualChunks: {
          // Separate vendor libraries
          vendor: ['react', 'react-dom'],
          router: ['react-router-dom'],
          ui: ['@mui/material', '@mui/lab', '@emotion/react', '@emotion/styled'],
          query: ['@tanstack/react-query'],
          utils: ['axios', 'date-fns', 'zod'],
          charts: ['@nivo/bar', '@nivo/line', '@nivo/pie']
        }
      },
      // Reduce bundle analysis time
      treeshake: {
        preset: 'recommended'
      }
    },

    // Increase chunk size warning limit
    chunkSizeWarningLimit: 1000,

    // Optimize CSS
    cssCodeSplit: true,

    // Faster builds by reducing work
    reportCompressedSize: false
  },

  // Performance optimizations
  define: {
    // Remove development-only code in production
    __DEV__: process.env.NODE_ENV === 'development'
  },

  // Faster CSS processing
  css: {
    devSourcemap: true,
    preprocessorOptions: {
      // Add any CSS preprocessor optimizations here if needed
    }
  }
})
